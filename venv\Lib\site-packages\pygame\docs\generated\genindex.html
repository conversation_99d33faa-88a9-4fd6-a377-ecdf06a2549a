<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Index &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/pygame.css?v=a854c6a8" />
    <script src="_static/documentation_options.js?v=0a414f3d"></script>
    <script src="_static/doctools.js?v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="_static/pygame.ico"/>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="#" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Index</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
          <div class="body" role="main">
            

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#_"><strong>_</strong></a>
 | <a href="#A"><strong>A</strong></a>
 | <a href="#B"><strong>B</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#E"><strong>E</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#H"><strong>H</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#J"><strong>J</strong></a>
 | <a href="#K"><strong>K</strong></a>
 | <a href="#L"><strong>L</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#N"><strong>N</strong></a>
 | <a href="#O"><strong>O</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#Q"><strong>Q</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#U"><strong>U</strong></a>
 | <a href="#V"><strong>V</strong></a>
 | <a href="#W"><strong>W</strong></a>
 
</div>
<h2 id="_">_</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/event.html#pygame.event.Event.__dict__">__dict__ (pygame.event.Event attribute)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/surface.html#pygame.Surface._pixels_address">_pixels_address (pygame.Surface attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/color.html#pygame.Color.a">a (pygame.Color attribute)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.aacircle">aacircle() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.aaellipse">aaellipse() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/draw.html#pygame.draw.aaline">aaline() (in module pygame.draw)</a>
</li>
      <li><a href="ref/draw.html#pygame.draw.aalines">aalines() (in module pygame.draw)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.aapolygon">aapolygon() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.aatrigon">aatrigon() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.Output.abort">abort() (pygame.midi.Output method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.Group.add">add() (pygame.sprite.Group method)</a>

      <ul>
        <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.add">(pygame.sprite.LayeredUpdates method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.Sprite.add">(pygame.sprite.Sprite method)</a>
</li>
      </ul></li>
      <li><a href="ref/examples.html#pygame.examples.aliens.main">aliens.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.Sprite.alive">alive() (pygame.sprite.Sprite method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.alpha">alpha (pygame._sdl2.video.Image attribute)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.alpha">(pygame._sdl2.video.Texture attribute)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.angle">angle (pygame._sdl2.video.Image attribute)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.angle">angle() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.angle_to">angle_to() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.angle_to">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/freetype.html#pygame.freetype.Font.antialiased">antialiased (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/draw.html#pygame.draw.arc">arc() (in module pygame.draw)</a>

      <ul>
        <li><a href="ref/gfxdraw.html#pygame.gfxdraw.arc">(in module pygame.gfxdraw)</a>
</li>
      </ul></li>
      <li><a href="ref/sndarray.html#pygame.sndarray.array">array() (in module pygame.sndarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.array2d">array2d() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.array3d">array3d() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.array_alpha">array_alpha() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.array_blue">array_blue() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.array_colorkey">array_colorkey() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.array_green">array_green() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.array_red">array_red() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/pixelcopy.html#pygame.pixelcopy.array_to_surface">array_to_surface() (in module pygame.pixelcopy)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.arraydemo.main">arraydemo.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.as_joystick">as_joystick() (pygame._sdl2.controller.Controller method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.as_polar">as_polar() (pygame.math.Vector2 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.as_spherical">as_spherical() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.ascender">ascender (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.attached">attached() (pygame._sdl2.controller.Controller method)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.average_color">average_color() (in module pygame.transform)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.average_surfaces">average_surfaces() (in module pygame.transform)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/color.html#pygame.Color.b">b (pygame.Color attribute)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.bezier">bezier() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.bgcolor">bgcolor (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.blend_fill.main">blend_fill.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.blend_mode">blend_mode (pygame._sdl2.video.Image attribute)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.blend_mode">(pygame._sdl2.video.Texture attribute)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.blit">blit() (pygame._sdl2.video.Renderer method)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.blit">(pygame.Surface method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/surfarray.html#pygame.surfarray.blit_array">blit_array() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.blit_blends.main">blit_blends.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.blits">blits() (pygame.Surface method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.bold">bold (pygame.font.Font attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.borderless">borderless (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.box">box() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/bufferproxy.html#pygame.BufferProxy">BufferProxy (class in pygame)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/camera.html#pygame.camera.Camera">Camera (class in pygame.camera)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.camera.main">camera.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD">CD (class in pygame.cdrom)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.centroid">centroid() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty.change_layer">change_layer() (pygame.sprite.LayeredDirty method)</a>

      <ul>
        <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.change_layer">(pygame.sprite.LayeredUpdates method)</a>
</li>
      </ul></li>
      <li><a href="ref/mixer.html#pygame.mixer.Channel">Channel (class in pygame.mixer)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.chimp.main">chimp.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.chop">chop() (in module pygame.transform)</a>
</li>
      <li><a href="ref/draw.html#pygame.draw.circle">circle() (in module pygame.draw)</a>

      <ul>
        <li><a href="ref/gfxdraw.html#pygame.gfxdraw.circle">(in module pygame.gfxdraw)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.clamp">clamp() (in module pygame.math)</a>

      <ul>
        <li><a href="ref/rect.html#pygame.Rect.clamp">(pygame.Rect method)</a>
</li>
      </ul></li>
      <li><a href="ref/rect.html#pygame.Rect.clamp_ip">clamp_ip() (pygame.Rect method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.clamp_magnitude">clamp_magnitude() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.clamp_magnitude">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.clamp_magnitude_ip">clamp_magnitude_ip() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.clamp_magnitude_ip">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/event.html#pygame.event.clear">clear() (in module pygame.event)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.clear">(pygame._sdl2.video.Renderer method)</a>
</li>
        <li><a href="ref/mask.html#pygame.mask.Mask.clear">(pygame.mask.Mask method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.Group.clear">(pygame.sprite.Group method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty.clear">(pygame.sprite.LayeredDirty method)</a>
</li>
      </ul></li>
      <li><a href="ref/rect.html#pygame.Rect.clip">clip() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.clipline">clipline() (pygame.Rect method)</a>
</li>
      <li><a href="ref/time.html#pygame.time.Clock">Clock (class in pygame.time)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.Input.close">close() (pygame.midi.Input method)</a>

      <ul>
        <li><a href="ref/midi.html#pygame.midi.Output.close">(pygame.midi.Output method)</a>
</li>
        <li><a href="ref/pixelarray.html#pygame.PixelArray.close">(pygame.PixelArray method)</a>
</li>
      </ul></li>
      <li><a href="ref/color.html#pygame.Color.cmy">cmy (pygame.Color attribute)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.collide_circle">collide_circle() (in module pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.collide_circle_ratio">collide_circle_ratio() (in module pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.collide_mask">collide_mask() (in module pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.collide_rect">collide_rect() (in module pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.collide_rect_ratio">collide_rect_ratio() (in module pygame.sprite)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/rect.html#pygame.Rect.collidedict">collidedict() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.collidedictall">collidedictall() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.collidelist">collidelist() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.collidelistall">collidelistall() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.collideobjects">collideobjects() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.collideobjectsall">collideobjectsall() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.collidepoint">collidepoint() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.colliderect">colliderect() (pygame.Rect method)</a>
</li>
      <li><a href="ref/color.html#pygame.Color">Color (class in pygame)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.color">color (pygame._sdl2.video.Image attribute)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.color">(pygame._sdl2.video.Texture attribute)</a>
</li>
      </ul></li>
      <li><a href="ref/camera.html#pygame.camera.colorspace">colorspace() (in module pygame.camera)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.compare">compare() (pygame.PixelArray method)</a>
</li>
      <li><a href="ref/cursors.html#pygame.cursors.compile">compile() (in module pygame.cursors)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.connected_component">connected_component() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.connected_components">connected_components() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/scrap.html#pygame.scrap.contains">contains() (in module pygame.scrap)</a>

      <ul>
        <li><a href="ref/rect.html#pygame.Rect.contains">(pygame.Rect method)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller">Controller (class in pygame._sdl2.controller)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.convert">convert() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.convert_alpha">convert_alpha() (pygame.Surface method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.convolve">convolve() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/cursors.html#pygame.cursors.Cursor.copy">copy() (pygame.cursors.Cursor method)</a>

      <ul>
        <li><a href="ref/mask.html#pygame.mask.Mask.copy">(pygame.mask.Mask method)</a>
</li>
        <li><a href="ref/math.html#pygame.math.Vector2.copy">(pygame.math.Vector2 method)</a>
</li>
        <li><a href="ref/math.html#pygame.math.Vector3.copy">(pygame.math.Vector3 method)</a>
</li>
        <li><a href="ref/rect.html#pygame.Rect.copy">(pygame.Rect method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.Group.copy">(pygame.sprite.Group method)</a>
</li>
        <li><a href="ref/surface.html#pygame.Surface.copy">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/color.html#pygame.Color.correct_gamma">correct_gamma() (pygame.Color method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.count">count() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.cross">cross() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.cross">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/cursors.html#pygame.cursors.Cursor">Cursor (class in pygame.cursors)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.cursors.main">cursors.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/event.html#pygame.event.custom_type">custom_type() (in module pygame.event)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/cursors.html#pygame.cursors.Cursor.data">data (pygame.cursors.Cursor attribute)</a>
</li>
      <li><a href="ref/time.html#pygame.time.delay">delay() (in module pygame.time)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.descender">descender (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.destroy">destroy() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.DirtySprite">DirtySprite (class in pygame.sprite)</a>
</li>
      <li><a href="ref/overlay.html#pygame.Overlay.display">display() (pygame.Overlay method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.display_index">display_index (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.distance_squared_to">distance_squared_to() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.distance_squared_to">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.distance_to">distance_to() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.distance_to">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.dot">dot() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.dot">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.draw">draw() (pygame._sdl2.video.Image method)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.draw">(pygame._sdl2.video.Texture method)</a>
</li>
        <li><a href="ref/mask.html#pygame.mask.Mask.draw">(pygame.mask.Mask method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.Group.draw">(pygame.sprite.Group method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty.draw">(pygame.sprite.LayeredDirty method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.draw">(pygame.sprite.LayeredUpdates method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.RenderUpdates.draw">(pygame.sprite.RenderUpdates method)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.draw_blend_mode">draw_blend_mode (pygame._sdl2.video.Renderer attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.draw_color">draw_color (pygame._sdl2.video.Renderer attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.draw_line">draw_line() (pygame._sdl2.video.Renderer method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.draw_point">draw_point() (pygame._sdl2.video.Renderer method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.draw_rect">draw_rect() (pygame._sdl2.video.Renderer method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.eject">eject() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.elementwise">elementwise() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.elementwise">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/draw.html#pygame.draw.ellipse">ellipse() (in module pygame.draw)</a>

      <ul>
        <li><a href="ref/gfxdraw.html#pygame.gfxdraw.ellipse">(in module pygame.gfxdraw)</a>
</li>
      </ul></li>
      <li><a href="ref/sprite.html#pygame.sprite.Group.empty">empty() (pygame.sprite.Group method)</a>
</li>
      <li><a href="ref/pygame.html#pygame.encode_file_path">encode_file_path() (in module pygame)</a>
</li>
      <li><a href="ref/pygame.html#pygame.encode_string">encode_string() (in module pygame)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/math.html#pygame.math.Vector2.epsilon">epsilon (pygame.math.Vector2 attribute)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.epsilon">(pygame.math.Vector3 attribute)</a>
</li>
      </ul></li>
      <li><a href="ref/mask.html#pygame.mask.Mask.erase">erase() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/pygame.html#pygame.error">error</a>
</li>
      <li><a href="ref/event.html#pygame.event.Event">Event (class in pygame.event)</a>
</li>
      <li><a href="ref/event.html#pygame.event.event_name">event_name() (in module pygame.event)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.eventlist.main">eventlist.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.extract">extract() (pygame.PixelArray method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/mixer.html#pygame.mixer.fadeout">fadeout() (in module pygame.mixer)</a>

      <ul>
        <li><a href="ref/music.html#pygame.mixer.music.fadeout">(in module pygame.mixer.music)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.fadeout">(pygame.mixer.Channel method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Sound.fadeout">(pygame.mixer.Sound method)</a>
</li>
      </ul></li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.fgcolor">fgcolor (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.fill">fill() (pygame.mask.Mask method)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.fill">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.fill_rect">fill_rect() (pygame._sdl2.video.Renderer method)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.filled_circle">filled_circle() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.filled_ellipse">filled_ellipse() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.filled_polygon">filled_polygon() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.filled_trigon">filled_trigon() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.find_channel">find_channel() (in module pygame.mixer)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.fit">fit() (pygame.Rect method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.fixed_sizes">fixed_sizes (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.fixed_width">fixed_width (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/display.html#pygame.display.flip">flip() (in module pygame.display)</a>

      <ul>
        <li><a href="ref/transform.html#pygame.transform.flip">(in module pygame.transform)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.flip_x">flip_x (pygame._sdl2.video.Image attribute)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.flip_y">flip_y (pygame._sdl2.video.Image attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.focus">focus() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font">Font (class in pygame.font)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.Font">(class in pygame.freetype)</a>
</li>
      </ul></li>
      <li><a href="ref/examples.html#pygame.examples.fonty.main">fonty.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.freetype_misc.main">freetype_misc.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.frequency_to_midi">frequency_to_midi() (in module pygame.midi)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.from_display_module">from_display_module() (pygame._sdl2.video.Window class method)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.from_joystick">from_joystick() (pygame._sdl2.controller.Controller static method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.from_polar">from_polar() (pygame.math.Vector2 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.from_spherical">from_spherical() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.from_surface">from_surface() (in module pygame.mask)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.from_surface">(pygame._sdl2.video.Texture static method)</a>
</li>
      </ul></li>
      <li><a href="ref/mask.html#pygame.mask.from_threshold">from_threshold() (in module pygame.mask)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.from_window">from_window() (pygame._sdl2.video.Renderer class method)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.from_window">(pygame._sdl2.video.Window class method)</a>
</li>
      </ul></li>
      <li><a href="ref/image.html#pygame.image.frombuffer">frombuffer() (in module pygame.image)</a>
</li>
      <li><a href="ref/image.html#pygame.image.frombytes">frombytes() (in module pygame.image)</a>
</li>
      <li><a href="ref/image.html#pygame.image.fromstring">fromstring() (in module pygame.image)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/color.html#pygame.Color.g">g (pygame.Color attribute)</a>
</li>
      <li><a href="ref/event.html#pygame.event.get">get() (in module pygame.event)</a>

      <ul>
        <li><a href="ref/fastevent.html#pygame.fastevent.get">(in module pygame.fastevent)</a>
</li>
        <li><a href="ref/scrap.html#pygame.scrap.get">(in module pygame.scrap)</a>
</li>
      </ul></li>
      <li><a href="ref/surface.html#pygame.Surface.get_abs_offset">get_abs_offset() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_abs_parent">get_abs_parent() (pygame.Surface method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_active">get_active() (in module pygame.display)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_all">get_all() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_allow_screensaver">get_allow_screensaver() (in module pygame.display)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_alpha">get_alpha() (pygame.Surface method)</a>
</li>
      <li><a href="ref/sndarray.html#pygame.sndarray.get_arraytype">get_arraytype() (in module pygame.sndarray)</a>

      <ul>
        <li><a href="ref/surfarray.html#pygame.surfarray.get_arraytype">(in module pygame.surfarray)</a>
</li>
      </ul></li>
      <li><a href="ref/sndarray.html#pygame.sndarray.get_arraytypes">get_arraytypes() (in module pygame.sndarray)</a>

      <ul>
        <li><a href="ref/surfarray.html#pygame.surfarray.get_arraytypes">(in module pygame.surfarray)</a>
</li>
      </ul></li>
      <li><a href="ref/font.html#pygame.font.Font.get_ascent">get_ascent() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.get_at">get_at() (pygame.mask.Mask method)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.get_at">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/surface.html#pygame.Surface.get_at_mapped">get_at_mapped() (pygame.Surface method)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.get_axis">get_axis() (pygame._sdl2.controller.Controller method)</a>

      <ul>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_axis">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
      <li><a href="ref/camera.html#pygame.camera.get_backends">get_backends() (in module pygame.camera)</a>
</li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_ball">get_ball() (pygame.joystick.Joystick method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_bitsize">get_bitsize() (pygame.Surface method)</a>
</li>
      <li><a href="ref/event.html#pygame.event.get_blocked">get_blocked() (in module pygame.event)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.get_bold">get_bold() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.get_bottom_layer">get_bottom_layer() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_bounding_rect">get_bounding_rect() (pygame.Surface method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.get_bounding_rects">get_bounding_rects() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_buffer">get_buffer() (pygame.Surface method)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.get_busy">get_busy() (in module pygame.mixer)</a>

      <ul>
        <li><a href="ref/music.html#pygame.mixer.music.get_busy">(in module pygame.mixer.music)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_busy">(pygame.cdrom.CD method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.get_busy">(pygame.mixer.Channel method)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.get_button">get_button() (pygame._sdl2.controller.Controller method)</a>

      <ul>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_button">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
      <li><a href="ref/surface.html#pygame.Surface.get_bytesize">get_bytesize() (pygame.Surface method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.get_cache_size">get_cache_size() (in module pygame.freetype)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_caption">get_caption() (in module pygame.display)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty.get_clip">get_clip() (pygame.sprite.LayeredDirty method)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.get_clip">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/surface.html#pygame.Surface.get_colorkey">get_colorkey() (pygame.Surface method)</a>
</li>
      <li><a href="ref/camera.html#pygame.camera.Camera.get_controls">get_controls() (pygame.camera.Camera method)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.get_count">get_count() (in module pygame._sdl2.controller)</a>

      <ul>
        <li><a href="ref/cdrom.html#pygame.cdrom.get_count">(in module pygame.cdrom)</a>
</li>
        <li><a href="ref/joystick.html#pygame.joystick.get_count">(in module pygame.joystick)</a>
</li>
        <li><a href="ref/midi.html#pygame.midi.get_count">(in module pygame.midi)</a>
</li>
      </ul></li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_current">get_current() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/mouse.html#pygame.mouse.get_cursor">get_cursor() (in module pygame.mouse)</a>
</li>
      <li><a href="ref/font.html#pygame.font.get_default_font">get_default_font() (in module pygame.font)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.get_default_font">(in module pygame.freetype)</a>
</li>
      </ul></li>
      <li><a href="ref/midi.html#pygame.midi.get_default_input_id">get_default_input_id() (in module pygame.midi)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.get_default_output_id">get_default_output_id() (in module pygame.midi)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.get_default_resolution">get_default_resolution() (in module pygame.freetype)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.get_descent">get_descent() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_desktop_sizes">get_desktop_sizes() (in module pygame.display)</a>
</li>
      <li><a href="ref/touch.html#pygame._sdl2.touch.get_device">get_device() (in module pygame._sdl2.touch)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.get_device_info">get_device_info() (in module pygame.midi)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_driver">get_driver() (in module pygame.display)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_empty">get_empty() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.get_endevent">get_endevent() (in module pygame.mixer.music)</a>

      <ul>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.get_endevent">(pygame.mixer.Channel method)</a>
</li>
      </ul></li>
      <li><a href="ref/pygame.html#pygame.get_error">get_error() (in module pygame)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.get_error">(in module pygame.freetype)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.get_eventstate">get_eventstate() (in module pygame._sdl2.controller)</a>
</li>
      <li><a href="ref/image.html#pygame.image.get_extended">get_extended() (in module pygame.image)</a>
</li>
      <li><a href="ref/touch.html#pygame._sdl2.touch.get_finger">get_finger() (in module pygame._sdl2.touch)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_flags">get_flags() (pygame.Surface method)</a>
</li>
      <li><a href="ref/key.html#pygame.key.get_focused">get_focused() (in module pygame.key)</a>

      <ul>
        <li><a href="ref/mouse.html#pygame.mouse.get_focused">(in module pygame.mouse)</a>
</li>
      </ul></li>
      <li><a href="ref/font.html#pygame.font.get_fonts">get_fonts() (in module pygame.font)</a>
</li>
      <li><a href="ref/time.html#pygame.time.Clock.get_fps">get_fps() (pygame.time.Clock method)</a>
</li>
      <li><a href="ref/event.html#pygame.event.get_grab">get_grab() (in module pygame.event)</a>
</li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_guid">get_guid() (pygame.joystick.Joystick method)</a>
</li>
      <li><a href="ref/overlay.html#pygame.Overlay.get_hardware">get_hardware() (pygame.Overlay method)</a>
</li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_hat">get_hat() (pygame.joystick.Joystick method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.get_height">get_height() (pygame.font.Font method)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.get_height">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_id">get_id() (pygame.cdrom.CD method)</a>

      <ul>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_id">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
      <li><a href="ref/camera.html#pygame.camera.Camera.get_image">get_image() (pygame.camera.Camera method)</a>
</li>
      <li><a href="ref/pygame.html#pygame.get_init">get_init() (in module pygame)</a>

      <ul>
        <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.get_init">(in module pygame._sdl2.controller)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.get_init">(in module pygame.cdrom)</a>
</li>
        <li><a href="ref/display.html#pygame.display.get_init">(in module pygame.display)</a>
</li>
        <li><a href="ref/fastevent.html#pygame.fastevent.get_init">(in module pygame.fastevent)</a>
</li>
        <li><a href="ref/font.html#pygame.font.get_init">(in module pygame.font)</a>
</li>
        <li><a href="ref/freetype.html#pygame.freetype.get_init">(in module pygame.freetype)</a>
</li>
        <li><a href="ref/joystick.html#pygame.joystick.get_init">(in module pygame.joystick)</a>
</li>
        <li><a href="ref/midi.html#pygame.midi.get_init">(in module pygame.midi)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.get_init">(in module pygame.mixer)</a>
</li>
        <li><a href="ref/scrap.html#pygame.scrap.get_init">(in module pygame.scrap)</a>
</li>
        <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.get_init">(pygame._sdl2.controller.Controller method)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_init">(pygame.cdrom.CD method)</a>
</li>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_init">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_instance_id">get_instance_id() (pygame.joystick.Joystick method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/font.html#pygame.font.Font.get_italic">get_italic() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/event.html#pygame.event.get_keyboard_grab">get_keyboard_grab() (in module pygame.event)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.get_layer_of_sprite">get_layer_of_sprite() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.Sound.get_length">get_length() (pygame.mixer.Sound method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.get_linesize">get_linesize() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_locked">get_locked() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_locks">get_locks() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_losses">get_losses() (pygame.Surface method)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.get_mapping">get_mapping() (pygame._sdl2.controller.Controller method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_masks">get_masks() (pygame.Surface method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.get_metrics">get_metrics() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/key.html#pygame.key.get_mods">get_mods() (in module pygame.key)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_name">get_name() (pygame.cdrom.CD method)</a>

      <ul>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_name">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
      <li><a href="ref/mixer.html#pygame.mixer.get_num_channels">get_num_channels() (in module pygame.mixer)</a>

      <ul>
        <li><a href="ref/mixer.html#pygame.mixer.Sound.get_num_channels">(pygame.mixer.Sound method)</a>
</li>
      </ul></li>
      <li><a href="ref/touch.html#pygame._sdl2.touch.get_num_devices">get_num_devices() (in module pygame._sdl2.touch)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_num_displays">get_num_displays() (in module pygame.display)</a>
</li>
      <li><a href="ref/touch.html#pygame._sdl2.touch.get_num_fingers">get_num_fingers() (in module pygame._sdl2.touch)</a>
</li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_numaxes">get_numaxes() (pygame.joystick.Joystick method)</a>
</li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_numballs">get_numballs() (pygame.joystick.Joystick method)</a>
</li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_numbuttons">get_numbuttons() (pygame.joystick.Joystick method)</a>
</li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_numhats">get_numhats() (pygame.joystick.Joystick method)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_numtracks">get_numtracks() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_offset">get_offset() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_palette">get_palette() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_palette_at">get_palette_at() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_parent">get_parent() (pygame.Surface method)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_paused">get_paused() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_pitch">get_pitch() (pygame.Surface method)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.get_pos">get_pos() (in module pygame.mixer.music)</a>

      <ul>
        <li><a href="ref/mouse.html#pygame.mouse.get_pos">(in module pygame.mouse)</a>
</li>
      </ul></li>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick.get_power_level">get_power_level() (pygame.joystick.Joystick method)</a>
</li>
      <li><a href="ref/key.html#pygame.key.get_pressed">get_pressed() (in module pygame.key)</a>

      <ul>
        <li><a href="ref/mouse.html#pygame.mouse.get_pressed">(in module pygame.mouse)</a>
</li>
      </ul></li>
      <li><a href="ref/mixer.html#pygame.mixer.Channel.get_queue">get_queue() (pygame.mixer.Channel method)</a>
</li>
      <li><a href="ref/camera.html#pygame.camera.Camera.get_raw">get_raw() (pygame.camera.Camera method)</a>

      <ul>
        <li><a href="ref/mixer.html#pygame.mixer.Sound.get_raw">(pygame.mixer.Sound method)</a>
</li>
      </ul></li>
      <li><a href="ref/time.html#pygame.time.Clock.get_rawtime">get_rawtime() (pygame.time.Clock method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.get_rect">get_rect() (pygame._sdl2.video.Image method)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.get_rect">(pygame._sdl2.video.Texture method)</a>
</li>
        <li><a href="ref/freetype.html#pygame.freetype.Font.get_rect">(pygame.freetype.Font method)</a>
</li>
        <li><a href="ref/mask.html#pygame.mask.Mask.get_rect">(pygame.mask.Mask method)</a>
</li>
        <li><a href="ref/surface.html#pygame.Surface.get_rect">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/mouse.html#pygame.mouse.get_rel">get_rel() (in module pygame.mouse)</a>
</li>
      <li><a href="ref/key.html#pygame.key.get_repeat">get_repeat() (in module pygame.key)</a>
</li>
      <li><a href="ref/pygame.html#pygame.get_sdl_byteorder">get_sdl_byteorder() (in module pygame)</a>
</li>
      <li><a href="ref/image.html#pygame.image.get_sdl_image_version">get_sdl_image_version() (in module pygame.image)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.get_sdl_mixer_version">get_sdl_mixer_version() (in module pygame.mixer)</a>
</li>
      <li><a href="ref/font.html#pygame.font.get_sdl_ttf_version">get_sdl_ttf_version() (in module pygame.font)</a>
</li>
      <li><a href="ref/pygame.html#pygame.get_sdl_version">get_sdl_version() (in module pygame)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_shifts">get_shifts() (pygame.Surface method)</a>
</li>
      <li><a href="ref/camera.html#pygame.camera.Camera.get_size">get_size() (pygame.camera.Camera method)</a>

      <ul>
        <li><a href="ref/mask.html#pygame.mask.Mask.get_size">(pygame.mask.Mask method)</a>
</li>
        <li><a href="ref/surface.html#pygame.Surface.get_size">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.get_sized_ascender">get_sized_ascender() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.get_sized_descender">get_sized_descender() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.get_sized_glyph_height">get_sized_glyph_height() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.get_sized_height">get_sized_height() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.get_sizes">get_sizes() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.get_smoothscale_backend">get_smoothscale_backend() (in module pygame.transform)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.Channel.get_sound">get_sound() (pygame.mixer.Channel method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.get_sprite">get_sprite() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.get_sprites_at">get_sprites_at() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.get_sprites_from_layer">get_sprites_from_layer() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.get_strikethrough">get_strikethrough() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_surface">get_surface() (in module pygame.display)</a>
</li>
      <li><a href="ref/time.html#pygame.time.get_ticks">get_ticks() (in module pygame.time)</a>
</li>
      <li><a href="ref/time.html#pygame.time.Clock.get_time">get_time() (pygame.time.Clock method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.get_top_layer">get_top_layer() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.get_top_sprite">get_top_sprite() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_track_audio">get_track_audio() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_track_length">get_track_length() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.get_track_start">get_track_start() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/scrap.html#pygame.scrap.get_types">get_types() (in module pygame.scrap)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.get_underline">get_underline() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.get_version">get_version() (in module pygame.freetype)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.get_view">get_view() (pygame.Surface method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.get_viewport">get_viewport() (pygame._sdl2.video.Renderer method)</a>
</li>
      <li><a href="ref/mouse.html#pygame.mouse.get_visible">get_visible() (in module pygame.mouse)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.get_volume">get_volume() (in module pygame.mixer.music)</a>

      <ul>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.get_volume">(pygame.mixer.Channel method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Sound.get_volume">(pygame.mixer.Sound method)</a>
</li>
      </ul></li>
      <li><a href="ref/surface.html#pygame.Surface.get_width">get_width() (pygame.Surface method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_window_size">get_window_size() (in module pygame.display)</a>
</li>
      <li><a href="ref/display.html#pygame.display.get_wm_info">get_wm_info() (in module pygame.display)</a>
</li>
      <li><a href="ref/display.html#pygame.display.gl_get_attribute">gl_get_attribute() (in module pygame.display)</a>
</li>
      <li><a href="ref/display.html#pygame.display.gl_set_attribute">gl_set_attribute() (in module pygame.display)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.glcube.main">glcube.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.grab">grab (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.grayscale">grayscale() (in module pygame.transform)</a>

      <ul>
        <li><a href="ref/color.html#pygame.Color.grayscale">(pygame.Color method)</a>
</li>
      </ul></li>
      <li><a href="ref/sprite.html#pygame.sprite.Group">Group (class in pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.groupcollide">groupcollide() (in module pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.Sprite.groups">groups() (pygame.sprite.Sprite method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.GroupSingle">GroupSingle() (in module pygame.sprite)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/sprite.html#pygame.sprite.Group.has">has() (pygame.sprite.Group method)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.headless_no_windows_needed.main">headless_no_windows_needed.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.height">height (pygame._sdl2.video.Texture attribute)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.Font.height">(pygame.freetype.Font attribute)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.hide">hide() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.hline">hline() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/color.html#pygame.Color.hsla">hsla (pygame.Color attribute)</a>
</li>
      <li><a href="ref/color.html#pygame.Color.hsva">hsva (pygame.Color attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/color.html#pygame.Color.i1i2i3">i1i2i3 (pygame.Color attribute)</a>
</li>
      <li><a href="ref/display.html#pygame.display.iconify">iconify() (in module pygame.display)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.id">id (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image">Image (class in pygame._sdl2.video)</a>
</li>
      <li><a href="c_api/base.html#c.import_pygame_base">import_pygame_base (C function)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.inflate">inflate() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.inflate_ip">inflate_ip() (pygame.Rect method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.Info">Info() (in module pygame.display)</a>
</li>
      <li><a href="ref/pygame.html#pygame.init">init() (in module pygame)</a>

      <ul>
        <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.init">(in module pygame._sdl2.controller)</a>
</li>
        <li><a href="ref/camera.html#pygame.camera.init">(in module pygame.camera)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.init">(in module pygame.cdrom)</a>
</li>
        <li><a href="ref/display.html#pygame.display.init">(in module pygame.display)</a>
</li>
        <li><a href="ref/fastevent.html#pygame.fastevent.init">(in module pygame.fastevent)</a>
</li>
        <li><a href="ref/font.html#pygame.font.init">(in module pygame.font)</a>
</li>
        <li><a href="ref/freetype.html#pygame.freetype.init">(in module pygame.freetype)</a>
</li>
        <li><a href="ref/joystick.html#pygame.joystick.init">(in module pygame.joystick)</a>
</li>
        <li><a href="ref/midi.html#pygame.midi.init">(in module pygame.midi)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.init">(in module pygame.mixer)</a>
</li>
        <li><a href="ref/scrap.html#pygame.scrap.init">(in module pygame.scrap)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.CD.init">(pygame.cdrom.CD method)</a>
</li>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.init">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/midi.html#pygame.midi.Input">Input (class in pygame.midi)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.invert">invert() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.is_controller">is_controller() (in module pygame._sdl2.controller)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.is_normalized">is_normalized() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.is_normalized">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/font.html#pygame.font.Font.italic">italic (pygame.font.Font attribute)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.itemsize">itemsize (pygame.PixelArray attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="J">J</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/joystick.html#pygame.joystick.Joystick">Joystick (class in pygame.joystick)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/examples.html#pygame.examples.joystick.main">joystick.main() (in module pygame.examples)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="K">K</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/freetype.html#pygame.freetype.Font.kerning">kerning (pygame.freetype.Font attribute)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/key.html#pygame.key.key_code">key_code() (in module pygame.key)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.Sprite.kill">kill() (pygame.sprite.Sprite method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="L">L</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/transform.html#pygame.transform.laplacian">laplacian() (in module pygame.transform)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty">LayeredDirty (class in pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates">LayeredUpdates (class in pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.layers">layers() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/bufferproxy.html#pygame.BufferProxy.length">length (pygame.BufferProxy attribute)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.length">length() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.length">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.length_squared">length_squared() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.length_squared">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.lerp">lerp() (in module pygame.math)</a>

      <ul>
        <li><a href="ref/color.html#pygame.Color.lerp">(pygame.Color method)</a>
</li>
        <li><a href="ref/math.html#pygame.math.Vector2.lerp">(pygame.math.Vector2 method)</a>
</li>
        <li><a href="ref/math.html#pygame.math.Vector3.lerp">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/draw.html#pygame.draw.line">line() (in module pygame.draw)</a>

      <ul>
        <li><a href="ref/gfxdraw.html#pygame.gfxdraw.line">(in module pygame.gfxdraw)</a>
</li>
      </ul></li>
      <li><a href="ref/draw.html#pygame.draw.lines">lines() (in module pygame.draw)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.liquid.main">liquid.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/camera.html#pygame.camera.list_cameras">list_cameras() (in module pygame.camera)</a>
</li>
      <li><a href="ref/display.html#pygame.display.list_modes">list_modes() (in module pygame.display)</a>
</li>
      <li><a href="ref/image.html#pygame.image.load">load() (in module pygame.image)</a>

      <ul>
        <li><a href="ref/music.html#pygame.mixer.music.load">(in module pygame.mixer.music)</a>
</li>
      </ul></li>
      <li><a href="ref/image.html#pygame.image.load_basic">load_basic() (in module pygame.image)</a>
</li>
      <li><a href="ref/image.html#pygame.image.load_extended">load_extended() (in module pygame.image)</a>
</li>
      <li><a href="ref/cursors.html#pygame.cursors.load_xbm">load_xbm() (in module pygame.cursors)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.lock">lock() (pygame.Surface method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.logical_size">logical_size (pygame._sdl2.video.Renderer attribute)</a>
</li>
      <li><a href="ref/scrap.html#pygame.scrap.lost">lost() (in module pygame.scrap)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/math.html#pygame.math.Vector2.magnitude">magnitude() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.magnitude">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.magnitude_squared">magnitude_squared() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.magnitude_squared">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/sndarray.html#pygame.sndarray.make_sound">make_sound() (in module pygame.sndarray)</a>
</li>
      <li><a href="ref/pixelcopy.html#pygame.pixelcopy.make_surface">make_surface() (in module pygame.pixelcopy)</a>

      <ul>
        <li><a href="ref/surfarray.html#pygame.surfarray.make_surface">(in module pygame.surfarray)</a>
</li>
        <li><a href="ref/pixelarray.html#pygame.PixelArray.make_surface">(pygame.PixelArray method)</a>
</li>
      </ul></li>
      <li><a href="ref/pixelcopy.html#pygame.pixelcopy.map_array">map_array() (in module pygame.pixelcopy)</a>

      <ul>
        <li><a href="ref/surfarray.html#pygame.surfarray.map_array">(in module pygame.surfarray)</a>
</li>
      </ul></li>
      <li><a href="ref/surface.html#pygame.Surface.map_rgb">map_rgb() (pygame.Surface method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask">Mask (class in pygame.mask)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.mask.main">mask.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/font.html#pygame.font.match_font">match_font() (in module pygame.font)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.maximize">maximize() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.metrics">metrics() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.midi.main">midi.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.midi_to_ansi_note">midi_to_ansi_note() (in module pygame.midi)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.midi_to_frequency">midi_to_frequency() (in module pygame.midi)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.MidiException">MidiException</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.midis2events">midis2events() (in module pygame.midi)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.minimize">minimize() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.mode_ok">mode_ok() (in module pygame.display)</a>
</li>
      <li>
    module

      <ul>
        <li><a href="ref/pygame.html#module-pygame">pygame</a>
</li>
        <li><a href="ref/sdl2_controller.html#module-pygame._sdl2.controller">pygame._sdl2.controller</a>
</li>
        <li><a href="ref/touch.html#module-pygame._sdl2.touch">pygame._sdl2.touch</a>
</li>
        <li><a href="ref/sdl2_video.html#module-pygame._sdl2.video">pygame._sdl2.video</a>
</li>
        <li><a href="ref/camera.html#module-pygame.camera">pygame.camera</a>
</li>
        <li><a href="ref/cdrom.html#module-pygame.cdrom">pygame.cdrom</a>
</li>
        <li><a href="ref/cursors.html#module-pygame.cursors">pygame.cursors</a>
</li>
        <li><a href="ref/display.html#module-pygame.display">pygame.display</a>
</li>
        <li><a href="ref/draw.html#module-pygame.draw">pygame.draw</a>
</li>
        <li><a href="ref/event.html#module-pygame.event">pygame.event</a>
</li>
        <li><a href="ref/examples.html#module-pygame.examples">pygame.examples</a>
</li>
        <li><a href="ref/fastevent.html#module-pygame.fastevent">pygame.fastevent</a>
</li>
        <li><a href="ref/font.html#module-pygame.font">pygame.font</a>
</li>
        <li><a href="ref/freetype.html#module-pygame.freetype">pygame.freetype</a>
</li>
        <li><a href="ref/gfxdraw.html#module-pygame.gfxdraw">pygame.gfxdraw</a>
</li>
        <li><a href="ref/image.html#module-pygame.image">pygame.image</a>
</li>
        <li><a href="ref/joystick.html#module-pygame.joystick">pygame.joystick</a>
</li>
        <li><a href="ref/key.html#module-pygame.key">pygame.key</a>
</li>
        <li><a href="ref/locals.html#module-pygame.locals">pygame.locals</a>
</li>
        <li><a href="ref/mask.html#module-pygame.mask">pygame.mask</a>
</li>
        <li><a href="ref/math.html#module-pygame.math">pygame.math</a>
</li>
        <li><a href="ref/midi.html#module-pygame.midi">pygame.midi</a>
</li>
        <li><a href="ref/mixer.html#module-pygame.mixer">pygame.mixer</a>
</li>
        <li><a href="ref/music.html#module-pygame.mixer.music">pygame.mixer.music</a>
</li>
        <li><a href="ref/mouse.html#module-pygame.mouse">pygame.mouse</a>
</li>
        <li><a href="ref/pixelcopy.html#module-pygame.pixelcopy">pygame.pixelcopy</a>
</li>
        <li><a href="ref/scrap.html#module-pygame.scrap">pygame.scrap</a>
</li>
        <li><a href="ref/sndarray.html#module-pygame.sndarray">pygame.sndarray</a>
</li>
        <li><a href="ref/sprite.html#module-pygame.sprite">pygame.sprite</a>
</li>
        <li><a href="ref/surfarray.html#module-pygame.surfarray">pygame.surfarray</a>
</li>
        <li><a href="ref/tests.html#module-pygame.tests">pygame.tests</a>
</li>
        <li><a href="ref/time.html#module-pygame.time">pygame.time</a>
</li>
        <li><a href="ref/transform.html#module-pygame.transform">pygame.transform</a>
</li>
        <li><a href="ref/pygame.html#module-pygame.version">pygame.version</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/rect.html#pygame.Rect.move">move() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.move_ip">move_ip() (pygame.Rect method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.move_to_back">move_to_back() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.move_to_front">move_to_front() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.move_towards">move_towards() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.move_towards">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.move_towards_ip">move_towards_ip() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.move_towards_ip">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/examples.html#pygame.examples.moveit.main">moveit.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.mustlock">mustlock() (pygame.Surface method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="N">N</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/freetype.html#pygame.freetype.Font.name">name (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/key.html#pygame.key.name">name() (in module pygame.key)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.name_forindex">name_forindex() (in module pygame._sdl2.controller)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.ndim">ndim (pygame.PixelArray attribute)</a>
</li>
      <li><a href="ref/color.html#pygame.Color.normalize">normalize() (pygame.Color method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector2.normalize">(pygame.math.Vector2 method)</a>
</li>
        <li><a href="ref/math.html#pygame.math.Vector3.normalize">(pygame.math.Vector3 method)</a>
</li>
        <li><a href="ref/rect.html#pygame.Rect.normalize">(pygame.Rect method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/math.html#pygame.math.Vector2.normalize_ip">normalize_ip() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.normalize_ip">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/midi.html#pygame.midi.Output.note_off">note_off() (pygame.midi.Output method)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.Output.note_on">note_on() (pygame.midi.Output method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/freetype.html#pygame.freetype.Font.oblique">oblique (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.opacity">opacity (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.OrderedUpdates">OrderedUpdates() (in module pygame.sprite)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.origin">origin (pygame._sdl2.video.Image attribute)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.Font.origin">(pygame.freetype.Font attribute)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/mask.html#pygame.mask.Mask.outline">outline() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.Output">Output (class in pygame.midi)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.overlap">overlap() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.overlap_area">overlap_area() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.overlap_mask">overlap_mask() (pygame.mask.Mask method)</a>
</li>
      <li><a href="ref/overlay.html#pygame.Overlay">Overlay (class in pygame)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/freetype.html#pygame.freetype.Font.pad">pad (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/bufferproxy.html#pygame.BufferProxy.parent">parent (pygame.BufferProxy attribute)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.path">path (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.pause">pause() (in module pygame.mixer)</a>

      <ul>
        <li><a href="ref/music.html#pygame.mixer.music.pause">(in module pygame.mixer.music)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.CD.pause">(pygame.cdrom.CD method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.pause">(pygame.mixer.Channel method)</a>
</li>
      </ul></li>
      <li><a href="ref/event.html#pygame.event.peek">peek() (in module pygame.event)</a>
</li>
      <li><a href="c_api/base.html#c.pg_buffer">pg_buffer (C type)</a>
</li>
      <li><a href="c_api/base.html#c.pg_buffer.consumer">pg_buffer.consumer (C member)</a>
</li>
      <li><a href="c_api/base.html#c.pg_buffer.release_buffer">pg_buffer.release_buffer (C member)</a>
</li>
      <li><a href="c_api/base.html#c.pg_buffer.view">pg_buffer.view (C member)</a>
</li>
      <li><a href="c_api/rwobject.html#c.pg_EncodeFilePath">pg_EncodeFilePath (C function)</a>
</li>
      <li><a href="c_api/rwobject.html#c.pg_EncodeString">pg_EncodeString (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_FloatFromObj">pg_FloatFromObj (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_FloatFromObjIndex">pg_FloatFromObjIndex (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_GetDefaultWindow">pg_GetDefaultWindow (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_GetDefaultWindowSurface">pg_GetDefaultWindowSurface (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_IntFromObj">pg_IntFromObj (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_IntFromObjIndex">pg_IntFromObjIndex (C function)</a>
</li>
      <li><a href="c_api/version.html#c.PG_MAJOR_VERSION">PG_MAJOR_VERSION (C macro)</a>
</li>
      <li><a href="c_api/version.html#c.PG_MINOR_VERSION">PG_MINOR_VERSION (C macro)</a>
</li>
      <li><a href="c_api/base.html#c.pg_mod_autoinit">pg_mod_autoinit (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_mod_autoquit">pg_mod_autoquit (C function)</a>
</li>
      <li><a href="c_api/version.html#c.PG_PATCH_VERSION">PG_PATCH_VERSION (C macro)</a>
</li>
      <li><a href="c_api/base.html#c.pg_RegisterQuit">pg_RegisterQuit (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_RGBAFromObj">pg_RGBAFromObj (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_SetDefaultWindow">pg_SetDefaultWindow (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_SetDefaultWindowSurface">pg_SetDefaultWindowSurface (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_TwoFloatsFromObj">pg_TwoFloatsFromObj (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_TwoIntsFromObj">pg_TwoIntsFromObj (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_UintFromObj">pg_UintFromObj (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pg_UintFromObjIndex">pg_UintFromObjIndex (C function)</a>
</li>
      <li><a href="c_api/version.html#c.PG_VERSION_ATLEAST">PG_VERSION_ATLEAST (C macro)</a>
</li>
      <li><a href="c_api/version.html#c.PG_VERSIONNUM">PG_VERSIONNUM (C macro)</a>
</li>
      <li><a href="c_api/base.html#c.pgBuffer_AsArrayInterface">pgBuffer_AsArrayInterface (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pgBuffer_AsArrayStruct">pgBuffer_AsArrayStruct (C function)</a>
</li>
      <li><a href="c_api/base.html#c.pgBuffer_Release">pgBuffer_Release (C function)</a>
</li>
      <li><a href="c_api/bufferproxy.html#c.pgBufproxy_Check">pgBufproxy_Check (C function)</a>
</li>
      <li><a href="c_api/bufferproxy.html#c.pgBufproxy_GetParent">pgBufproxy_GetParent (C function)</a>
</li>
      <li><a href="c_api/bufferproxy.html#c.pgBufproxy_New">pgBufproxy_New (C function)</a>
</li>
      <li><a href="c_api/bufferproxy.html#c.pgBufproxy_Trip">pgBufproxy_Trip (C function)</a>
</li>
      <li><a href="c_api/bufferproxy.html#c.pgBufproxy_Type">pgBufproxy_Type (C var)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgChannel_AsInt">pgChannel_AsInt (C function)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgChannel_Check">pgChannel_Check (C function)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgChannel_New">pgChannel_New (C function)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgChannel_Type">pgChannel_Type (C var)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgChannelObject">pgChannelObject (C type)</a>
</li>
      <li><a href="c_api/color.html#c.pgColor_Check">pgColor_Check (C function)</a>
</li>
      <li><a href="c_api/color.html#c.pgColor_New">pgColor_New (C function)</a>
</li>
      <li><a href="c_api/color.html#c.pgColor_NewLength">pgColor_NewLength (C function)</a>
</li>
      <li><a href="c_api/color.html#c.pgColor_Type">pgColor_Type (C var)</a>
</li>
      <li><a href="c_api/base.html#c.pgDict_AsBuffer">pgDict_AsBuffer (C function)</a>
</li>
      <li><a href="c_api/event.html#c.pgEvent_Check">pgEvent_Check (C function)</a>
</li>
      <li><a href="c_api/event.html#c.pgEvent_FillUserEvent">pgEvent_FillUserEvent (C function)</a>
</li>
      <li><a href="c_api/event.html#c.pgEvent_New">pgEvent_New (C function)</a>
</li>
      <li><a href="c_api/event.html#c.pgEvent_New2">pgEvent_New2 (C function)</a>
</li>
      <li><a href="c_api/event.html#c.pgEvent_Type">pgEvent_Type (C type)</a>
</li>
      <li><a href="c_api/event.html#c.pgEventObject">pgEventObject (C type)</a>
</li>
      <li><a href="c_api/event.html#c.pgEventObject.type">pgEventObject.type (C member)</a>
</li>
      <li><a href="c_api/base.html#c.pgExc_BufferError">pgExc_BufferError (C var)</a>
</li>
      <li><a href="c_api/base.html#c.pgExc_SDLError">pgExc_SDLError (C var)</a>
</li>
      <li><a href="c_api/freetype.html#c.pgFont_Check">pgFont_Check (C function)</a>
</li>
      <li><a href="c_api/freetype.html#c.pgFont_IS_ALIVE">pgFont_IS_ALIVE (C function)</a>
</li>
      <li><a href="c_api/freetype.html#c.pgFont_New">pgFont_New (C function)</a>
</li>
      <li><a href="c_api/freetype.html#c.pgFont_Type">pgFont_Type (C type)</a>
</li>
      <li><a href="c_api/freetype.html#c.pgFontObject">pgFontObject (C type)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgLifetimeLock_Check">pgLifetimeLock_Check (C function)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgLifetimeLock_Type">pgLifetimeLock_Type (C var)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgLifetimeLockObject">pgLifetimeLockObject (C type)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgLifetimeLockObject.lockobj">pgLifetimeLockObject.lockobj (C member)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgLifetimeLockObject.surface">pgLifetimeLockObject.surface (C member)</a>
</li>
      <li><a href="c_api/base.html#c.pgObject_GetBuffer">pgObject_GetBuffer (C function)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRect_AsRect">pgRect_AsRect (C function)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRect_Check">pgRect_Check (C function)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRect_FromObject">pgRect_FromObject (C function)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRect_New">pgRect_New (C function)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRect_New4">pgRect_New4 (C function)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRect_Normalize">pgRect_Normalize (C function)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRect_Type">pgRect_Type (C var)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRectObject">pgRectObject (C type)</a>
</li>
      <li><a href="c_api/rect.html#c.pgRectObject.r">pgRectObject.r (C member)</a>
</li>
      <li><a href="c_api/rwobject.html#c.pgRWops_FromFileObject">pgRWops_FromFileObject (C function)</a>
</li>
      <li><a href="c_api/rwobject.html#c.pgRWops_FromObject">pgRWops_FromObject (C function)</a>
</li>
      <li><a href="c_api/rwobject.html#c.pgRWops_IsFileObject">pgRWops_IsFileObject (C function)</a>
</li>
      <li><a href="c_api/rwobject.html#c.pgRWops_ReleaseObject">pgRWops_ReleaseObject (C function)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgSound_AsChunk">pgSound_AsChunk (C function)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgSound_Check">pgSound_Check (C function)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgSound_New">pgSound_New (C function)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgSound_Type">pgSound_Type (C var)</a>
</li>
      <li><a href="c_api/mixer.html#c.pgSoundObject">pgSoundObject (C type)</a>
</li>
      <li><a href="c_api/surface.html#c.pgSurface_AsSurface">pgSurface_AsSurface (C function)</a>
</li>
      <li><a href="c_api/surface.html#c.pgSurface_Blit">pgSurface_Blit (C function)</a>
</li>
      <li><a href="c_api/surface.html#c.pgSurface_Check">pgSurface_Check (C function)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgSurface_Lock">pgSurface_Lock (C function)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgSurface_LockBy">pgSurface_LockBy (C function)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgSurface_LockLifetime">pgSurface_LockLifetime (C function)</a>
</li>
      <li><a href="c_api/surface.html#c.pgSurface_New">pgSurface_New (C function)</a>
</li>
      <li><a href="c_api/surface.html#c.pgSurface_New2">pgSurface_New2 (C function)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgSurface_Prep">pgSurface_Prep (C function)</a>
</li>
      <li><a href="c_api/surface.html#c.pgSurface_Type">pgSurface_Type (C var)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgSurface_UnLock">pgSurface_UnLock (C function)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgSurface_UnLockBy">pgSurface_UnLockBy (C function)</a>
</li>
      <li><a href="c_api/surflock.html#c.pgSurface_Unprep">pgSurface_Unprep (C function)</a>
</li>
      <li><a href="c_api/surface.html#c.pgSurfaceObject">pgSurfaceObject (C type)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="c_api/display.html#c.pgVidInfo_AsVidInfo">pgVidInfo_AsVidInfo (C function)</a>
</li>
      <li><a href="c_api/display.html#c.pgVidInfo_Check">pgVidInfo_Check (C function)</a>
</li>
      <li><a href="c_api/display.html#c.pgVidInfo_New">pgVidInfo_New (C function)</a>
</li>
      <li><a href="c_api/display.html#c.pgVidInfo_Type">pgVidInfo_Type (C var)</a>
</li>
      <li><a href="c_api/display.html#c.pgVidInfoObject">pgVidInfoObject (C type)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.pie">pie() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.Output.pitch_bend">pitch_bend() (pygame.midi.Output method)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.pixel">pixel() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray">PixelArray (class in pygame)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.pixelarray.main">pixelarray.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.pixels2d">pixels2d() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.pixels3d">pixels3d() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.pixels_alpha">pixels_alpha() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.pixels_blue">pixels_blue() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.pixels_green">pixels_green() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/surfarray.html#pygame.surfarray.pixels_red">pixels_red() (in module pygame.surfarray)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.play">play() (in module pygame.mixer.music)</a>

      <ul>
        <li><a href="ref/cdrom.html#pygame.cdrom.CD.play">(pygame.cdrom.CD method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.play">(pygame.mixer.Channel method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Sound.play">(pygame.mixer.Sound method)</a>
</li>
      </ul></li>
      <li><a href="ref/examples.html#pygame.examples.playmus.main">playmus.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/event.html#pygame.event.poll">poll() (in module pygame.event)</a>

      <ul>
        <li><a href="ref/fastevent.html#pygame.fastevent.poll">(in module pygame.fastevent)</a>
</li>
        <li><a href="ref/midi.html#pygame.midi.Input.poll">(pygame.midi.Input method)</a>
</li>
      </ul></li>
      <li><a href="ref/draw.html#pygame.draw.polygon">polygon() (in module pygame.draw)</a>

      <ul>
        <li><a href="ref/gfxdraw.html#pygame.gfxdraw.polygon">(in module pygame.gfxdraw)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.position">position (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/event.html#pygame.event.post">post() (in module pygame.event)</a>

      <ul>
        <li><a href="ref/fastevent.html#pygame.fastevent.post">(in module pygame.fastevent)</a>
</li>
      </ul></li>
      <li><a href="ref/mixer.html#pygame.mixer.pre_init">pre_init() (in module pygame.mixer)</a>
</li>
      <li><a href="ref/color.html#pygame.Color.premul_alpha">premul_alpha() (pygame.Color method)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.premul_alpha">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.present">present() (pygame._sdl2.video.Renderer method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.project">project() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.project">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/event.html#pygame.event.pump">pump() (in module pygame.event)</a>

      <ul>
        <li><a href="ref/fastevent.html#pygame.fastevent.pump">(in module pygame.fastevent)</a>
</li>
      </ul></li>
      <li><a href="ref/scrap.html#pygame.scrap.put">put() (in module pygame.scrap)</a>
</li>
      <li>
    pygame

      <ul>
        <li><a href="ref/pygame.html#module-pygame">module</a>
</li>
      </ul></li>
      <li>
    pygame._sdl2.controller

      <ul>
        <li><a href="ref/sdl2_controller.html#module-pygame._sdl2.controller">module</a>
</li>
      </ul></li>
      <li>
    pygame._sdl2.touch

      <ul>
        <li><a href="ref/touch.html#module-pygame._sdl2.touch">module</a>
</li>
      </ul></li>
      <li>
    pygame._sdl2.video

      <ul>
        <li><a href="ref/sdl2_video.html#module-pygame._sdl2.video">module</a>
</li>
      </ul></li>
      <li>
    pygame.camera

      <ul>
        <li><a href="ref/camera.html#module-pygame.camera">module</a>
</li>
      </ul></li>
      <li>
    pygame.cdrom

      <ul>
        <li><a href="ref/cdrom.html#module-pygame.cdrom">module</a>
</li>
      </ul></li>
      <li>
    pygame.cursors

      <ul>
        <li><a href="ref/cursors.html#module-pygame.cursors">module</a>
</li>
      </ul></li>
      <li>
    pygame.display

      <ul>
        <li><a href="ref/display.html#module-pygame.display">module</a>
</li>
      </ul></li>
      <li>
    pygame.draw

      <ul>
        <li><a href="ref/draw.html#module-pygame.draw">module</a>
</li>
      </ul></li>
      <li>
    pygame.event

      <ul>
        <li><a href="ref/event.html#module-pygame.event">module</a>
</li>
      </ul></li>
      <li>
    pygame.examples

      <ul>
        <li><a href="ref/examples.html#module-pygame.examples">module</a>
</li>
      </ul></li>
      <li>
    pygame.fastevent

      <ul>
        <li><a href="ref/fastevent.html#module-pygame.fastevent">module</a>
</li>
      </ul></li>
      <li>
    pygame.font

      <ul>
        <li><a href="ref/font.html#module-pygame.font">module</a>
</li>
      </ul></li>
      <li>
    pygame.freetype

      <ul>
        <li><a href="ref/freetype.html#module-pygame.freetype">module</a>
</li>
      </ul></li>
      <li>
    pygame.gfxdraw

      <ul>
        <li><a href="ref/gfxdraw.html#module-pygame.gfxdraw">module</a>
</li>
      </ul></li>
      <li>
    pygame.image

      <ul>
        <li><a href="ref/image.html#module-pygame.image">module</a>
</li>
      </ul></li>
      <li>
    pygame.joystick

      <ul>
        <li><a href="ref/joystick.html#module-pygame.joystick">module</a>
</li>
      </ul></li>
      <li>
    pygame.key

      <ul>
        <li><a href="ref/key.html#module-pygame.key">module</a>
</li>
      </ul></li>
      <li>
    pygame.locals

      <ul>
        <li><a href="ref/locals.html#module-pygame.locals">module</a>
</li>
      </ul></li>
      <li>
    pygame.mask

      <ul>
        <li><a href="ref/mask.html#module-pygame.mask">module</a>
</li>
      </ul></li>
      <li>
    pygame.math

      <ul>
        <li><a href="ref/math.html#module-pygame.math">module</a>
</li>
      </ul></li>
      <li>
    pygame.midi

      <ul>
        <li><a href="ref/midi.html#module-pygame.midi">module</a>
</li>
      </ul></li>
      <li>
    pygame.mixer

      <ul>
        <li><a href="ref/mixer.html#module-pygame.mixer">module</a>
</li>
      </ul></li>
      <li>
    pygame.mixer.music

      <ul>
        <li><a href="ref/music.html#module-pygame.mixer.music">module</a>
</li>
      </ul></li>
      <li>
    pygame.mouse

      <ul>
        <li><a href="ref/mouse.html#module-pygame.mouse">module</a>
</li>
      </ul></li>
      <li>
    pygame.pixelcopy

      <ul>
        <li><a href="ref/pixelcopy.html#module-pygame.pixelcopy">module</a>
</li>
      </ul></li>
      <li>
    pygame.scrap

      <ul>
        <li><a href="ref/scrap.html#module-pygame.scrap">module</a>
</li>
      </ul></li>
      <li>
    pygame.sndarray

      <ul>
        <li><a href="ref/sndarray.html#module-pygame.sndarray">module</a>
</li>
      </ul></li>
      <li>
    pygame.sprite

      <ul>
        <li><a href="ref/sprite.html#module-pygame.sprite">module</a>
</li>
      </ul></li>
      <li>
    pygame.surfarray

      <ul>
        <li><a href="ref/surfarray.html#module-pygame.surfarray">module</a>
</li>
      </ul></li>
      <li>
    pygame.tests

      <ul>
        <li><a href="ref/tests.html#module-pygame.tests">module</a>
</li>
      </ul></li>
      <li>
    pygame.time

      <ul>
        <li><a href="ref/time.html#module-pygame.time">module</a>
</li>
      </ul></li>
      <li>
    pygame.transform

      <ul>
        <li><a href="ref/transform.html#module-pygame.transform">module</a>
</li>
      </ul></li>
      <li>
    pygame.version

      <ul>
        <li><a href="ref/pygame.html#module-pygame.version">module</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="Q">Q</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/camera.html#pygame.camera.Camera.query_image">query_image() (pygame.camera.Camera method)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.queue">queue() (in module pygame.mixer.music)</a>

      <ul>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.queue">(pygame.mixer.Channel method)</a>
</li>
      </ul></li>
      <li><a href="ref/pygame.html#pygame.quit">quit() (in module pygame)</a>

      <ul>
        <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.quit">(in module pygame._sdl2.controller)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.quit">(in module pygame.cdrom)</a>
</li>
        <li><a href="ref/display.html#pygame.display.quit">(in module pygame.display)</a>
</li>
        <li><a href="ref/font.html#pygame.font.quit">(in module pygame.font)</a>
</li>
        <li><a href="ref/freetype.html#pygame.freetype.quit">(in module pygame.freetype)</a>
</li>
        <li><a href="ref/joystick.html#pygame.joystick.quit">(in module pygame.joystick)</a>
</li>
        <li><a href="ref/midi.html#pygame.midi.quit">(in module pygame.midi)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.quit">(in module pygame.mixer)</a>
</li>
        <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.quit">(pygame._sdl2.controller.Controller method)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.CD.quit">(pygame.cdrom.CD method)</a>
</li>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.quit">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/color.html#pygame.Color.r">r (pygame.Color attribute)</a>
</li>
      <li><a href="ref/bufferproxy.html#pygame.BufferProxy.raw">raw (pygame.BufferProxy attribute)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.Input.read">read() (pygame.midi.Input method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect">Rect (class in pygame)</a>
</li>
      <li><a href="ref/draw.html#pygame.draw.rect">rect() (in module pygame.draw)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.rectangle">rectangle() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.reflect">reflect() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.reflect">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.reflect_ip">reflect_ip() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.reflect_ip">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/pygame.html#pygame.register_quit">register_quit() (in module pygame)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.relative_mouse">relative_mouse (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.Group.remove">remove() (pygame.sprite.Group method)</a>

      <ul>
        <li><a href="ref/sprite.html#pygame.sprite.Sprite.remove">(pygame.sprite.Sprite method)</a>
</li>
      </ul></li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.remove_sprites_of_layer">remove_sprites_of_layer() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.render">render() (pygame.font.Font method)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.Font.render">(pygame.freetype.Font method)</a>
</li>
      </ul></li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.render_raw">render_raw() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.render_raw_to">render_raw_to() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.render_to">render_to() (pygame.freetype.Font method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.RenderClear">RenderClear (class in pygame.sprite)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer">Renderer (class in pygame._sdl2.video)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.renderer">renderer (pygame._sdl2.video.Texture attribute)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.RenderPlain">RenderPlain (class in pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.RenderUpdates">RenderUpdates (class in pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty.repaint_rect">repaint_rect() (pygame.sprite.LayeredDirty method)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.replace">replace() (pygame.PixelArray method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.resizable">resizable (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.resolution">resolution (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.restore">restore() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/cdrom.html#pygame.cdrom.CD.resume">resume() (pygame.cdrom.CD method)</a>
</li>
      <li><a href="ref/pygame.html#pygame.version.rev">rev (in module pygame.version)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/music.html#pygame.mixer.music.rewind">rewind() (in module pygame.mixer.music)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.rotate">rotate() (in module pygame.transform)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector2.rotate">(pygame.math.Vector2 method)</a>
</li>
        <li><a href="ref/math.html#pygame.math.Vector3.rotate">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.rotate_ip">rotate_ip() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.rotate_ip">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.rotate_ip_rad">rotate_ip_rad() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.rotate_ip_rad">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.rotate_rad">rotate_rad() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.rotate_rad">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector2.rotate_rad_ip">rotate_rad_ip() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.rotate_rad_ip">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_x">rotate_x() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_x_ip">rotate_x_ip() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_x_ip_rad">rotate_x_ip_rad() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_x_rad">rotate_x_rad() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_x_rad_ip">rotate_x_rad_ip() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_y">rotate_y() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_y_ip">rotate_y_ip() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_y_ip_rad">rotate_y_ip_rad() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_y_rad">rotate_y_rad() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_y_rad_ip">rotate_y_rad_ip() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_z">rotate_z() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_z_ip">rotate_z_ip() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_z_ip_rad">rotate_z_ip_rad() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_z_rad">rotate_z_rad() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3.rotate_z_rad_ip">rotate_z_rad_ip() (pygame.math.Vector3 method)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.rotation">rotation (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.rotozoom">rotozoom() (in module pygame.transform)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.rumble">rumble() (pygame._sdl2.controller.Controller method)</a>

      <ul>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.rumble">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
      <li><a href="ref/tests.html#pygame.tests.run">run() (in module pygame.tests)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/sndarray.html#pygame.sndarray.samples">samples() (in module pygame.sndarray)</a>
</li>
      <li><a href="ref/image.html#pygame.image.save">save() (in module pygame.image)</a>
</li>
      <li><a href="ref/image.html#pygame.image.save_extended">save_extended() (in module pygame.image)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.scalable">scalable (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.scale">scale (pygame._sdl2.video.Renderer attribute)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.scale">scale() (in module pygame.transform)</a>

      <ul>
        <li><a href="ref/mask.html#pygame.mask.Mask.scale">(pygame.mask.Mask method)</a>
</li>
      </ul></li>
      <li><a href="ref/transform.html#pygame.transform.scale2x">scale2x() (in module pygame.transform)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.scale_by">scale_by() (in module pygame.transform)</a>

      <ul>
        <li><a href="ref/rect.html#pygame.Rect.scale_by">(pygame.Rect method)</a>
</li>
      </ul></li>
      <li><a href="ref/rect.html#pygame.Rect.scale_by_ip">scale_by_ip() (pygame.Rect method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.scale_to_length">scale_to_length() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.scale_to_length">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/examples.html#pygame.examples.scaletest.main">scaletest.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.scrap_clipboard.main">scrap_clipboard.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.scroll">scroll() (pygame.Surface method)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.scroll.main">scroll.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/pygame.html#pygame.version.SDL">SDL (in module pygame.version)</a>
</li>
      <li><a href="ref/display.html#pygame.display.set_allow_screensaver">set_allow_screensaver() (in module pygame.display)</a>
</li>
      <li><a href="ref/event.html#pygame.event.set_allowed">set_allowed() (in module pygame.event)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.set_alpha">set_alpha() (pygame.Surface method)</a>
</li>
      <li><a href="ref/mask.html#pygame.mask.Mask.set_at">set_at() (pygame.mask.Mask method)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.set_at">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/event.html#pygame.event.set_blocked">set_blocked() (in module pygame.event)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.set_bold">set_bold() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.set_caption">set_caption() (in module pygame.display)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty.set_clip">set_clip() (pygame.sprite.LayeredDirty method)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.set_clip">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/surface.html#pygame.Surface.set_colorkey">set_colorkey() (pygame.Surface method)</a>
</li>
      <li><a href="ref/camera.html#pygame.camera.Camera.set_controls">set_controls() (pygame.camera.Camera method)</a>
</li>
      <li><a href="ref/mouse.html#pygame.mouse.set_cursor">set_cursor() (in module pygame.mouse)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.set_default_resolution">set_default_resolution() (in module pygame.freetype)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.set_endevent">set_endevent() (in module pygame.mixer.music)</a>

      <ul>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.set_endevent">(pygame.mixer.Channel method)</a>
</li>
      </ul></li>
      <li><a href="ref/pygame.html#pygame.set_error">set_error() (in module pygame)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.set_eventstate">set_eventstate() (in module pygame._sdl2.controller)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.set_fullscreen">set_fullscreen() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.set_gamma">set_gamma() (in module pygame.display)</a>
</li>
      <li><a href="ref/display.html#pygame.display.set_gamma_ramp">set_gamma_ramp() (in module pygame.display)</a>
</li>
      <li><a href="ref/event.html#pygame.event.set_grab">set_grab() (in module pygame.event)</a>
</li>
      <li><a href="ref/display.html#pygame.display.set_icon">set_icon() (in module pygame.display)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.set_icon">(pygame._sdl2.video.Window method)</a>
</li>
      </ul></li>
      <li><a href="ref/midi.html#pygame.midi.Output.set_instrument">set_instrument() (pygame.midi.Output method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.set_italic">set_italic() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/event.html#pygame.event.set_keyboard_grab">set_keyboard_grab() (in module pygame.event)</a>
</li>
      <li><a href="ref/color.html#pygame.Color.set_length">set_length() (pygame.Color method)</a>
</li>
      <li><a href="ref/overlay.html#pygame.Overlay.set_location">set_location() (pygame.Overlay method)</a>
</li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.set_mapping">set_mapping() (pygame._sdl2.controller.Controller method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.set_masks">set_masks() (pygame.Surface method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.set_modal_for">set_modal_for() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/display.html#pygame.display.set_mode">set_mode() (in module pygame.display)</a>

      <ul>
        <li><a href="ref/scrap.html#pygame.scrap.set_mode">(in module pygame.scrap)</a>
</li>
      </ul></li>
      <li><a href="ref/key.html#pygame.key.set_mods">set_mods() (in module pygame.key)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.set_num_channels">set_num_channels() (in module pygame.mixer)</a>
</li>
      <li><a href="ref/display.html#pygame.display.set_palette">set_palette() (in module pygame.display)</a>

      <ul>
        <li><a href="ref/surface.html#pygame.Surface.set_palette">(pygame.Surface method)</a>
</li>
      </ul></li>
      <li><a href="ref/surface.html#pygame.Surface.set_palette_at">set_palette_at() (pygame.Surface method)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.set_pos">set_pos() (in module pygame.mixer.music)</a>

      <ul>
        <li><a href="ref/mouse.html#pygame.mouse.set_pos">(in module pygame.mouse)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/key.html#pygame.key.set_repeat">set_repeat() (in module pygame.key)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.set_reserved">set_reserved() (in module pygame.mixer)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.set_script">set_script() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.set_shifts">set_shifts() (pygame.Surface method)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.set_smoothscale_backend">set_smoothscale_backend() (in module pygame.transform)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.set_strikethrough">set_strikethrough() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/key.html#pygame.key.set_text_input_rect">set_text_input_rect() (in module pygame.key)</a>
</li>
      <li><a href="ref/time.html#pygame.time.set_timer">set_timer() (in module pygame.time)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty.set_timing_threshold">set_timing_threshold() (pygame.sprite.LayeredDirty method)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredDirty.set_timing_treshold">set_timing_treshold() (pygame.sprite.LayeredDirty method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.set_underline">set_underline() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.set_viewport">set_viewport() (pygame._sdl2.video.Renderer method)</a>
</li>
      <li><a href="ref/mouse.html#pygame.mouse.set_visible">set_visible() (in module pygame.mouse)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.set_volume">set_volume() (in module pygame.mixer.music)</a>

      <ul>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.set_volume">(pygame.mixer.Channel method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Sound.set_volume">(pygame.mixer.Sound method)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.set_windowed">set_windowed() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.shape">shape (pygame.PixelArray attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.show">show() (pygame._sdl2.video.Window method)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.size">size (pygame._sdl2.video.Window attribute)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.Font.size">(pygame.freetype.Font attribute)</a>
</li>
      </ul></li>
      <li><a href="ref/font.html#pygame.font.Font.size">size() (pygame.font.Font method)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector2.slerp">slerp() (pygame.math.Vector2 method)</a>

      <ul>
        <li><a href="ref/math.html#pygame.math.Vector3.slerp">(pygame.math.Vector3 method)</a>
</li>
      </ul></li>
      <li><a href="ref/transform.html#pygame.transform.smoothscale">smoothscale() (in module pygame.transform)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.smoothscale_by">smoothscale_by() (in module pygame.transform)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.Sound">Sound (class in pygame.mixer)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.sound.main">sound.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.sound_array_demos.main">sound_array_demos.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.Sprite">Sprite (class in pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.spritecollide">spritecollide() (in module pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.spritecollideany">spritecollideany() (in module pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.Group.sprites">sprites() (pygame.sprite.Group method)</a>

      <ul>
        <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.sprites">(pygame.sprite.LayeredUpdates method)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.srcrect">srcrect (pygame._sdl2.video.Image attribute)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.stars.main">stars.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/camera.html#pygame.camera.Camera.start">start() (pygame.camera.Camera method)</a>
</li>
      <li><a href="ref/key.html#pygame.key.start_text_input">start_text_input() (in module pygame.key)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.stop">stop() (in module pygame.mixer)</a>

      <ul>
        <li><a href="ref/music.html#pygame.mixer.music.stop">(in module pygame.mixer.music)</a>
</li>
        <li><a href="ref/camera.html#pygame.camera.Camera.stop">(pygame.camera.Camera method)</a>
</li>
        <li><a href="ref/cdrom.html#pygame.cdrom.CD.stop">(pygame.cdrom.CD method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.stop">(pygame.mixer.Channel method)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Sound.stop">(pygame.mixer.Sound method)</a>
</li>
      </ul></li>
      <li><a href="ref/sdl2_controller.html#pygame._sdl2.controller.Controller.stop_rumble">stop_rumble() (pygame._sdl2.controller.Controller method)</a>

      <ul>
        <li><a href="ref/joystick.html#pygame.joystick.Joystick.stop_rumble">(pygame.joystick.Joystick method)</a>
</li>
      </ul></li>
      <li><a href="ref/key.html#pygame.key.stop_text_input">stop_text_input() (in module pygame.key)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.strength">strength (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.strides">strides (pygame.PixelArray attribute)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.strikethrough">strikethrough (pygame.font.Font attribute)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.strong">strong (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.style">style (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.subsurface">subsurface() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface">Surface (class in pygame)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.surface">surface (pygame.PixelArray attribute)</a>
</li>
      <li><a href="ref/pixelcopy.html#pygame.pixelcopy.surface_to_array">surface_to_array() (in module pygame.pixelcopy)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.LayeredUpdates.switch_layer">switch_layer() (pygame.sprite.LayeredUpdates method)</a>
</li>
      <li><a href="ref/font.html#pygame.font.SysFont">SysFont() (in module pygame.font)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.SysFont">(in module pygame.freetype)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.target">target (pygame._sdl2.video.Renderer attribute)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.testsprite.main">testsprite.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture">Texture (class in pygame._sdl2.video)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Image.texture">texture (pygame._sdl2.video.Image attribute)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.textured_polygon">textured_polygon() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/transform.html#pygame.transform.threshold">threshold() (in module pygame.transform)</a>
</li>
      <li><a href="ref/time.html#pygame.time.Clock.tick">tick() (pygame.time.Clock method)</a>
</li>
      <li><a href="ref/time.html#pygame.time.Clock.tick_busy_loop">tick_busy_loop() (pygame.time.Clock method)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.time">time() (in module pygame.midi)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window.title">title (pygame._sdl2.video.Window attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Renderer.to_surface">to_surface() (pygame._sdl2.video.Renderer method)</a>

      <ul>
        <li><a href="ref/mask.html#pygame.mask.Mask.to_surface">(pygame.mask.Mask method)</a>
</li>
      </ul></li>
      <li><a href="ref/image.html#pygame.image.tobytes">tobytes() (in module pygame.image)</a>
</li>
      <li><a href="ref/display.html#pygame.display.toggle_fullscreen">toggle_fullscreen() (in module pygame.display)</a>
</li>
      <li><a href="ref/image.html#pygame.image.tostring">tostring() (in module pygame.image)</a>
</li>
      <li><a href="ref/pixelarray.html#pygame.PixelArray.transpose">transpose() (pygame.PixelArray method)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.trigon">trigon() (in module pygame.gfxdraw)</a>
</li>
      <li><a href="ref/cursors.html#pygame.cursors.Cursor.type">type (pygame.cursors.Cursor attribute)</a>

      <ul>
        <li><a href="ref/event.html#pygame.event.Event.type">(pygame.event.Event attribute)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/freetype.html#pygame.freetype.Font.ucs4">ucs4 (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/font.html#pygame.font.Font.underline">underline (pygame.font.Font attribute)</a>

      <ul>
        <li><a href="ref/freetype.html#pygame.freetype.Font.underline">(pygame.freetype.Font attribute)</a>
</li>
      </ul></li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.underline_adjustment">underline_adjustment (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.union">union() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.union_ip">union_ip() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.unionall">unionall() (pygame.Rect method)</a>
</li>
      <li><a href="ref/rect.html#pygame.Rect.unionall_ip">unionall_ip() (pygame.Rect method)</a>
</li>
      <li><a href="ref/music.html#pygame.mixer.music.unload">unload() (in module pygame.mixer.music)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.unlock">unlock() (pygame.Surface method)</a>
</li>
      <li><a href="ref/surface.html#pygame.Surface.unmap_rgb">unmap_rgb() (pygame.Surface method)</a>
</li>
      <li><a href="ref/mixer.html#pygame.mixer.unpause">unpause() (in module pygame.mixer)</a>

      <ul>
        <li><a href="ref/music.html#pygame.mixer.music.unpause">(in module pygame.mixer.music)</a>
</li>
        <li><a href="ref/mixer.html#pygame.mixer.Channel.unpause">(pygame.mixer.Channel method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/display.html#pygame.display.update">update() (in module pygame.display)</a>

      <ul>
        <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.update">(pygame._sdl2.video.Texture method)</a>
</li>
        <li><a href="ref/color.html#pygame.Color.update">(pygame.Color method)</a>
</li>
        <li><a href="ref/math.html#pygame.math.Vector2.update">(pygame.math.Vector2 method)</a>
</li>
        <li><a href="ref/math.html#pygame.math.Vector3.update">(pygame.math.Vector3 method)</a>
</li>
        <li><a href="ref/rect.html#pygame.Rect.update">(pygame.Rect method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.Group.update">(pygame.sprite.Group method)</a>
</li>
        <li><a href="ref/sprite.html#pygame.sprite.Sprite.update">(pygame.sprite.Sprite method)</a>
</li>
      </ul></li>
      <li><a href="ref/sndarray.html#pygame.sndarray.use_arraytype">use_arraytype() (in module pygame.sndarray)</a>

      <ul>
        <li><a href="ref/surfarray.html#pygame.surfarray.use_arraytype">(in module pygame.surfarray)</a>
</li>
      </ul></li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.use_bitmap_strikes">use_bitmap_strikes (pygame.freetype.Font attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="V">V</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/math.html#pygame.math.Vector2">Vector2 (class in pygame.math)</a>
</li>
      <li><a href="ref/math.html#pygame.math.Vector3">Vector3 (class in pygame.math)</a>
</li>
      <li><a href="ref/pygame.html#pygame.version.ver">ver (in module pygame.version)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/pygame.html#pygame.version.vernum">vernum (in module pygame.version)</a>
</li>
      <li><a href="ref/freetype.html#pygame.freetype.Font.vertical">vertical (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/examples.html#pygame.examples.vgrade.main">vgrade.main() (in module pygame.examples)</a>
</li>
      <li><a href="ref/gfxdraw.html#pygame.gfxdraw.vline">vline() (in module pygame.gfxdraw)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="W">W</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/event.html#pygame.event.wait">wait() (in module pygame.event)</a>

      <ul>
        <li><a href="ref/fastevent.html#pygame.fastevent.wait">(in module pygame.fastevent)</a>
</li>
        <li><a href="ref/time.html#pygame.time.wait">(in module pygame.time)</a>
</li>
      </ul></li>
      <li><a href="ref/freetype.html#pygame.freetype.was_init">was_init() (in module pygame.freetype)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.WeakDirtySprite">WeakDirtySprite (class in pygame.sprite)</a>
</li>
      <li><a href="ref/sprite.html#pygame.sprite.WeakSprite">WeakSprite (class in pygame.sprite)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="ref/freetype.html#pygame.freetype.Font.wide">wide (pygame.freetype.Font attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Texture.width">width (pygame._sdl2.video.Texture attribute)</a>
</li>
      <li><a href="ref/sdl2_video.html#pygame._sdl2.video.Window">Window (class in pygame._sdl2.video)</a>
</li>
      <li><a href="ref/bufferproxy.html#pygame.BufferProxy.write">write() (pygame.BufferProxy method)</a>

      <ul>
        <li><a href="ref/midi.html#pygame.midi.Output.write">(pygame.midi.Output method)</a>
</li>
      </ul></li>
      <li><a href="ref/midi.html#pygame.midi.Output.write_short">write_short() (pygame.midi.Output method)</a>
</li>
      <li><a href="ref/midi.html#pygame.midi.Output.write_sys_ex">write_sys_ex() (pygame.midi.Output method)</a>
</li>
  </ul></td>
</tr></table>



            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="#" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Index</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>