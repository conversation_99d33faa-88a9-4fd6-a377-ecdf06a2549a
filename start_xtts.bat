@echo off
echo Starting XTTS Voice Generator...

:: Check if Python 3.10 is installed
set PYTHON310_PATH=%LOCALAPPDATA%\Programs\Python\Python310\python.exe
if not exist "%PYTHON310_PATH%" (
    echo Python 3.10 not found. Running Python 3.10 installer...
    powershell -ExecutionPolicy Bypass -File install_python310.ps1
    if errorlevel 1 (
        echo Python 3.10 installation failed. Please install Python 3.10 manually.
        pause
        exit /b 1
    )
)

:: Check if virtual environment exists
if not exist venv (
    echo Virtual environment not found. Running setup first...
    powershell -ExecutionPolicy Bypass -File setup_xtts.ps1
    if errorlevel 1 (
        echo Setup failed. Please check the error messages above.
        pause
        exit /b 1
    )
)

:: Activate virtual environment and run the GUI
echo Activating virtual environment...
call venv\Scripts\activate.bat

:: Check if TTS is installed
pip show TTS > nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Package installation failed. Please check the error messages above.
        pause
        exit /b 1
    )
)

:: Create necessary directories
if not exist samples mkdir samples
if not exist output mkdir output
if not exist models mkdir models

echo Starting XTTS GUI...
python xtts_gui.py

:: Deactivate virtual environment when done
call venv\Scripts\deactivate.bat
