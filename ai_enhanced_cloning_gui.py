#!/usr/bin/env python3
"""
AI-Enhanced Voice Cloning GUI
GUI integration for free AI models and enhanced voice cloning
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import threading
import time

class AIEnhancedCloningGUI:
    """GUI for AI-enhanced voice cloning"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 AI-Enhanced Voice Cloning 🇪🇬")
        self.root.geometry("1000x800")
        self.root.configure(bg='#1a1a1a')
        
        # Initialize AI cloning system
        self.ai_cloning = None
        self.current_analysis = None
        self.selected_reference = None
        
        self.create_gui()
        self.initialize_ai_system()
    
    def create_gui(self):
        """Create the AI-enhanced GUI"""
        
        # Main title
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(pady=10)
        
        title_label = tk.Label(title_frame, text="🤖 AI-Enhanced Voice Cloning 🇪🇬", 
                              font=('Arial', 20, 'bold'), fg='#00ff88', bg='#1a1a1a')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="Free AI Models for Superior Egyptian Voice Cloning", 
                                 font=('Arial', 12), fg='#88ff88', bg='#1a1a1a')
        subtitle_label.pack()
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Voice Analysis Tab
        self.create_voice_analysis_tab()
        
        # Enhanced Cloning Tab
        self.create_enhanced_cloning_tab()
        
        # Library Analysis Tab
        self.create_library_analysis_tab()
        
        # AI Insights Tab
        self.create_ai_insights_tab()
    
    def create_voice_analysis_tab(self):
        """Create voice analysis tab"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="🔍 Voice Analysis")
        
        # File selection
        file_frame = tk.LabelFrame(analysis_frame, text="📁 Select Audio File", 
                                  font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        file_frame.pack(fill='x', padx=10, pady=10)
        
        self.selected_file_var = tk.StringVar(value="No file selected")
        file_label = tk.Label(file_frame, textvariable=self.selected_file_var, 
                             font=('Arial', 10), fg='white', bg='#2a2a2a')
        file_label.pack(pady=5)
        
        button_frame = tk.Frame(file_frame, bg='#2a2a2a')
        button_frame.pack(pady=5)
        
        tk.Button(button_frame, text="📁 Browse Audio File", 
                 command=self.browse_audio_file, bg='#4a4a4a', fg='white').pack(side='left', padx=5)
        
        tk.Button(button_frame, text="🎤 Use Trained Sample", 
                 command=self.select_trained_sample, bg='#4a4a4a', fg='white').pack(side='left', padx=5)
        
        tk.Button(button_frame, text="🔍 Analyze Voice", 
                 command=self.analyze_voice, bg='#00aa44', fg='white', 
                 font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        
        # Analysis results
        results_frame = tk.LabelFrame(analysis_frame, text="📊 AI Analysis Results", 
                                     font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.analysis_text = tk.Text(results_frame, height=15, font=('Consolas', 9), 
                                    bg='#3a3a3a', fg='#88ff88', wrap='word')
        scrollbar = tk.Scrollbar(results_frame, orient='vertical', command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=scrollbar.set)
        
        self.analysis_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
    
    def create_enhanced_cloning_tab(self):
        """Create enhanced cloning tab"""
        cloning_frame = ttk.Frame(self.notebook)
        self.notebook.add(cloning_frame, text="🚀 Enhanced Cloning")
        
        # Reference audio selection
        ref_frame = tk.LabelFrame(cloning_frame, text="🎤 Reference Audio", 
                                 font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        ref_frame.pack(fill='x', padx=10, pady=10)
        
        self.reference_var = tk.StringVar(value="No reference selected")
        ref_label = tk.Label(ref_frame, textvariable=self.reference_var, 
                            font=('Arial', 10), fg='white', bg='#2a2a2a')
        ref_label.pack(pady=5)
        
        ref_button_frame = tk.Frame(ref_frame, bg='#2a2a2a')
        ref_button_frame.pack(pady=5)
        
        tk.Button(ref_button_frame, text="📁 Browse Reference", 
                 command=self.browse_reference_audio, bg='#4a4a4a', fg='white').pack(side='left', padx=5)
        
        tk.Button(ref_button_frame, text="🎯 Use Best Trained Sample", 
                 command=self.use_best_sample, bg='#4a4a4a', fg='white').pack(side='left', padx=5)
        
        # Text input
        text_frame = tk.LabelFrame(cloning_frame, text="📝 Text to Generate", 
                                  font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        text_frame.pack(fill='x', padx=10, pady=10)
        
        self.text_area = tk.Text(text_frame, height=4, font=('Arial', 11), 
                                bg='#3a3a3a', fg='white', wrap='word')
        self.text_area.pack(fill='x', padx=5, pady=5)
        
        # AI suggestions
        suggestions_frame = tk.Frame(text_frame, bg='#2a2a2a')
        suggestions_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(suggestions_frame, text="💡 AI Suggestions:", 
                font=('Arial', 10, 'bold'), fg='#88ff88', bg='#2a2a2a').pack(anchor='w')
        
        suggestion_buttons_frame = tk.Frame(suggestions_frame, bg='#2a2a2a')
        suggestion_buttons_frame.pack(fill='x', pady=2)
        
        suggestions = [
            "جميل قوي! هذا اختبار للذكاء الاصطناعي.",
            "يلا بينا نجرب الصوت الجديد المحسن.",
            "والله العظيم ده أحلى كلام سمعته النهاردة!",
            "مصر أم الدنيا وتقنية الذكاء الاصطناعي رائعة."
        ]
        
        for suggestion in suggestions:
            btn = tk.Button(suggestion_buttons_frame, text=suggestion[:30] + "...", 
                           command=lambda s=suggestion: self.insert_suggestion(s),
                           bg='#4a4a4a', fg='white', font=('Arial', 8))
            btn.pack(side='left', padx=2, pady=2)
        
        # AI parameters display
        params_frame = tk.LabelFrame(cloning_frame, text="⚙️ AI-Optimized Parameters", 
                                    font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        params_frame.pack(fill='x', padx=10, pady=10)
        
        self.params_text = tk.Text(params_frame, height=4, font=('Consolas', 9), 
                                  bg='#3a3a3a', fg='#88ff88', state='disabled')
        self.params_text.pack(fill='x', padx=5, pady=5)
        
        # Generation controls
        gen_frame = tk.LabelFrame(cloning_frame, text="🎤 AI-Enhanced Generation", 
                                 font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        gen_frame.pack(fill='x', padx=10, pady=10)
        
        gen_button_frame = tk.Frame(gen_frame, bg='#2a2a2a')
        gen_button_frame.pack(pady=10)
        
        tk.Button(gen_button_frame, text="🚀 Generate with AI Enhancement", 
                 command=self.generate_enhanced_speech, bg='#00aa44', fg='white', 
                 font=('Arial', 12, 'bold')).pack(side='left', padx=5)
        
        tk.Button(gen_button_frame, text="🎯 Quick Test", 
                 command=self.quick_test_generation, bg='#4a4a4a', fg='white').pack(side='left', padx=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(gen_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.pack(pady=5)
        
        self.status_label = tk.Label(gen_frame, text="Ready for AI-enhanced generation", 
                                    font=('Arial', 10), fg='#88ff88', bg='#2a2a2a')
        self.status_label.pack()
    
    def create_library_analysis_tab(self):
        """Create library analysis tab"""
        library_frame = ttk.Frame(self.notebook)
        self.notebook.add(library_frame, text="📊 Library Analysis")
        
        # Library selection
        lib_select_frame = tk.LabelFrame(library_frame, text="📁 Voice Library Selection", 
                                        font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        lib_select_frame.pack(fill='x', padx=10, pady=10)
        
        self.library_var = tk.StringVar(value="trained_egyptian_samples")
        
        library_options = [
            "trained_egyptian_samples",
            "egyptian_voice_library", 
            "pronunciation_samples",
            "enhanced_egyptian_samples"
        ]
        
        for option in library_options:
            rb = tk.Radiobutton(lib_select_frame, text=option, variable=self.library_var, 
                               value=option, bg='#2a2a2a', fg='white', 
                               selectcolor='#4a4a4a', font=('Arial', 10))
            rb.pack(anchor='w', padx=10, pady=2)
        
        button_frame = tk.Frame(lib_select_frame, bg='#2a2a2a')
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="📊 Analyze Library", 
                 command=self.analyze_library, bg='#00aa44', fg='white', 
                 font=('Arial', 11, 'bold')).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="📁 Browse Custom Library", 
                 command=self.browse_custom_library, bg='#4a4a4a', fg='white').pack(side='left', padx=5)
        
        # Library results
        lib_results_frame = tk.LabelFrame(library_frame, text="📈 Library Analysis Results", 
                                         font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        lib_results_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.library_text = tk.Text(lib_results_frame, height=15, font=('Consolas', 9), 
                                   bg='#3a3a3a', fg='#88ff88', wrap='word')
        lib_scrollbar = tk.Scrollbar(lib_results_frame, orient='vertical', command=self.library_text.yview)
        self.library_text.configure(yscrollcommand=lib_scrollbar.set)
        
        self.library_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        lib_scrollbar.pack(side='right', fill='y')
    
    def create_ai_insights_tab(self):
        """Create AI insights tab"""
        insights_frame = ttk.Frame(self.notebook)
        self.notebook.add(insights_frame, text="💡 AI Insights")
        
        # AI model status
        status_frame = tk.LabelFrame(insights_frame, text="🤖 AI Model Status", 
                                    font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        status_frame.pack(fill='x', padx=10, pady=10)
        
        self.ai_status_text = tk.Text(status_frame, height=6, font=('Consolas', 9), 
                                     bg='#3a3a3a', fg='#88ff88', state='disabled')
        self.ai_status_text.pack(fill='x', padx=5, pady=5)
        
        # AI recommendations
        recommendations_frame = tk.LabelFrame(insights_frame, text="💡 AI Recommendations", 
                                             font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a')
        recommendations_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.recommendations_text = tk.Text(recommendations_frame, height=10, font=('Consolas', 9), 
                                           bg='#3a3a3a', fg='#88ff88', wrap='word')
        rec_scrollbar = tk.Scrollbar(recommendations_frame, orient='vertical', command=self.recommendations_text.yview)
        self.recommendations_text.configure(yscrollcommand=rec_scrollbar.set)
        
        self.recommendations_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        rec_scrollbar.pack(side='right', fill='y')
        
        # AI controls
        controls_frame = tk.Frame(insights_frame, bg='#2a2a2a')
        controls_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(controls_frame, text="🔄 Refresh AI Status", 
                 command=self.refresh_ai_status, bg='#4a4a4a', fg='white').pack(side='left', padx=5)
        
        tk.Button(controls_frame, text="📋 Export AI Report", 
                 command=self.export_ai_report, bg='#4a4a4a', fg='white').pack(side='left', padx=5)
    
    def initialize_ai_system(self):
        """Initialize AI system in background"""
        def init_ai():
            try:
                from free_ai_voice_cloning import FreeAIVoiceCloning
                self.ai_cloning = FreeAIVoiceCloning()
                self.root.after(0, self.update_ai_status_success)
            except Exception as e:
                self.root.after(0, lambda: self.update_ai_status_error(str(e)))
        
        threading.Thread(target=init_ai, daemon=True).start()
        self.update_ai_status("🔄 Initializing AI models...")
    
    def update_ai_status_success(self):
        """Update AI status on successful initialization"""
        self.update_ai_status("✅ AI models loaded successfully!")
        self.refresh_ai_status()
    
    def update_ai_status_error(self, error):
        """Update AI status on initialization error"""
        self.update_ai_status(f"⚠️ AI initialization warning: {error}")
    
    def update_ai_status(self, message):
        """Update AI status display"""
        if hasattr(self, 'ai_status_text'):
            self.ai_status_text.config(state='normal')
            self.ai_status_text.insert(tk.END, f"{message}\n")
            self.ai_status_text.see(tk.END)
            self.ai_status_text.config(state='disabled')
    
    def browse_audio_file(self):
        """Browse for audio file to analyze"""
        file_path = filedialog.askopenfilename(
            title="Select Audio File for Analysis",
            filetypes=[("Audio files", "*.wav *.mp3 *.flac")]
        )
        if file_path:
            self.selected_file_var.set(os.path.basename(file_path))
            self.selected_audio_file = file_path
    
    def select_trained_sample(self):
        """Select from trained samples"""
        if os.path.exists("trained_egyptian_samples"):
            files = [f for f in os.listdir("trained_egyptian_samples") if f.endswith('.wav')]
            if files:
                # Create selection dialog
                selection_window = tk.Toplevel(self.root)
                selection_window.title("Select Trained Sample")
                selection_window.geometry("400x300")
                selection_window.configure(bg='#2a2a2a')
                
                tk.Label(selection_window, text="🎤 Select Trained Sample", 
                        font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2a2a2a').pack(pady=10)
                
                listbox = tk.Listbox(selection_window, bg='#3a3a3a', fg='white', 
                                    font=('Arial', 10), height=10)
                listbox.pack(fill='both', expand=True, padx=10, pady=10)
                
                for file in files:
                    listbox.insert(tk.END, file)
                
                def select_file():
                    selection = listbox.curselection()
                    if selection:
                        selected_file = files[selection[0]]
                        file_path = os.path.join("trained_egyptian_samples", selected_file)
                        self.selected_file_var.set(selected_file)
                        self.selected_audio_file = file_path
                        selection_window.destroy()
                
                tk.Button(selection_window, text="✅ Select", command=select_file, 
                         bg='#00aa44', fg='white').pack(pady=10)
    
    def analyze_voice(self):
        """Analyze selected voice file"""
        if not hasattr(self, 'selected_audio_file'):
            messagebox.showwarning("Warning", "Please select an audio file first")
            return
        
        if not self.ai_cloning:
            messagebox.showerror("Error", "AI system not initialized")
            return
        
        def analyze():
            try:
                self.root.after(0, lambda: self.update_analysis_display("🔍 Analyzing voice characteristics..."))
                
                analysis = self.ai_cloning.analyze_voice_characteristics(self.selected_audio_file)
                self.current_analysis = analysis
                
                if analysis:
                    self.root.after(0, lambda: self.display_analysis_results(analysis))
                else:
                    self.root.after(0, lambda: self.update_analysis_display("❌ Voice analysis failed"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.update_analysis_display(f"❌ Analysis error: {str(e)}"))
        
        threading.Thread(target=analyze, daemon=True).start()
    
    def display_analysis_results(self, analysis):
        """Display voice analysis results"""
        self.analysis_text.delete(1.0, tk.END)
        
        # Format analysis results
        result_text = f"🔍 AI Voice Analysis Results\n"
        result_text += f"=" * 50 + "\n\n"
        
        # Quality metrics
        quality = analysis.get("quality_metrics", {})
        result_text += f"📊 Quality Metrics:\n"
        result_text += f"  Overall Score: {quality.get('overall_quality_score', 0):.1f}/100\n"
        result_text += f"  SNR: {quality.get('snr_db', 0):.1f} dB\n"
        result_text += f"  Duration: {quality.get('duration_seconds', 0):.1f} seconds\n\n"
        
        # Voice characteristics
        speaker = analysis.get("speaker_characteristics", {})
        result_text += f"🎭 Voice Characteristics:\n"
        result_text += f"  Voice Type: {speaker.get('voice_type', 'Unknown')}\n"
        
        f0_stats = speaker.get("fundamental_frequency", {})
        result_text += f"  Pitch Mean: {f0_stats.get('mean', 0):.1f} Hz\n"
        result_text += f"  Pitch Range: {f0_stats.get('range', 0):.1f} Hz\n\n"
        
        # Emotion analysis
        emotion = analysis.get("emotion_analysis", {})
        result_text += f"😊 Emotion Analysis:\n"
        result_text += f"  Detected Emotion: {emotion.get('emotion', 'Unknown')}\n"
        result_text += f"  Confidence: {emotion.get('confidence', 0):.2f}\n\n"
        
        # Accent analysis
        accent = analysis.get("accent_analysis", {})
        result_text += f"🇪🇬 Accent Analysis:\n"
        result_text += f"  Predicted Accent: {accent.get('predicted_accent', 'Unknown')}\n"
        result_text += f"  Egyptian Score: {accent.get('egyptian_score', 0):.2f}\n"
        result_text += f"  Confidence: {accent.get('confidence', 0):.1f}%\n\n"
        
        # AI recommendations
        recommendations = analysis.get("cloning_recommendations", [])
        if recommendations:
            result_text += f"💡 AI Recommendations:\n"
            for i, rec in enumerate(recommendations, 1):
                result_text += f"  {i}. {rec.get('recommendation', 'N/A')}\n"
                result_text += f"     Priority: {rec.get('priority', 'Unknown')}\n"
        
        self.analysis_text.insert(1.0, result_text)
        
        # Update parameters display
        if hasattr(self, 'params_text'):
            self.update_parameters_display(analysis)
    
    def update_analysis_display(self, message):
        """Update analysis display with message"""
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(1.0, message)
    
    def update_parameters_display(self, analysis):
        """Update AI-optimized parameters display"""
        if not self.ai_cloning:
            return
        
        try:
            params = self.ai_cloning.generate_optimized_parameters(analysis)
            
            self.params_text.config(state='normal')
            self.params_text.delete(1.0, tk.END)
            
            params_text = "⚙️ AI-Optimized Parameters:\n"
            for key, value in params.items():
                params_text += f"  {key}: {value}\n"
            
            self.params_text.insert(1.0, params_text)
            self.params_text.config(state='disabled')
            
        except Exception as e:
            self.params_text.config(state='normal')
            self.params_text.delete(1.0, tk.END)
            self.params_text.insert(1.0, f"⚠️ Parameter optimization error: {str(e)}")
            self.params_text.config(state='disabled')
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🤖 Starting AI-Enhanced Voice Cloning GUI...")
    app = AIEnhancedCloningGUI()
    app.run()

if __name__ == "__main__":
    main()
