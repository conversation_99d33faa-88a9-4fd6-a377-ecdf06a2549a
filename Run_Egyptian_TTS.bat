@echo off
echo 🇪🇬 Starting Egyptian TTS GUI with Emotions...
echo ================================================

REM Change to the project directory
cd /d "C:\Users\<USER>\Pictures\New folder\xtts-project"

REM Activate the XTTS environment and run the GUI
C:\Users\<USER>\miniconda3\envs\xtts\python.exe xtts_gui_egyptian_emotions.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo ❌ Error occurred. Press any key to close...
    pause >nul
)
