@echo off
echo Starting XTTS Voice Generator (Updated May 2025)...

:: Activate virtual environment
echo Activating Python 3.10 virtual environment...
call xtts_venv_310\Scripts\activate.bat

:: Create necessary directories
if not exist samples mkdir samples
if not exist output mkdir output
if not exist models mkdir models

echo Starting XTTS GUI...
python xtts_gui.py

:: Deactivate virtual environment when done
call xtts_venv_310\Scripts\deactivate.bat
