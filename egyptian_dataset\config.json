{"model_name": "xtts", "run_name": "egyptian_xtts", "run_description": "XTTS fine-tuned on Egyptian Arabic", "datasets": [{"name": "egyptian_dataset", "path": "./egyptian_dataset/", "meta_file_train": "metadata.csv", "language": "ar"}], "model_args": {"gpt_batch_size": 1, "enable_redaction": false, "kv_cache": true, "gpt_max_audio_len": 229376, "gpt_max_text_len": 200, "gpt_max_new_tokens": 1024, "gpt_min_audio_len": 22050, "mel_norm_file": null, "dvae_checkpoint": null, "xtts_checkpoint": null, "tokenizer_file": null, "gpt_num_audio_tokens": 1024, "gpt_start_audio_token": 1024, "gpt_stop_audio_token": 1025, "gpt_use_masking_gt_prompt_approach": true, "gpt_use_perceiver_resampler": true}, "audio": {"sample_rate": 22050, "output_sample_rate": 24000}, "batch_size": 2, "eval_batch_size": 1, "num_loader_workers": 0, "num_eval_loader_workers": 0, "run_eval": true, "test_delay_epochs": 5, "epochs": 100, "save_step": 1000, "eval_step": 1000, "log_step": 100, "save_n_checkpoints": 5, "lr": 5e-06, "weight_decay": 1e-06, "optimizer": "AdamW", "scheduler": "MultiStepLR", "lr_scheduler_params": {"milestones": [50000, 150000, 300000], "gamma": 0.5, "last_epoch": -1}, "output_path": "./egyptian_xtts_training/", "logger": "tensorboard", "logger_uri": null, "mixed_precision": false, "distributed_backend": "nccl", "distributed_url": "tcp://localhost:54321", "restore_path": null, "resume": false, "run_eval_steps": null, "save_all_best": false, "save_checkpoints": true, "print_step": 25, "plot_step": 100, "model_param_stats": false, "early_stop_patience": 10, "early_stop_threshold": 0.5, "use_phonemes": false, "phonemizer": null, "phoneme_language": null, "compute_input_seq_cache": true, "precompute_num_workers": 0, "start_by_longest": false}