#!/usr/bin/env python3
"""
Egyptian Data Preparation Script
Prepare audio files and create training splits
"""

import os
import pandas as pd
import librosa
import soundfile as sf
from sklearn.model_selection import train_test_split

def prepare_audio_files():
    """Process and normalize audio files"""
    print("🎵 Processing audio files...")
    
    wavs_dir = "egyptian_dataset/wavs"
    if not os.path.exists(wavs_dir):
        print(f"❌ Directory not found: {wavs_dir}")
        return
    
    processed_count = 0
    
    for filename in os.listdir(wavs_dir):
        if filename.endswith('.wav'):
            filepath = os.path.join(wavs_dir, filename)
            
            # Load audio
            audio, sr = librosa.load(filepath, sr=22050)
            
            # Normalize audio
            audio = librosa.util.normalize(audio)
            
            # Save normalized audio
            sf.write(filepath, audio, 22050)
            processed_count += 1
    
    print(f"✅ Processed {processed_count} audio files")

def create_train_eval_split():
    """Create training and evaluation splits"""
    print("📝 Creating train/eval splits...")
    
    metadata_path = "egyptian_dataset/metadata.csv"
    if not os.path.exists(metadata_path):
        print(f"❌ Metadata file not found: {metadata_path}")
        return
    
    # Load metadata
    df = pd.read_csv(metadata_path, sep='|')
    
    # Split data (80% train, 20% eval)
    train_df, eval_df = train_test_split(df, test_size=0.2, random_state=42)
    
    # Save splits
    train_df.to_csv("egyptian_dataset/train.txt", sep='|', index=False)
    eval_df.to_csv("egyptian_dataset/eval.txt", sep='|', index=False)
    
    print(f"✅ Training samples: {len(train_df)}")
    print(f"✅ Evaluation samples: {len(eval_df)}")

def validate_dataset():
    """Validate the dataset"""
    print("🔍 Validating dataset...")
    
    issues = []
    
    # Check metadata
    metadata_path = "egyptian_dataset/metadata.csv"
    if os.path.exists(metadata_path):
        df = pd.read_csv(metadata_path, sep='|')
        
        for idx, row in df.iterrows():
            audio_file = f"egyptian_dataset/wavs/{row['audio_file']}"
            
            if not os.path.exists(audio_file):
                issues.append(f"Missing audio: {row['audio_file']}")
            
            if pd.isna(row['text']) or len(row['text'].strip()) == 0:
                issues.append(f"Empty text for: {row['audio_file']}")
    
    if issues:
        print("❌ Dataset issues found:")
        for issue in issues[:10]:  # Show first 10 issues
            print(f"  • {issue}")
        if len(issues) > 10:
            print(f"  ... and {len(issues) - 10} more issues")
    else:
        print("✅ Dataset validation passed!")

def main():
    """Main data preparation function"""
    print("🇪🇬 Egyptian Dataset Preparation")
    print("=" * 35)
    
    prepare_audio_files()
    create_train_eval_split()
    validate_dataset()
    
    print("
🎯 Dataset ready for training!")

if __name__ == "__main__":
    main()
