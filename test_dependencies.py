#!/usr/bin/env python3
"""
Test script to check if required dependencies are available
"""

import sys
import importlib

def test_import(module_name, description=""):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        print(f"✓ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"✗ {module_name} - {description} - ERROR: {e}")
        return False

def main():
    print("Testing dependencies for XTTS GUI...")
    print("=" * 50)
    
    # Test basic Python modules
    basic_modules = [
        ("tkinter", "GUI framework"),
        ("threading", "Threading support"),
        ("os", "Operating system interface"),
        ("sys", "System-specific parameters"),
        ("time", "Time-related functions"),
        ("json", "JSON encoder/decoder"),
        ("logging", "Logging facility"),
        ("subprocess", "Subprocess management"),
        ("urllib.request", "URL handling"),
    ]
    
    print("\nBasic Python modules:")
    basic_ok = all(test_import(module, desc) for module, desc in basic_modules)
    
    # Test scientific computing modules
    scientific_modules = [
        ("numpy", "Numerical computing"),
        ("scipy", "Scientific computing"),
        ("torch", "PyTorch deep learning"),
        ("torchaudio", "PyTorch audio processing"),
    ]
    
    print("\nScientific computing modules:")
    scientific_ok = all(test_import(module, desc) for module, desc in scientific_modules)
    
    # Test audio/multimedia modules
    multimedia_modules = [
        ("pygame", "Audio playback"),
    ]
    
    print("\nMultimedia modules:")
    multimedia_ok = all(test_import(module, desc) for module, desc in multimedia_modules)
    
    # Test TTS modules (these might fail)
    tts_modules = [
        ("TTS", "Coqui TTS library"),
        ("TTS.api", "TTS API"),
        ("TTS.utils.manage", "TTS model management"),
    ]
    
    print("\nTTS modules (optional - may fail):")
    tts_ok = all(test_import(module, desc) for module, desc in tts_modules)
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Basic modules: {'✓ OK' if basic_ok else '✗ FAILED'}")
    print(f"Scientific modules: {'✓ OK' if scientific_ok else '✗ FAILED'}")
    print(f"Multimedia modules: {'✓ OK' if multimedia_ok else '✗ FAILED'}")
    print(f"TTS modules: {'✓ OK' if tts_ok else '✗ FAILED (expected)'}")
    
    if basic_ok and scientific_ok and multimedia_ok:
        print("\n✓ Core dependencies are available!")
        if not tts_ok:
            print("⚠ TTS library needs to be installed for full functionality")
    else:
        print("\n✗ Some core dependencies are missing!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
