#!/usr/bin/env python3
"""
Demo script showing Egyptian accent and emotion controls
This script demonstrates the different emotion presets and their effects
"""

import os
import time
from TTS.api import TTS

def demo_egyptian_emotions():
    """Demo the Egyptian accent with different emotions"""
    
    print("🎭 XTTS Egyptian Accent & Emotion Demo")
    print("=" * 50)
    
    # Initialize TTS
    print("Loading XTTS model...")
    tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=True)
    
    # Sample Egyptian text
    egyptian_text = "أهلاً وسهلاً! إزيك النهاردة؟ أنا بتكلم بالمصري الأصيل. القاهرة مدينة جميلة جداً والناس فيها طيبين قوي."
    
    # Voice sample path (you need to provide this)
    voice_sample = input("Enter path to Egyptian voice sample (WAV file): ")
    
    if not os.path.exists(voice_sample):
        print("❌ Voice sample not found!")
        return
    
    # Emotion presets
    emotions = {
        "Neutral": {"temperature": 0.7, "length_penalty": 1.0, "repetition_penalty": 2.0},
        "Happy": {"temperature": 0.9, "length_penalty": 0.8, "repetition_penalty": 1.8},
        "Sad": {"temperature": 0.5, "length_penalty": 1.2, "repetition_penalty": 2.2},
        "Angry": {"temperature": 1.0, "length_penalty": 0.7, "repetition_penalty": 1.5},
        "Excited": {"temperature": 1.1, "length_penalty": 0.6, "repetition_penalty": 1.6},
        "Calm": {"temperature": 0.4, "length_penalty": 1.3, "repetition_penalty": 2.5},
        "Dramatic": {"temperature": 1.2, "length_penalty": 0.5, "repetition_penalty": 1.4}
    }
    
    # Create output directory
    os.makedirs("demo_output", exist_ok=True)
    
    print(f"\n🎤 Generating speech with different emotions...")
    print(f"📝 Text: {egyptian_text}")
    print(f"🎵 Voice sample: {os.path.basename(voice_sample)}")
    
    for emotion_name, params in emotions.items():
        print(f"\n🎭 Generating {emotion_name} emotion...")
        print(f"   Temperature: {params['temperature']}")
        print(f"   Length Penalty: {params['length_penalty']}")
        print(f"   Repetition Penalty: {params['repetition_penalty']}")
        
        output_file = f"demo_output/egyptian_{emotion_name.lower()}.wav"
        
        try:
            start_time = time.time()
            
            # Generate with emotion parameters
            # Note: For advanced parameters, you'd need to use the model directly
            # This is a simplified version using the TTS API
            tts.tts_to_file(
                text=egyptian_text,
                speaker_wav=voice_sample,
                language="ar",
                file_path=output_file
            )
            
            generation_time = time.time() - start_time
            print(f"   ✅ Generated in {generation_time:.2f}s -> {output_file}")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print(f"\n🎉 Demo complete! Check the 'demo_output' folder for generated files.")
    print(f"\n🔊 You can now compare the different emotional expressions:")
    
    for emotion_name in emotions.keys():
        output_file = f"demo_output/egyptian_{emotion_name.lower()}.wav"
        if os.path.exists(output_file):
            print(f"   • {emotion_name}: {output_file}")

def demo_accents():
    """Demo different Arabic accents"""
    
    print("\n🌍 Arabic Accent Demo")
    print("=" * 30)
    
    # Sample texts for different accents
    accent_texts = {
        "Egyptian": "أهلاً وسهلاً! إزيك النهاردة؟ أنا بتكلم بالمصري الأصيل.",
        "Levantine": "مرحبا! كيفك اليوم؟ أنا بحكي باللهجة الشامية.",
        "Gulf": "السلام عليكم! شلونك اليوم؟ أنا أتكلم باللهجة الخليجية.",
        "Standard": "السلام عليكم ورحمة الله وبركاته. كيف حالكم اليوم؟"
    }
    
    print("📚 Sample texts for different accents:")
    for accent, text in accent_texts.items():
        print(f"\n🏛️ {accent}:")
        print(f"   Arabic: {text}")
        
        # Simple translations
        translations = {
            "Egyptian": "Hello and welcome! How are you today? I speak authentic Egyptian.",
            "Levantine": "Hello! How are you today? I speak the Levantine dialect.",
            "Gulf": "Peace be upon you! How are you today? I speak the Gulf dialect.",
            "Standard": "Peace be upon you and God's mercy and blessings. How are you today?"
        }
        print(f"   English: {translations[accent]}")

def main():
    """Main demo function"""
    print("🎭 XTTS Egyptian Accent & Emotion Demo")
    print("Choose demo type:")
    print("1. Emotion Demo (requires voice sample)")
    print("2. Accent Text Examples")
    print("3. Both")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        demo_egyptian_emotions()
    elif choice == "2":
        demo_accents()
    elif choice == "3":
        demo_accents()
        print("\n" + "="*60)
        demo_egyptian_emotions()
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    main()
