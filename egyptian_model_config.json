{"model_name": "Egyptian XTTS Model", "version": "1.0", "description": "XTTS model trained for Egyptian Arabic pronunciation", "training_data": {"total_samples": 39, "pronunciation_focus": ["ج sound as 'g' (not 'j')", "ق sound as glottal stop", "Egyptian expressions and idioms", "Educational content pronunciation", "Natural Egyptian intonation"]}, "optimal_settings": {"temperature": 0.9, "length_penalty": 0.9, "repetition_penalty": 1.9, "top_k": 60, "top_p": 0.85, "speed": 1.0}, "pronunciation_patterns": {"ج": {"standard": "j_sound", "egyptian": "g_sound", "examples": ["جميل → gameel", "جديد → gedeed"]}, "ق": {"standard": "q_sound", "egyptian": "glottal_stop", "examples": ["قال → aal", "قوي → awi"]}, "ث": {"standard": "th_sound", "egyptian": "s_or_t_sound", "examples": ["ثلاثة → talata"]}, "ذ": {"standard": "dh_sound", "egyptian": "z_or_d_sound", "examples": ["ذه<PERSON> → dahab"]}}, "usage_instructions": {"voice_samples": "Use files from trained_egyptian_samples/", "settings": "Apply optimal_settings in your GUI", "text_input": "Use Egyptian expressions and vocabulary", "expected_results": "Authentic Egyptian pronunciation and accent"}, "quality_indicators": ["ج pronounced as 'g' sound", "ق pronounced as glottal stop", "Natural Egyptian intonation", "Correct stress patterns", "Authentic Egyptian expressions"]}