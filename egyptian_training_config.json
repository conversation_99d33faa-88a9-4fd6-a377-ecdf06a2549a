{"model_name": "xtts_egyptian", "run_name": "egyptian_pronunciation_model", "run_description": "XTTS fine-tuned for Egyptian Arabic pronunciation", "datasets": [{"name": "egyptian_model_training", "path": "./egyptian_model_training/", "meta_file_train": "metadata.csv", "language": "ar"}], "model_args": {"gpt_batch_size": 1, "enable_redaction": false, "kv_cache": true, "gpt_max_audio_len": 229376, "gpt_max_text_len": 200, "gpt_max_new_tokens": 1024, "gpt_min_audio_len": 22050}, "audio": {"sample_rate": 22050, "output_sample_rate": 24000}, "batch_size": 1, "eval_batch_size": 1, "num_loader_workers": 0, "num_eval_loader_workers": 0, "run_eval": true, "test_delay_epochs": 5, "epochs": 20, "save_step": 100, "eval_step": 100, "log_step": 25, "save_n_checkpoints": 5, "lr": 5e-06, "weight_decay": 1e-06, "optimizer": "AdamW", "output_path": "./egyptian_trained_model/", "training_focus": "egyptian_pronunciation", "num_samples": 39, "mixed_precision": false, "use_phonemes": false, "compute_input_seq_cache": true}