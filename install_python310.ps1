# Script to download and install Python 3.10 for XTTS

$pythonUrl = "https://www.python.org/ftp/python/3.10.11/python-3.10.11-amd64.exe"
$installerPath = "$env:TEMP\python-3.10.11-amd64.exe"
$pythonInstallDir = "$env:LOCALAPPDATA\Programs\Python\Python310"

Write-Host "Downloading Python 3.10.11..." -ForegroundColor Green
Invoke-WebRequest -Uri $pythonUrl -OutFile $installerPath

Write-Host "Installing Python 3.10.11..." -ForegroundColor Green
Start-Process -FilePath $installerPath -ArgumentList "/quiet", "InstallAllUsers=0", "PrependPath=1", "Include_test=0", "Include_pip=1", "Include_doc=0", "Include_launcher=1", "InstallLauncherAllUsers=0", "CompileAll=1", "TargetDir=$pythonInstallDir" -Wait

Write-Host "Cleaning up..." -ForegroundColor Green
Remove-Item -Path $installerPath -Force

Write-Host "Python 3.10.11 has been installed to: $pythonInstallDir" -ForegroundColor Green
Write-Host "Creating a new virtual environment with Python 3.10..." -ForegroundColor Green

# Create a new virtual environment with Python 3.10
& "$pythonInstallDir\python.exe" -m venv venv_py310

Write-Host "Virtual environment created at ./venv_py310" -ForegroundColor Green
Write-Host "To activate the environment, run: .\venv_py310\Scripts\Activate.ps1" -ForegroundColor Cyan
