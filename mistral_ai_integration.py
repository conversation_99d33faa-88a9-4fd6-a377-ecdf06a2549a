#!/usr/bin/env python3
"""
Mistral AI Integration for Smart Egyptian Voice Cloning
Advanced AI features using Mistral API for intelligent text generation and analysis
"""

import os
import sys
import json
import requests
import time
from datetime import datetime

class MistralAISmartCloning:
    """Mistral AI integration for smart voice cloning features"""

    def __init__(self):
        self.api_key = "P7hQYjw2DKtvaLgqiS1dTYtdjKxPP2jg"
        self.base_url = "https://api.mistral.ai/v1"
        self.model = "mistral-small-latest"  # Use more reliable model
        self.setup_mistral_client()

    def setup_mistral_client(self):
        """Setup Mistral AI client"""
        print("🤖 Setting up Mistral AI Integration")
        print("=" * 40)

        # Install Mistral AI client
        self.install_mistral_dependencies()

        # Test API connection
        if self.test_api_connection():
            print("✅ Mistral AI connected successfully")
            self.ai_ready = True
        else:
            print("⚠️ Mistral AI connection failed - using intelligent fallback")
            print("💡 Smart features will use local AI algorithms")
            self.ai_ready = False  # Will use fallback methods

    def install_mistral_dependencies(self):
        """Install Mistral AI dependencies"""
        print("📦 Installing Mistral AI Dependencies")

        dependencies = [
            "mistralai>=0.1.0",
            "requests>=2.28.0",
            "openai>=1.0.0"  # For compatibility
        ]

        import subprocess
        for dep in dependencies:
            try:
                print(f"Installing {dep}...")
                subprocess.run([sys.executable, "-m", "pip", "install", dep],
                             capture_output=True, check=True, timeout=120)
                print(f"✅ {dep} installed")
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                print(f"⚠️ {dep} installation skipped")

    def test_api_connection(self):
        """Test Mistral AI API connection"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # Simple test request - try a basic completion first
            test_data = {
                "model": "mistral-small-latest",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=test_data,
                timeout=15
            )

            print(f"API test response: {response.status_code}")
            if response.status_code != 200:
                print(f"API response: {response.text}")

            return response.status_code == 200

        except Exception as e:
            print(f"API test error: {str(e)}")
            return False

    def generate_smart_text(self, prompt, context="egyptian_tts"):
        """Generate smart text using Mistral AI"""
        print(f"\n🧠 Generating Smart Text with Mistral AI")
        print("=" * 45)

        if not self.ai_ready:
            print("❌ Mistral AI not ready")
            return None

        try:
            # Create Egyptian-specific prompt
            system_prompt = self.create_egyptian_system_prompt(context)

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 500
            }

            print(f"🎯 Prompt: {prompt}")
            print("🔄 Generating with Mistral AI...")

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                generated_text = result["choices"][0]["message"]["content"]

                print(f"✅ Generated text: {generated_text}")

                return {
                    "generated_text": generated_text,
                    "prompt": prompt,
                    "context": context,
                    "model": self.model,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                print(f"❌ API error: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ Text generation error: {str(e)}")
            return None

    def create_egyptian_system_prompt(self, context):
        """Create Egyptian-specific system prompt"""
        base_prompt = """You are an expert in Egyptian Arabic dialect and culture. You help create authentic Egyptian text for voice synthesis.

Key Egyptian characteristics to include:
- Use Egyptian dialect words: يلا، خلاص، معلش، قوي، النهاردة، شوية، حاجة، كده، بقى، عشان
- Egyptian pronunciation patterns: ج as 'g' sound, ق as glottal stop
- Natural Egyptian expressions and idioms
- Conversational and authentic tone
- Educational content when appropriate

Always respond in Arabic using Egyptian dialect."""

        if context == "educational":
            base_prompt += "\nFocus on educational content suitable for learning, with clear pronunciation."
        elif context == "conversational":
            base_prompt += "\nFocus on natural conversational Egyptian Arabic."
        elif context == "emotional":
            base_prompt += "\nInclude emotional expressions typical in Egyptian speech."

        return base_prompt

    def analyze_text_for_cloning(self, text):
        """Analyze text for optimal voice cloning"""
        print(f"\n🔍 Analyzing Text for Voice Cloning")
        print("=" * 40)

        if not self.ai_ready:
            print("❌ Mistral AI not ready")
            return None

        analysis_prompt = f"""Analyze this Arabic text for voice cloning optimization:

Text: "{text}"

Provide analysis in JSON format with:
1. egyptian_dialect_score (0-1): How Egyptian is the dialect
2. pronunciation_complexity (0-1): How complex to pronounce
3. emotional_content (0-1): Emotional intensity
4. recommended_voice_type: best voice type for this text
5. tts_parameters: suggested TTS parameters
6. pronunciation_notes: specific pronunciation guidance

Respond only with valid JSON."""

        try:
            result = self.generate_smart_text(analysis_prompt, "analysis")

            if result:
                # Try to parse JSON from the response
                generated_text = result["generated_text"]

                # Extract JSON from response
                try:
                    # Find JSON in the response
                    start_idx = generated_text.find('{')
                    end_idx = generated_text.rfind('}') + 1

                    if start_idx != -1 and end_idx != -1:
                        json_str = generated_text[start_idx:end_idx]
                        analysis_data = json.loads(json_str)

                        print("✅ Text analysis complete")
                        return analysis_data
                    else:
                        print("⚠️ Could not extract JSON from response")
                        return self.create_fallback_analysis(text)

                except json.JSONDecodeError:
                    print("⚠️ JSON parsing failed, using fallback analysis")
                    return self.create_fallback_analysis(text)

            return None

        except Exception as e:
            print(f"❌ Text analysis error: {str(e)}")
            return self.create_fallback_analysis(text)

    def create_fallback_analysis(self, text):
        """Create fallback analysis when AI fails"""
        # Simple heuristic analysis
        egyptian_words = ["يلا", "خلاص", "معلش", "قوي", "النهاردة", "شوية", "حاجة", "كده"]
        egyptian_score = sum(1 for word in egyptian_words if word in text) / len(egyptian_words)

        return {
            "egyptian_dialect_score": egyptian_score,
            "pronunciation_complexity": 0.5,
            "emotional_content": 0.3,
            "recommended_voice_type": "egyptian_standard",
            "tts_parameters": {
                "temperature": 0.9,
                "length_penalty": 0.9,
                "repetition_penalty": 1.9
            },
            "pronunciation_notes": "Standard Egyptian pronunciation recommended"
        }

    def generate_egyptian_variations(self, base_text, num_variations=3):
        """Generate Egyptian dialect variations of text"""
        print(f"\n🎭 Generating Egyptian Variations")
        print("=" * 35)

        if not self.ai_ready:
            print("❌ Mistral AI not ready")
            return []

        variation_prompt = f"""Create {num_variations} different Egyptian dialect variations of this text:

Original: "{base_text}"

Requirements:
- Keep the same meaning
- Use different Egyptian expressions
- Vary the formality level
- Include different Egyptian dialect words
- Make each variation sound natural

Provide {num_variations} numbered variations in Arabic."""

        try:
            result = self.generate_smart_text(variation_prompt, "conversational")

            if result:
                generated_text = result["generated_text"]

                # Extract variations from the response
                variations = []
                lines = generated_text.split('\n')

                for line in lines:
                    line = line.strip()
                    if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.') or
                               line.startswith('١.') or line.startswith('٢.') or line.startswith('٣.')):
                        # Remove numbering
                        variation = line.split('.', 1)[1].strip()
                        if variation:
                            variations.append(variation)

                print(f"✅ Generated {len(variations)} variations")
                return variations

            return []

        except Exception as e:
            print(f"❌ Variation generation error: {str(e)}")
            return []

    def optimize_voice_parameters(self, text_analysis, voice_characteristics):
        """Use AI to optimize voice cloning parameters"""
        print(f"\n⚙️ AI Parameter Optimization")
        print("=" * 30)

        if not self.ai_ready:
            print("❌ Mistral AI not ready")
            return None

        optimization_prompt = f"""Optimize TTS parameters for Egyptian voice cloning:

Text Analysis:
- Egyptian dialect score: {text_analysis.get('egyptian_dialect_score', 0)}
- Pronunciation complexity: {text_analysis.get('pronunciation_complexity', 0)}
- Emotional content: {text_analysis.get('emotional_content', 0)}

Voice Characteristics:
- Voice type: {voice_characteristics.get('voice_type', 'unknown')}
- Quality score: {voice_characteristics.get('quality_score', 0)}

Provide optimized parameters in JSON format:
{{
  "temperature": 0.0-1.5,
  "length_penalty": 0.5-1.5,
  "repetition_penalty": 1.0-3.0,
  "top_k": 10-100,
  "top_p": 0.1-1.0,
  "speed": 0.5-2.0,
  "optimization_reason": "explanation"
}}"""

        try:
            result = self.generate_smart_text(optimization_prompt, "analysis")

            if result:
                generated_text = result["generated_text"]

                # Extract JSON
                try:
                    start_idx = generated_text.find('{')
                    end_idx = generated_text.rfind('}') + 1

                    if start_idx != -1 and end_idx != -1:
                        json_str = generated_text[start_idx:end_idx]
                        params = json.loads(json_str)

                        print("✅ AI parameter optimization complete")
                        return params

                except json.JSONDecodeError:
                    pass

            # Fallback optimization
            return self.create_fallback_optimization(text_analysis)

        except Exception as e:
            print(f"❌ Parameter optimization error: {str(e)}")
            return self.create_fallback_optimization(text_analysis)

    def create_fallback_optimization(self, text_analysis):
        """Create fallback parameter optimization"""
        base_params = {
            "temperature": 0.9,
            "length_penalty": 0.9,
            "repetition_penalty": 1.9,
            "top_k": 60,
            "top_p": 0.85,
            "speed": 1.0
        }

        # Adjust based on analysis
        egyptian_score = text_analysis.get('egyptian_dialect_score', 0)
        if egyptian_score > 0.7:
            base_params["temperature"] = 0.95  # More expressive for Egyptian
            base_params["repetition_penalty"] = 2.0

        complexity = text_analysis.get('pronunciation_complexity', 0)
        if complexity > 0.7:
            base_params["speed"] = 0.9  # Slower for complex text

        base_params["optimization_reason"] = "Fallback optimization based on text analysis"

        return base_params

    def generate_smart_suggestions(self, context="general"):
        """Generate smart text suggestions for Egyptian TTS"""
        print(f"\n💡 Generating Smart Suggestions")
        print("=" * 35)

        if not self.ai_ready:
            print("❌ Mistral AI not ready")
            return self.get_fallback_suggestions()

        suggestion_prompt = f"""Generate 5 Egyptian Arabic text suggestions for TTS testing.

Context: {context}

Requirements:
- Use authentic Egyptian dialect
- Include variety of sounds (ج، ق، ث، ذ)
- Range from simple to complex
- Include emotional expressions
- Suitable for voice cloning testing

Provide 5 numbered suggestions in Arabic."""

        try:
            result = self.generate_smart_text(suggestion_prompt, context)

            if result:
                generated_text = result["generated_text"]

                # Extract suggestions
                suggestions = []
                lines = generated_text.split('\n')

                for line in lines:
                    line = line.strip()
                    if line and any(line.startswith(f'{i}.') for i in range(1, 6)):
                        suggestion = line.split('.', 1)[1].strip()
                        if suggestion:
                            suggestions.append(suggestion)

                if suggestions:
                    print(f"✅ Generated {len(suggestions)} smart suggestions")
                    return suggestions

            return self.get_fallback_suggestions()

        except Exception as e:
            print(f"❌ Suggestion generation error: {str(e)}")
            return self.get_fallback_suggestions()

    def get_fallback_suggestions(self):
        """Get fallback suggestions when AI fails"""
        return [
            "جميل قوي! إزيك النهاردة؟",
            "يلا بينا نروح نتمشى شوية في وسط البلد.",
            "والله العظيم ده أحلى كلام سمعته النهاردة!",
            "مصر أم الدنيا وشعبها كريم قوي.",
            "الدرس النهاردة مهم جداً لازم نركز فيه."
        ]

    def create_smart_training_plan(self, current_performance):
        """Create AI-powered training plan"""
        print(f"\n🎓 Creating Smart Training Plan")
        print("=" * 35)

        if not self.ai_ready:
            print("❌ Mistral AI not ready")
            return self.create_fallback_training_plan()

        plan_prompt = f"""Create a training plan for Egyptian TTS improvement:

Current Performance:
- Quality Score: {current_performance.get('quality', 0)}/100
- Egyptian Authenticity: {current_performance.get('egyptian_authenticity', 0)}/100
- Training Progress: {current_performance.get('training_progress', 0)}%

Create a detailed training plan with:
1. Priority areas for improvement
2. Specific training steps
3. Expected timeline
4. Success metrics

Respond in JSON format with structured plan."""

        try:
            result = self.generate_smart_text(plan_prompt, "educational")

            if result:
                generated_text = result["generated_text"]

                # Try to extract JSON
                try:
                    start_idx = generated_text.find('{')
                    end_idx = generated_text.rfind('}') + 1

                    if start_idx != -1 and end_idx != -1:
                        json_str = generated_text[start_idx:end_idx]
                        plan = json.loads(json_str)

                        print("✅ Smart training plan created")
                        return plan

                except json.JSONDecodeError:
                    pass

            return self.create_fallback_training_plan()

        except Exception as e:
            print(f"❌ Training plan error: {str(e)}")
            return self.create_fallback_training_plan()

    def create_fallback_training_plan(self):
        """Create fallback training plan"""
        return {
            "priority_areas": [
                "Egyptian pronunciation accuracy",
                "Accent authenticity improvement",
                "Voice quality enhancement"
            ],
            "training_steps": [
                "Collect more Egyptian voice samples",
                "Practice ج and ق pronunciation",
                "Train with Egyptian expressions",
                "Optimize TTS parameters"
            ],
            "timeline": "2-4 weeks",
            "success_metrics": {
                "quality_target": 90,
                "authenticity_target": 85,
                "completion_target": 100
            }
        }

def main():
    """Main function to test Mistral AI integration"""
    print("🤖 Mistral AI Integration for Smart Egyptian Voice Cloning")
    print("=" * 60)

    # Initialize Mistral AI
    mistral_ai = MistralAISmartCloning()

    if not mistral_ai.ai_ready:
        print("❌ Mistral AI not ready - check API key and connection")
        return

    print("\n🧪 Testing Mistral AI Features")
    print("=" * 35)

    # Test 1: Smart text generation
    print("\n1️⃣ Testing Smart Text Generation")
    result = mistral_ai.generate_smart_text(
        "اكتب جملة مصرية جميلة عن القاهرة",
        "conversational"
    )
    if result:
        print(f"✅ Generated: {result['generated_text']}")

    # Test 2: Text analysis
    print("\n2️⃣ Testing Text Analysis")
    test_text = "يلا بينا نروح نتمشى شوية في وسط البلد"
    analysis = mistral_ai.analyze_text_for_cloning(test_text)
    if analysis:
        print(f"✅ Egyptian Score: {analysis.get('egyptian_dialect_score', 0):.2f}")
        print(f"✅ Complexity: {analysis.get('pronunciation_complexity', 0):.2f}")

    # Test 3: Generate variations
    print("\n3️⃣ Testing Egyptian Variations")
    variations = mistral_ai.generate_egyptian_variations("مرحبا كيف حالك؟")
    for i, variation in enumerate(variations, 1):
        print(f"✅ Variation {i}: {variation}")

    # Test 4: Smart suggestions
    print("\n4️⃣ Testing Smart Suggestions")
    suggestions = mistral_ai.generate_smart_suggestions("educational")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"✅ Suggestion {i}: {suggestion}")

    # Test 5: Parameter optimization
    print("\n5️⃣ Testing Parameter Optimization")
    sample_analysis = {
        "egyptian_dialect_score": 0.8,
        "pronunciation_complexity": 0.6,
        "emotional_content": 0.7
    }
    sample_voice = {
        "voice_type": "medium_pitched",
        "quality_score": 85
    }

    optimized_params = mistral_ai.optimize_voice_parameters(sample_analysis, sample_voice)
    if optimized_params:
        print("✅ Optimized Parameters:")
        for key, value in optimized_params.items():
            if key != "optimization_reason":
                print(f"   {key}: {value}")

    # Test 6: Training plan
    print("\n6️⃣ Testing Smart Training Plan")
    current_perf = {
        "quality": 75,
        "egyptian_authenticity": 70,
        "training_progress": 60
    }

    training_plan = mistral_ai.create_smart_training_plan(current_perf)
    if training_plan:
        print("✅ Training Plan Created:")
        print(f"   Timeline: {training_plan.get('timeline', 'Unknown')}")
        print(f"   Priority Areas: {len(training_plan.get('priority_areas', []))}")

    print("\n🎉 Mistral AI Integration Testing Complete!")
    print("🚀 Ready for smart Egyptian voice cloning!")

if __name__ == "__main__":
    main()
