﻿**********************
Windows PowerShell transcript start
Start time: 20250525000514
Username: THEDAYIMETP\omara
RunAs User: THEDAYIMETP\omara
Configuration Name: 
Machine: THEDAYIMETP (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -ExecutionPolicy Bypass -File install_xtts_full.ps1
Process ID: 28304
PSVersion: 5.1.26100.4061
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.26100.4061
BuildVersion: 10.0.26100.4061
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is xtts_install_log.txt
Step 1: Checking Python 3.10 installation...
Python 3.10 is already installed.
\nStep 2: Checking GPU and CUDA installation...
NVIDIA GPU detected!
CUDA 11.8 not found. Installing CUDA...
NOTE: CUDA installation requires admin privileges and may take some time.
You can also install CUDA manually from: https://developer.nvidia.com/cuda-11-8-0-download-archive
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> $global:?
True
**********************
Windows PowerShell transcript end
End time: 20250525000646
**********************
**********************
Windows PowerShell transcript start
Start time: 20250525000917
Username: THEDAYIMETP\omara
RunAs User: THEDAYIMETP\omara
Configuration Name: 
Machine: THEDAYIMETP (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -ExecutionPolicy Bypass -File install_xtts_full.ps1
Process ID: 31900
PSVersion: 5.1.26100.4061
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.26100.4061
BuildVersion: 10.0.26100.4061
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is xtts_install_log.txt
Step 1: Checking Python 3.10 installation...
Python 3.10 is already installed.
\nStep 2: Checking GPU and CUDA installation...
NVIDIA GPU detected!
CUDA 12.7 is already installed.
\nStep 3: Creating virtual environment...

Virtual environment 'xtts_venv' created successfully!
\nStep 4: Installing dependencies...

Installing PyTorch with CUDA support...
