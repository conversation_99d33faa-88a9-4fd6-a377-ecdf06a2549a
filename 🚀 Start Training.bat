@echo off
title Egyptian XTTS Training
color 0C

echo.
echo     🚀 Working Egyptian Training 🇪🇬
echo     =================================
echo.
echo     This will test and optimize your Egyptian accent
echo     Using your existing audio file
echo.
echo     Progress will be shown below:
echo.

REM Change to project directory
cd /d "%~dp0"

REM Run the working training
C:\Users\<USER>\miniconda3\envs\xtts\python.exe working_egyptian_training.py

echo.
echo     Training session complete!
echo     Check the generated files and results.
echo.
pause
