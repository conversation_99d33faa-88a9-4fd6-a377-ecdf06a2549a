@echo off
title Actual Egyptian Model Training
color 0A

echo.
echo     🚀 Actual Egyptian Model Training 🇪🇬
echo     ====================================
echo.
echo     This will train the XTTS model specifically for Egyptian pronunciation:
echo     • 39 training samples prepared
echo     • Fine-tune for Egyptian accent
echo     • Learn correct character pronunciation
echo     • Create custom Egyptian model
echo.
echo     ⏱️ Training time: 1-3 hours
echo     📊 Progress will be shown below
echo.

REM Change to project directory
cd /d "%~dp0"

echo     🎓 Starting actual Egyptian model training...
echo.

REM Run the actual training
C:\Users\<USER>\miniconda3\envs\xtts\python.exe train_egyptian_model.py

echo.
echo     🎉 Training session complete!
echo     📁 Check egyptian_trained_model/ for results
echo.
pause
