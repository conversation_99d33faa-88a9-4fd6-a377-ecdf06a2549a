#!/usr/bin/env python3
"""
Google AI Enhanced Voice Cloning System
Complete integration of Google AI with XTTS voice cloning
"""

import os
import sys
import json
import time
import subprocess
from datetime import datetime

class GoogleAIVoiceCloning:
    """Complete Google AI enhanced voice cloning system"""
    
    def __init__(self):
        self.setup_paths()
        self.initialize_google_ai()
        self.initialize_xtts()
        
    def setup_paths(self):
        """Setup file paths"""
        self.project_dir = os.path.dirname(os.path.abspath(__file__))
        self.output_dir = os.path.join(self.project_dir, "google_ai_outputs")
        self.samples_dir = os.path.join(self.project_dir, "trained_egyptian_samples")
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"📁 Project directory: {self.project_dir}")
        print(f"📁 Output directory: {self.output_dir}")
        print(f"📁 Samples directory: {self.samples_dir}")
    
    def initialize_google_ai(self):
        """Initialize Google AI"""
        try:
            from google_ai_integration import GoogleAISmartCloning
            self.google_ai = GoogleAISmartCloning()
            
            if self.google_ai.ai_ready:
                print("✅ Google AI initialized successfully")
                self.ai_ready = True
            else:
                print("⚠️ Google AI using fallback mode")
                self.ai_ready = False
                
        except Exception as e:
            print(f"❌ Google AI initialization error: {str(e)}")
            self.google_ai = None
            self.ai_ready = False
    
    def initialize_xtts(self):
        """Initialize XTTS system"""
        try:
            # Check if XTTS is available
            import torch
            print("✅ PyTorch available")
            
            # Try to import TTS
            try:
                from TTS.api import TTS
                print("✅ TTS library available")
                self.tts_ready = True
            except ImportError:
                print("⚠️ TTS library not found - installing...")
                self.install_tts()
                
        except Exception as e:
            print(f"❌ XTTS initialization error: {str(e)}")
            self.tts_ready = False
    
    def install_tts(self):
        """Install TTS library"""
        try:
            print("📦 Installing TTS library...")
            subprocess.run([sys.executable, "-m", "pip", "install", "TTS"], 
                         capture_output=True, check=True, timeout=300)
            print("✅ TTS library installed")
            self.tts_ready = True
        except Exception as e:
            print(f"❌ TTS installation failed: {str(e)}")
            self.tts_ready = False
    
    def get_best_reference_voice(self, text_analysis=None):
        """Get best reference voice based on AI analysis"""
        if not os.path.exists(self.samples_dir):
            print(f"❌ Samples directory not found: {self.samples_dir}")
            return None
        
        # Get available voice samples
        voice_files = [f for f in os.listdir(self.samples_dir) if f.endswith('.wav')]
        
        if not voice_files:
            print("❌ No voice samples found")
            return None
        
        # AI-based voice selection
        if text_analysis and self.ai_ready:
            egyptian_score = text_analysis.get('egyptian_dialect_score', 0)
            complexity = text_analysis.get('pronunciation_complexity', 0)
            emotion = text_analysis.get('emotional_content', 0)
            
            # Smart voice selection logic
            if egyptian_score > 0.8:
                # High Egyptian content - use ج sound samples
                preferred = [f for f in voice_files if 'ج_sound' in f]
                if preferred:
                    selected = preferred[0]
                    print(f"🎯 AI selected voice for high Egyptian content: {selected}")
                    return os.path.join(self.samples_dir, selected)
            
            if emotion > 0.6:
                # High emotional content - use expression samples
                preferred = [f for f in voice_files if 'expressions' in f]
                if preferred:
                    selected = preferred[0]
                    print(f"🎭 AI selected voice for emotional content: {selected}")
                    return os.path.join(self.samples_dir, selected)
            
            if complexity > 0.7:
                # Complex pronunciation - use educational samples
                preferred = [f for f in voice_files if 'educational' in f]
                if preferred:
                    selected = preferred[0]
                    print(f"📚 AI selected voice for complex text: {selected}")
                    return os.path.join(self.samples_dir, selected)
        
        # Default selection
        default_voice = voice_files[0]
        print(f"🎤 Using default voice: {default_voice}")
        return os.path.join(self.samples_dir, default_voice)
    
    def generate_smart_speech(self, text, reference_voice=None, output_filename=None):
        """Generate speech with Google AI optimization"""
        print(f"\n🚀 Google AI Enhanced Voice Cloning")
        print("=" * 50)
        
        if not text:
            print("❌ No text provided")
            return None
        
        # Generate output filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"google_ai_speech_{timestamp}.wav"
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        print(f"📝 Text: {text}")
        print(f"🎤 Output: {output_filename}")
        
        # Step 1: Analyze text with Google AI
        text_analysis = None
        if self.ai_ready and self.google_ai:
            print("\n🔍 Analyzing text with Google AI...")
            try:
                text_analysis = self.google_ai.analyze_text_for_cloning(text)
                if text_analysis:
                    print(f"✅ Egyptian Score: {text_analysis.get('egyptian_dialect_score', 0):.2f}")
                    print(f"✅ Complexity: {text_analysis.get('pronunciation_complexity', 0):.2f}")
                    print(f"✅ Emotion: {text_analysis.get('emotional_content', 0):.2f}")
            except Exception as e:
                print(f"⚠️ AI analysis error: {str(e)}")
        
        # Step 2: Select best reference voice
        if not reference_voice:
            reference_voice = self.get_best_reference_voice(text_analysis)
        
        if not reference_voice or not os.path.exists(reference_voice):
            print(f"❌ Reference voice not found: {reference_voice}")
            return None
        
        print(f"🎭 Reference voice: {os.path.basename(reference_voice)}")
        
        # Step 3: Generate speech with XTTS
        success = self.generate_with_xtts(text, reference_voice, output_path, text_analysis)
        
        if success:
            print(f"✅ Speech generated successfully: {output_filename}")
            return output_path
        else:
            print("❌ Speech generation failed")
            return None
    
    def generate_with_xtts(self, text, reference_voice, output_path, text_analysis=None):
        """Generate speech using XTTS"""
        try:
            print("\n🎤 Generating speech with XTTS...")
            
            # Method 1: Try using TTS API
            if self.tts_ready:
                try:
                    from TTS.api import TTS
                    
                    # Initialize TTS model
                    print("🔄 Loading XTTS model...")
                    tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
                    
                    # AI-optimized parameters
                    if text_analysis and self.ai_ready:
                        # Use AI-optimized settings
                        egyptian_score = text_analysis.get('egyptian_dialect_score', 0)
                        complexity = text_analysis.get('pronunciation_complexity', 0)
                        
                        # Adjust speed based on complexity
                        speed = 1.0
                        if complexity > 0.7:
                            speed = 0.9  # Slower for complex text
                        elif egyptian_score > 0.8:
                            speed = 1.1  # Slightly faster for Egyptian
                        
                        print(f"⚙️ AI-optimized speed: {speed}")
                    else:
                        speed = 1.0
                    
                    # Generate speech
                    print("🔄 Generating audio...")
                    tts.tts_to_file(
                        text=text,
                        speaker_wav=reference_voice,
                        language="ar",
                        file_path=output_path,
                        speed=speed
                    )
                    
                    print("✅ XTTS generation successful")
                    return True
                    
                except Exception as e:
                    print(f"⚠️ TTS API error: {str(e)}")
                    # Fall back to command line method
            
            # Method 2: Command line fallback
            print("🔄 Using command line XTTS...")
            return self.generate_with_command_line(text, reference_voice, output_path)
            
        except Exception as e:
            print(f"❌ XTTS generation error: {str(e)}")
            return False
    
    def generate_with_command_line(self, text, reference_voice, output_path):
        """Generate speech using command line XTTS"""
        try:
            # Create temporary text file
            temp_text_file = os.path.join(self.output_dir, "temp_text.txt")
            with open(temp_text_file, "w", encoding="utf-8") as f:
                f.write(text)
            
            # Build command
            cmd = [
                sys.executable, "-m", "TTS.bin.synthesize",
                "--model_name", "tts_models/multilingual/multi-dataset/xtts_v2",
                "--text", text,
                "--speaker_wav", reference_voice,
                "--language_idx", "ar",
                "--out_path", output_path
            ]
            
            print(f"🔄 Running command: {' '.join(cmd[:3])}...")
            
            # Run command
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            # Clean up
            if os.path.exists(temp_text_file):
                os.remove(temp_text_file)
            
            if result.returncode == 0 and os.path.exists(output_path):
                print("✅ Command line generation successful")
                return True
            else:
                print(f"❌ Command failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Command line error: {str(e)}")
            return False
    
    def quick_test(self):
        """Quick test of the system"""
        print("\n🧪 Quick Test of Google AI Voice Cloning")
        print("=" * 45)
        
        # Test text
        test_text = "جميل قوي! هذا اختبار للذكاء الاصطناعي المحسن."
        
        # Generate speech
        output_file = self.generate_smart_speech(
            text=test_text,
            output_filename="google_ai_test.wav"
        )
        
        if output_file:
            print(f"\n🎉 Test successful!")
            print(f"📁 Output file: {output_file}")
            print(f"📂 Open folder: {self.output_dir}")
            
            # Try to play the file
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(output_file)
                else:  # Linux/Mac
                    subprocess.run(['xdg-open', output_file])
                print("🔊 Playing audio file...")
            except:
                print("💡 Please manually open the audio file to listen")
        else:
            print("❌ Test failed")
    
    def batch_generate(self, texts, output_prefix="google_ai_batch"):
        """Generate multiple speech files"""
        print(f"\n📦 Batch Generation: {len(texts)} texts")
        print("=" * 40)
        
        results = []
        
        for i, text in enumerate(texts, 1):
            print(f"\n[{i}/{len(texts)}] Generating: {text[:50]}...")
            
            output_filename = f"{output_prefix}_{i:02d}.wav"
            output_file = self.generate_smart_speech(text, output_filename=output_filename)
            
            if output_file:
                results.append(output_file)
                print(f"✅ Generated: {output_filename}")
            else:
                print(f"❌ Failed: {output_filename}")
        
        print(f"\n📊 Batch Results: {len(results)}/{len(texts)} successful")
        return results

def main():
    """Main function"""
    print("🤖 Google AI Enhanced Voice Cloning System")
    print("=" * 50)
    
    # Initialize system
    voice_cloning = GoogleAIVoiceCloning()
    
    # Quick test
    voice_cloning.quick_test()
    
    # Interactive mode
    print("\n💬 Interactive Mode")
    print("Enter text to generate speech (or 'quit' to exit):")
    
    while True:
        try:
            user_input = input("\n🎤 Text: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if user_input:
                output_file = voice_cloning.generate_smart_speech(user_input)
                if output_file:
                    print(f"✅ Generated: {os.path.basename(output_file)}")
                else:
                    print("❌ Generation failed")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
