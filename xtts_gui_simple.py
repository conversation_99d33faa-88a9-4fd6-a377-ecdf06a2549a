#!/usr/bin/env python3
"""
Simplified XTTS GUI - Works without TTS library for testing
This version shows the GUI and tests basic functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import sys
import time
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xtts_simple.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SimpleXTTSApp:
    def __init__(self, root):
        logger.info("Initializing Simple XTTS GUI application")
        self.root = root
        self.root.title("XTTS Voice Cloning (Simple Version)")
        self.root.geometry("800x600")
        
        # Initialize variables
        self.voice_sample_path = None
        self.last_audio_file = None
        
        # Create main container
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Simple Version (TTS not loaded)")
        self.status_label = ttk.Label(
            self.main_frame, 
            textvariable=self.status_var,
            wraplength=700
        )
        self.status_label.pack(pady=10)
        
        # Info label
        info_text = """This is a simplified version of the XTTS GUI for testing purposes.
To use the full version with voice cloning, you need to install the TTS library.
See the instructions below for installation steps."""
        
        info_label = ttk.Label(
            self.main_frame,
            text=info_text,
            wraplength=700,
            justify=tk.CENTER,
            foreground="blue"
        )
        info_label.pack(pady=10)
        
        # Installation instructions
        self._create_installation_instructions()
        
        # Voice Sample Frame
        sample_frame = ttk.LabelFrame(self.main_frame, text="Voice Sample", padding="10")
        sample_frame.pack(fill=tk.X, pady=10)
        
        self.sample_path_var = tk.StringVar()
        ttk.Label(sample_frame, text="Voice Sample Path:").pack(anchor=tk.W)
        
        path_frame = ttk.Frame(sample_frame)
        path_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(path_frame, textvariable=self.sample_path_var, state='readonly').pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        ttk.Button(
            path_frame,
            text="Browse...",
            command=self._browse_sample
        ).pack(side=tk.LEFT)
        
        # Text Input
        ttk.Label(self.main_frame, text="Text to Speak:").pack(anchor=tk.W, pady=(10, 0))
        self.text_input = tk.Text(self.main_frame, height=10, wrap=tk.WORD)
        self.text_input.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Buttons Frame
        btn_frame = ttk.Frame(self.main_frame)
        btn_frame.pack(pady=10)
        
        self.test_btn = ttk.Button(
            btn_frame,
            text="Test Dependencies",
            command=self._test_dependencies
        )
        self.test_btn.pack(side=tk.LEFT, padx=5)
        
        self.install_btn = ttk.Button(
            btn_frame,
            text="Try Install TTS",
            command=self._try_install_tts
        )
        self.install_btn.pack(side=tk.LEFT, padx=5)
        
        # Set default text
        self.text_input.insert('1.0', "This is a test of the XTTS voice cloning system.")
        
        logger.info("Simple GUI initialization complete")

    def _create_installation_instructions(self):
        """Create installation instructions frame"""
        inst_frame = ttk.LabelFrame(self.main_frame, text="Installation Instructions", padding="10")
        inst_frame.pack(fill=tk.X, pady=10)
        
        instructions = """To install the TTS library:

1. Option 1 - Try automatic installation:
   Click the "Try Install TTS" button below

2. Option 2 - Manual installation:
   Open a command prompt and run:
   pip install TTS

3. Option 3 - If compilation fails:
   Install Visual Studio Build Tools or use conda:
   conda install -c conda-forge coqui-tts

4. After installation, restart this application"""
        
        inst_label = ttk.Label(
            inst_frame,
            text=instructions,
            justify=tk.LEFT,
            font=("Consolas", 9)
        )
        inst_label.pack(anchor=tk.W)

    def _browse_sample(self):
        """Browse for voice sample file"""
        try:
            filetypes = (
                ('WAV files', '*.wav'),
                ('All files', '*.*')
            )
            filename = filedialog.askopenfilename(
                title='Select a voice sample',
                filetypes=filetypes
            )
            if filename:
                if not os.path.exists(filename):
                    logger.error(f"Selected file does not exist: {filename}")
                    messagebox.showerror("Error", "Selected file does not exist")
                    return
                
                self.voice_sample_path = filename
                self.sample_path_var.set(os.path.basename(filename))
                logger.info(f"Voice sample selected: {filename}")
                self.status_var.set(f"Voice sample loaded: {os.path.basename(filename)}")
        except Exception as e:
            logger.error(f"Error browsing for voice sample: {str(e)}")
            messagebox.showerror("Error", f"Failed to open file: {str(e)}")

    def _test_dependencies(self):
        """Test if dependencies are available"""
        self.status_var.set("Testing dependencies...")
        
        def test_thread():
            try:
                # Test basic imports
                import numpy
                import torch
                import pygame
                
                # Test TTS
                try:
                    import TTS
                    tts_available = True
                    tts_version = getattr(TTS, '__version__', 'unknown')
                except ImportError:
                    tts_available = False
                    tts_version = None
                
                # Update UI in main thread
                self.root.after(0, lambda: self._show_test_results(tts_available, tts_version))
                
            except Exception as e:
                self.root.after(0, lambda: self._show_test_error(str(e)))
        
        threading.Thread(target=test_thread, daemon=True).start()

    def _show_test_results(self, tts_available, tts_version):
        """Show dependency test results"""
        if tts_available:
            message = f"✓ All dependencies available!\nTTS version: {tts_version}\n\nYou can now use the full XTTS GUI."
            self.status_var.set("All dependencies available!")
            messagebox.showinfo("Dependencies Test", message)
        else:
            message = "✓ Core dependencies available\n✗ TTS library not found\n\nPlease install TTS library for full functionality."
            self.status_var.set("TTS library not found")
            messagebox.showwarning("Dependencies Test", message)

    def _show_test_error(self, error):
        """Show dependency test error"""
        message = f"Error testing dependencies:\n{error}"
        self.status_var.set("Dependency test failed")
        messagebox.showerror("Dependencies Test", message)

    def _try_install_tts(self):
        """Try to install TTS library"""
        self.status_var.set("Attempting to install TTS...")
        self.install_btn.config(state=tk.DISABLED)
        
        def install_thread():
            try:
                import subprocess
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', 'TTS'
                ], capture_output=True, text=True, timeout=300)
                
                success = result.returncode == 0
                self.root.after(0, lambda: self._install_complete(success, result.stdout, result.stderr))
                
            except subprocess.TimeoutExpired:
                self.root.after(0, lambda: self._install_complete(False, "", "Installation timed out"))
            except Exception as e:
                self.root.after(0, lambda: self._install_complete(False, "", str(e)))
        
        threading.Thread(target=install_thread, daemon=True).start()

    def _install_complete(self, success, stdout, stderr):
        """Handle installation completion"""
        self.install_btn.config(state=tk.NORMAL)
        
        if success:
            self.status_var.set("TTS installation completed!")
            messagebox.showinfo("Installation", "TTS installed successfully!\nPlease restart the application.")
        else:
            self.status_var.set("TTS installation failed")
            error_msg = f"Installation failed:\n\nSTDOUT:\n{stdout}\n\nSTDERR:\n{stderr}"
            messagebox.showerror("Installation Failed", error_msg)

def main():
    logger.info("Starting Simple XTTS GUI application")
    
    # Set up the root window
    root = tk.Tk()
    root.title("XTTS Voice Cloning (Simple)")
    
    # Create and run the application
    app = SimpleXTTSApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
