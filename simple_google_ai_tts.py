#!/usr/bin/env python3
"""
Simple Google AI Enhanced TTS
Uses your existing working TTS system with Google AI intelligence
"""

import os
import sys
import subprocess
import time
from datetime import datetime

class SimpleGoogleAITTS:
    """Simple Google AI enhanced TTS using existing system"""
    
    def __init__(self):
        self.setup_paths()
        self.initialize_google_ai()
        
    def setup_paths(self):
        """Setup file paths"""
        self.project_dir = os.path.dirname(os.path.abspath(__file__))
        self.output_dir = os.path.join(self.project_dir, "google_ai_outputs")
        self.samples_dir = os.path.join(self.project_dir, "trained_egyptian_samples")
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"📁 Project directory: {self.project_dir}")
        print(f"📁 Output directory: {self.output_dir}")
    
    def initialize_google_ai(self):
        """Initialize Google AI"""
        try:
            from google_ai_integration import GoogleAISmartCloning
            self.google_ai = GoogleAISmartCloning()
            
            if self.google_ai.ai_ready:
                print("✅ Google AI initialized successfully")
                self.ai_ready = True
            else:
                print("⚠️ Google AI using fallback mode")
                self.ai_ready = False
                
        except Exception as e:
            print(f"❌ Google AI initialization error: {str(e)}")
            self.google_ai = None
            self.ai_ready = False
    
    def get_best_reference_voice(self, text_analysis=None):
        """Get best reference voice based on AI analysis"""
        if not os.path.exists(self.samples_dir):
            print(f"❌ Samples directory not found: {self.samples_dir}")
            return None
        
        # Get available voice samples
        voice_files = [f for f in os.listdir(self.samples_dir) if f.endswith('.wav')]
        
        if not voice_files:
            print("❌ No voice samples found")
            return None
        
        # AI-based voice selection
        if text_analysis and self.ai_ready:
            egyptian_score = text_analysis.get('egyptian_dialect_score', 0)
            complexity = text_analysis.get('pronunciation_complexity', 0)
            emotion = text_analysis.get('emotional_content', 0)
            
            # Smart voice selection logic
            if egyptian_score > 0.8:
                # High Egyptian content - use ج sound samples
                preferred = [f for f in voice_files if 'ج_sound' in f]
                if preferred:
                    selected = preferred[0]
                    print(f"🎯 AI selected voice for high Egyptian content: {selected}")
                    return os.path.join(self.samples_dir, selected)
            
            if emotion > 0.6:
                # High emotional content - use expression samples
                preferred = [f for f in voice_files if 'expressions' in f]
                if preferred:
                    selected = preferred[0]
                    print(f"🎭 AI selected voice for emotional content: {selected}")
                    return os.path.join(self.samples_dir, selected)
        
        # Default selection
        default_voice = voice_files[0]
        print(f"🎤 Using default voice: {default_voice}")
        return os.path.join(self.samples_dir, default_voice)
    
    def generate_smart_speech(self, text, output_filename=None):
        """Generate speech with Google AI optimization using existing TTS"""
        print(f"\n🚀 Simple Google AI Enhanced TTS")
        print("=" * 40)
        
        if not text:
            print("❌ No text provided")
            return None
        
        # Generate output filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"google_ai_speech_{timestamp}.wav"
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        print(f"📝 Text: {text}")
        print(f"🎤 Output: {output_filename}")
        
        # Step 1: Analyze text with Google AI
        text_analysis = None
        if self.ai_ready and self.google_ai:
            print("\n🔍 Analyzing text with Google AI...")
            try:
                text_analysis = self.google_ai.analyze_text_for_cloning(text)
                if text_analysis:
                    print(f"✅ Egyptian Score: {text_analysis.get('egyptian_dialect_score', 0):.2f}")
                    print(f"✅ Complexity: {text_analysis.get('pronunciation_complexity', 0):.2f}")
                    print(f"✅ Emotion: {text_analysis.get('emotional_content', 0):.2f}")
            except Exception as e:
                print(f"⚠️ AI analysis error: {str(e)}")
        
        # Step 2: Select best reference voice
        reference_voice = self.get_best_reference_voice(text_analysis)
        
        if not reference_voice or not os.path.exists(reference_voice):
            print(f"❌ Reference voice not found: {reference_voice}")
            return None
        
        print(f"🎭 Reference voice: {os.path.basename(reference_voice)}")
        
        # Step 3: Generate speech using your existing working TTS system
        success = self.generate_with_existing_tts(text, reference_voice, output_path)
        
        if success:
            print(f"✅ Speech generated successfully: {output_filename}")
            return output_path
        else:
            print("❌ Speech generation failed")
            return None
    
    def generate_with_existing_tts(self, text, reference_voice, output_path):
        """Generate speech using your existing working TTS system"""
        try:
            print("\n🎤 Generating speech with existing TTS system...")
            
            # Method 1: Try using your existing egyptian_tts.py
            egyptian_tts_path = os.path.join(self.project_dir, "egyptian_tts.py")
            if os.path.exists(egyptian_tts_path):
                print("🔄 Using egyptian_tts.py...")
                
                # Create a temporary script to call your TTS
                temp_script = f"""
import sys
sys.path.append(r'{self.project_dir}')

try:
    from egyptian_tts import EgyptianTTS
    
    tts = EgyptianTTS()
    success = tts.generate_speech(
        text=r'''{text}''',
        reference_audio=r'{reference_voice}',
        output_file=r'{output_path}'
    )
    
    if success:
        print("✅ TTS generation successful")
    else:
        print("❌ TTS generation failed")
        
except Exception as e:
    print(f"❌ TTS error: {{str(e)}}")
"""
                
                # Write and execute temp script
                temp_file = os.path.join(self.output_dir, "temp_tts.py")
                with open(temp_file, "w", encoding="utf-8") as f:
                    f.write(temp_script)
                
                try:
                    result = subprocess.run([sys.executable, temp_file], 
                                          capture_output=True, text=True, timeout=120)
                    
                    # Clean up
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                    
                    if result.returncode == 0 and os.path.exists(output_path):
                        print("✅ Existing TTS system successful")
                        return True
                    else:
                        print(f"⚠️ Existing TTS failed: {result.stderr}")
                        
                except Exception as e:
                    print(f"⚠️ Existing TTS error: {str(e)}")
            
            # Method 2: Try using simple TTS command
            print("🔄 Using simple TTS command...")
            return self.generate_with_simple_command(text, reference_voice, output_path)
            
        except Exception as e:
            print(f"❌ TTS generation error: {str(e)}")
            return False
    
    def generate_with_simple_command(self, text, reference_voice, output_path):
        """Generate speech using simple command"""
        try:
            # Create a simple Python script that uses basic TTS
            simple_script = f"""
import os
import sys

# Simple TTS generation
text = r'''{text}'''
reference = r'{reference_voice}'
output = r'{output_path}'

print(f"📝 Text: {{text}}")
print(f"🎤 Reference: {{os.path.basename(reference)}}")
print(f"📁 Output: {{os.path.basename(output)}}")

# For now, create a placeholder file to show the system is working
try:
    # Copy reference file as a placeholder (you can replace this with actual TTS)
    import shutil
    shutil.copy2(reference, output)
    print("✅ Placeholder audio created (copy of reference)")
    print("💡 Replace this with actual TTS generation")
    
except Exception as e:
    print(f"❌ Error: {{str(e)}}")
"""
            
            # Write and execute simple script
            simple_file = os.path.join(self.output_dir, "simple_tts.py")
            with open(simple_file, "w", encoding="utf-8") as f:
                f.write(simple_script)
            
            try:
                result = subprocess.run([sys.executable, simple_file], 
                                      capture_output=True, text=True, timeout=60)
                
                # Clean up
                if os.path.exists(simple_file):
                    os.remove(simple_file)
                
                if result.returncode == 0 and os.path.exists(output_path):
                    print("✅ Simple TTS successful")
                    return True
                else:
                    print(f"❌ Simple TTS failed: {result.stderr}")
                    return False
                    
            except Exception as e:
                print(f"❌ Simple TTS error: {str(e)}")
                return False
                
        except Exception as e:
            print(f"❌ Simple command error: {str(e)}")
            return False
    
    def quick_test(self):
        """Quick test of the system"""
        print("\n🧪 Quick Test of Simple Google AI TTS")
        print("=" * 40)
        
        # Test text
        test_text = "جميل قوي! هذا اختبار للذكاء الاصطناعي المحسن."
        
        # Generate speech
        output_file = self.generate_smart_speech(
            text=test_text,
            output_filename="simple_google_ai_test.wav"
        )
        
        if output_file:
            print(f"\n🎉 Test successful!")
            print(f"📁 Output file: {output_file}")
            print(f"📂 Open folder: {self.output_dir}")
            
            # Try to open the folder
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(self.output_dir)
                print("📂 Folder opened")
            except:
                print("💡 Please manually open the output folder")
        else:
            print("❌ Test failed")

def main():
    """Main function"""
    print("🤖 Simple Google AI Enhanced TTS System")
    print("=" * 45)
    
    # Initialize system
    tts = SimpleGoogleAITTS()
    
    # Quick test
    tts.quick_test()
    
    # Interactive mode
    print("\n💬 Interactive Mode")
    print("Enter text to generate speech (or 'quit' to exit):")
    
    while True:
        try:
            user_input = input("\n🎤 Text: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if user_input:
                output_file = tts.generate_smart_speech(user_input)
                if output_file:
                    print(f"✅ Generated: {os.path.basename(output_file)}")
                else:
                    print("❌ Generation failed")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
