#!/usr/bin/env python3
"""
Voice Sample Recorder for Egyptian Accent Training
Record and process voice samples for better Egyptian TTS
"""

import os
import sys
import time
import wave
import threading

def check_audio_dependencies():
    """Check if audio recording dependencies are available"""
    try:
        import pyaudio
        return True
    except ImportError:
        print("❌ PyAudio not found. Install with: pip install pyaudio")
        return False

def record_voice_sample(filename, duration=10):
    """Record a voice sample"""
    try:
        import pyaudio
        
        # Audio settings
        CHUNK = 1024
        FORMAT = pyaudio.paInt16
        CHANNELS = 1  # Mono
        RATE = 22050  # Good quality for TTS
        
        print(f"🎤 Recording {filename} for {duration} seconds...")
        print("📢 Speak clearly in Egyptian Arabic!")
        print("3... 2... 1... START!")
        
        # Initialize PyAudio
        p = pyaudio.PyAudio()
        
        # Open stream
        stream = p.open(format=FORMAT,
                       channels=CHANNELS,
                       rate=RATE,
                       input=True,
                       frames_per_buffer=CHUNK)
        
        frames = []
        
        # Record for specified duration
        for i in range(0, int(RATE / CHUNK * duration)):
            data = stream.read(CHUNK)
            frames.append(data)
            
            # Show progress
            progress = (i + 1) / (RATE / CHUNK * duration) * 100
            if int(progress) % 10 == 0:
                print(f"Recording... {int(progress)}%")
        
        print("🛑 Recording finished!")
        
        # Stop and close stream
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        # Save the recording
        os.makedirs("voice_samples", exist_ok=True)
        filepath = os.path.join("voice_samples", filename)
        
        wf = wave.open(filepath, 'wb')
        wf.setnchannels(CHANNELS)
        wf.setsampwidth(p.get_sample_size(FORMAT))
        wf.setframerate(RATE)
        wf.writeframes(b''.join(frames))
        wf.close()
        
        print(f"✅ Saved: {filepath}")
        return filepath
        
    except Exception as e:
        print(f"❌ Recording error: {str(e)}")
        return None

def get_egyptian_recording_prompts():
    """Get prompts for recording Egyptian voice samples"""
    return {
        "basic_greeting": {
            "text": "أهلاً وسهلاً! إزيك النهاردة؟ أنا مصري من القاهرة.",
            "pronunciation": "Ahlan wa sahlan! Izzayyak ennaharda? Ana masri min el-qahira.",
            "english": "Hello and welcome! How are you today? I'm Egyptian from Cairo.",
            "tips": "Emphasize the 'g' sound in جميل and the glottal stop in قاهرة"
        },
        "emotional_expression": {
            "text": "والله العظيم ده أحلى كلام سمعته النهاردة! مصر أم الدنيا!",
            "pronunciation": "Wallahi el-azeem da ahla kalam seme'to ennaharda! Masr umm ed-dunya!",
            "english": "I swear this is the best thing I heard today! Egypt is the mother of the world!",
            "tips": "Show excitement and pride - this should sound enthusiastic"
        },
        "hospitality": {
            "text": "اتفضل اشرب شاي واقعد معانا شوية. البيت بيتك.",
            "pronunciation": "Itfaddal ishrab shai wa-o'od ma'ana shwayya. El-beet beetak.",
            "english": "Please have some tea and sit with us for a while. The house is yours.",
            "tips": "Warm, welcoming tone - like inviting a friend"
        },
        "storytelling": {
            "text": "كان يا ما كان في قديم الزمان واحد مصري عايش في الإسكندرية.",
            "pronunciation": "Kan ya ma kan fi qadeem ez-zaman wahed masri 'ayesh fil-iskandariyya.",
            "english": "Once upon a time there was an Egyptian living in Alexandria.",
            "tips": "Narrative tone, like telling a story to children"
        },
        "casual_conversation": {
            "text": "يلا بينا نروح نتمشى شوية في وسط البلد ونشرب قهوة.",
            "pronunciation": "Yalla beena nerooh netmassha shwayya fi west el-balad wi-nishrab ahwa.",
            "english": "Come on, let's go walk a bit downtown and drink coffee.",
            "tips": "Casual, friendly tone - like talking to a friend"
        }
    }

def guided_recording_session():
    """Guide user through recording multiple voice samples"""
    print("🎙️ Egyptian Voice Sample Recording Session")
    print("=" * 45)
    
    if not check_audio_dependencies():
        print("\n💡 To install PyAudio:")
        print("pip install pyaudio")
        print("\nIf that fails, try:")
        print("conda install pyaudio")
        return
    
    prompts = get_egyptian_recording_prompts()
    
    print(f"\n📝 We'll record {len(prompts)} different voice samples")
    print("Each recording will be 10 seconds long")
    print("Speak clearly and naturally in Egyptian Arabic")
    
    input("\nPress Enter when ready to start...")
    
    recorded_files = []
    
    for i, (prompt_name, prompt_info) in enumerate(prompts.items(), 1):
        print(f"\n{'='*50}")
        print(f"📖 Recording {i}/{len(prompts)}: {prompt_name.replace('_', ' ').title()}")
        print('='*50)
        
        print(f"\n📝 Arabic Text:")
        print(f"   {prompt_info['text']}")
        print(f"\n🔤 Pronunciation Guide:")
        print(f"   {prompt_info['pronunciation']}")
        print(f"\n🇬🇧 English Meaning:")
        print(f"   {prompt_info['english']}")
        print(f"\n💡 Tips:")
        print(f"   {prompt_info['tips']}")
        
        input(f"\nPress Enter to start recording {prompt_name}...")
        
        filename = f"egyptian_{prompt_name}_{int(time.time())}.wav"
        recorded_file = record_voice_sample(filename, duration=10)
        
        if recorded_file:
            recorded_files.append(recorded_file)
            print(f"✅ Successfully recorded: {prompt_name}")
        else:
            print(f"❌ Failed to record: {prompt_name}")
        
        if i < len(prompts):
            print("\n⏸️ Take a short break if needed...")
            time.sleep(2)
    
    print(f"\n🎉 Recording session complete!")
    print(f"📁 Recorded {len(recorded_files)} voice samples:")
    
    for file in recorded_files:
        print(f"   • {file}")
    
    print(f"\n🎯 Next steps:")
    print("1. Test these samples in the XTTS GUI")
    print("2. Compare quality and choose the best ones")
    print("3. Use the best samples for voice cloning")
    print("4. Consider recording more samples for variety")

def analyze_existing_samples():
    """Analyze existing voice samples"""
    print("🔍 Analyzing Existing Voice Samples")
    print("=" * 35)
    
    samples_dir = "voice_samples"
    if not os.path.exists(samples_dir):
        print(f"❌ No '{samples_dir}' directory found")
        print("Create the directory and add your voice samples")
        return
    
    audio_files = [f for f in os.listdir(samples_dir) 
                   if f.endswith(('.wav', '.mp3', '.flac'))]
    
    if not audio_files:
        print(f"❌ No audio files found in '{samples_dir}'")
        return
    
    print(f"📁 Found {len(audio_files)} audio files:")
    
    for file in audio_files:
        filepath = os.path.join(samples_dir, file)
        size = os.path.getsize(filepath)
        
        print(f"\n📄 {file}")
        print(f"   Size: {size:,} bytes")
        
        # Try to get audio info
        try:
            if file.endswith('.wav'):
                with wave.open(filepath, 'rb') as wf:
                    frames = wf.getnframes()
                    rate = wf.getframerate()
                    duration = frames / rate
                    channels = wf.getnchannels()
                    
                    print(f"   Duration: {duration:.1f} seconds")
                    print(f"   Sample Rate: {rate} Hz")
                    print(f"   Channels: {channels}")
                    
                    # Quality assessment
                    if duration < 5:
                        print("   ⚠️ Too short (recommend 5-15 seconds)")
                    elif duration > 30:
                        print("   ⚠️ Too long (recommend 5-15 seconds)")
                    else:
                        print("   ✅ Good duration")
                    
                    if rate < 16000:
                        print("   ⚠️ Low sample rate (recommend 22kHz+)")
                    else:
                        print("   ✅ Good sample rate")
                    
                    if channels > 1:
                        print("   ⚠️ Stereo (recommend mono)")
                    else:
                        print("   ✅ Mono channel")
        
        except Exception as e:
            print(f"   ❌ Could not analyze: {str(e)}")

def main():
    """Main function"""
    print("🇪🇬 Egyptian Voice Sample Recorder")
    print("=" * 35)
    
    options = [
        ("Record new voice samples", guided_recording_session),
        ("Analyze existing samples", analyze_existing_samples),
        ("Show recording tips", lambda: print(get_recording_tips())),
        ("Exit", lambda: sys.exit(0))
    ]
    
    while True:
        print("\nChoose an option:")
        for i, (desc, _) in enumerate(options, 1):
            print(f"{i}. {desc}")
        
        try:
            choice = int(input("\nEnter choice (1-4): ")) - 1
            if 0 <= choice < len(options):
                options[choice][1]()
            else:
                print("Invalid choice!")
        except ValueError:
            print("Invalid choice!")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break

def get_recording_tips():
    """Get recording tips"""
    return """
🎯 Egyptian Voice Recording Tips:

🎤 Technical Setup:
• Use a quiet room with minimal echo
• Position microphone 6-12 inches from mouth
• Avoid background noise (AC, fans, traffic)
• Use a pop filter if available
• Record in WAV format for best quality

🗣️ Speaking Technique:
• Speak naturally, not too slow or fast
• Use authentic Egyptian pronunciation:
  - ج = 'g' sound (جميل = "gameel")
  - ق = glottal stop (قال = "aal")
  - ث = 's' or 't' sound
  - ذ = 'z' or 'd' sound
• Include natural Egyptian intonation
• Show emotion appropriate to the text
• Pause briefly between sentences

📝 Content Guidelines:
• Use conversational Egyptian Arabic
• Include Egyptian-specific words: يلا، إزيك، قوي
• Avoid formal/classical Arabic
• Focus on Cairo dialect
• Include variety: greetings, emotions, stories
"""

if __name__ == "__main__":
    main()
