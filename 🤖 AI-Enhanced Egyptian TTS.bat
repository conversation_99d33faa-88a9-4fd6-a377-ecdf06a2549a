@echo off
title AI-Enhanced Egyptian TTS
color 0A

echo.
echo     🤖 AI-Enhanced Egyptian TTS 🇪🇬
echo     ===============================
echo.
echo     Launching intelligent Egyptian TTS with:
echo     • AI-powered training optimization
echo     • Real-time performance monitoring
echo     • Intelligent parameter suggestions
echo     • Automated quality assessment
echo     • Smart pronunciation training
echo.

REM Change to project directory
cd /d "%~dp0"

echo     🚀 Starting AI-Enhanced GUI...
echo.

REM Launch AI-Enhanced GUI
C:\Users\<USER>\miniconda3\envs\xtts\python.exe ai_enhanced_gui.py

echo.
echo     👋 AI-Enhanced Egyptian TTS closed
echo.
pause
