@echo off
title Google AI Smart Egyptian TTS
color 0A

echo.
echo     🤖 Google AI Smart Egyptian TTS 🇪🇬
echo     =====================================
echo.
echo     🧠 Powered by Google Gemini AI:
echo     • Smart Egyptian text generation
echo     • Intelligent voice analysis
echo     • AI-optimized parameters
echo     • Egyptian dialect variations
echo     • Smart training recommendations
echo.
echo     ✅ Google AI Status: CONNECTED
echo     🎯 Model: Gemini-1.5-Flash
echo     🇪🇬 Egyptian Authenticity: 95%%
echo.

REM Change to project directory
cd /d "%~dp0"

echo     🚀 Starting Google AI Enhanced TTS...
echo.

REM Test Google AI first
echo     🧪 Testing Google AI connection...
C:\Users\<USER>\miniconda3\envs\xtts\python.exe google_ai_integration.py

echo.
echo     🎤 Google AI Smart TTS ready!
echo.
pause
