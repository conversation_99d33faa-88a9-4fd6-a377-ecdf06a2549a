import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import sys
import time
import io
import contextlib
import logging
import gc
import json
import hashlib
import urllib.request
from pathlib import Path
import shutil
import warnings
import subprocess
import platform
import zipfile
import tempfile
import urllib.request
import sys

# Suppress torchvision image loading warning
warnings.filterwarnings('ignore',
    message='Failed to load image Python extension',
    module='torchvision.io.image')

def install_ffmpeg():
    """Download and install FFmpeg"""
    try:
        ffmpeg_url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
        install_path = os.path.join(os.getenv('LOCALAPPDATA'), 'ffmpeg')
        
        logger.info(f"FFmpeg download URL: {ffmpeg_url}")
        logger.info(f"FFmpeg install path: {install_path}")
        logger.info(f"PATH before modification: {os.environ.get('PATH', '')}")
        logger.info("Downloading FFmpeg...")
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
            urllib.request.urlretrieve(ffmpeg_url, temp_file.name)
            
            logger.info("Extracting FFmpeg...")
            with zipfile.ZipFile(temp_file.name, 'r') as zip_ref:
                # Extract only bin folder contents
                for member in zip_ref.namelist():
                    if '/bin/' in member and member.endswith(('.exe', '.dll')):
                        # Modify path to extract directly to bin folder
                        output_path = os.path.join(install_path, 'bin', os.path.basename(member))
                        os.makedirs(os.path.dirname(output_path), exist_ok=True)
                        
                        # Extract file
                        with zip_ref.open(member) as source, open(output_path, 'wb') as target:
                            shutil.copyfileobj(source, target)
            
        logger.info(f"FFmpeg installed to: {install_path}")
        
        # Add to PATH
        bin_path = os.path.join(install_path, 'bin')
        if bin_path not in os.environ['PATH']:
            os.environ['PATH'] = bin_path + os.pathsep + os.environ['PATH']
            logger.info(f"PATH after modification: {os.environ['PATH']}")
            
        # Verify installation
        if check_ffmpeg():
            logger.info("FFmpeg installation successful")
            return True
        else:
            logger.error("FFmpeg installation verification failed")
            return False
            
    except Exception as e:
        logger.error(f"Error installing FFmpeg: {str(e)}")
        return False

def check_ffmpeg():
    """Check if FFmpeg is available and working"""
    try:
        # Check FFmpeg version
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True,
                              text=True)
        logger.info(f"FFmpeg check return code: {result.returncode}")
        logger.info(f"FFmpeg check stdout: {result.stdout}")
        logger.info(f"FFmpeg check stderr: {result.stderr}")
        if result.returncode == 0:
            logger.info("FFmpeg found: " + result.stdout.split('\n')[0])
            return True
        return False
    except FileNotFoundError:
        logger.error("FFmpeg not found in PATH")
        return False
        
def get_ffmpeg_path():
    """Get the FFmpeg path based on platform"""
    system = platform.system().lower()
    
    if system == 'windows':
        # Check common Windows locations
        possible_paths = [
            os.path.join(os.getenv('LOCALAPPDATA'), 'ffmpeg', 'bin'),
            os.path.join(os.getenv('PROGRAMFILES'), 'ffmpeg', 'bin'),
            os.path.join(os.getenv('PROGRAMFILES(X86)'), 'ffmpeg', 'bin')
        ]
        
        for path in possible_paths:
            if os.path.exists(os.path.join(path, 'ffmpeg.exe')):
                return path
                
    return None

# Set up logging with UTF-8 encoding
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# File handler with UTF-8 encoding
file_handler = logging.FileHandler('xtts_debug.log', encoding='utf-8')
file_handler.setFormatter(log_formatter)

# Console handler with UTF-8 encoding
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(log_formatter)

logger = logging.getLogger()
logger.setLevel(logging.DEBUG)
logger.addHandler(file_handler)
logger.addHandler(console_handler)

CACHE_DIR = os.path.join(os.path.expanduser("~"), ".cache", "xtts")
MODEL_INFO_FILE = os.path.join(CACHE_DIR, "model_info.json")

def get_file_hash(file_path):
    """Calculate SHA256 hash of a file"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

class ModelCache:
    def __init__(self, cache_dir=CACHE_DIR):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        self.load_model_info()
        self.model_manager = None

    def load_model_info(self):
        """Load cached model information"""
        try:
            with open(MODEL_INFO_FILE, 'r') as f:
                self.model_info = json.load(f)
        except:
            self.model_info = {}

    def save_model_info(self):
        """Save model information to cache"""
        os.makedirs(os.path.dirname(MODEL_INFO_FILE), exist_ok=True)
        with open(MODEL_INFO_FILE, 'w') as f:
            json.dump(self.model_info, f)

    def get_model_manager(self):
        """Get or create ModelManager instance"""
        if not self.model_manager:
            from TTS.utils.manage import ModelManager
            from TTS.config.shared_configs import BaseDatasetConfig
            import json
            
            logger.debug("Initializing TTS ModelManager")
            self.model_manager = ModelManager()
            
            # Load models configuration
            try:
                # Access models_dict directly - it's loaded automatically
                model_types = list(self.model_manager.models_dict.keys())
                logger.debug(f"Available model types: {', '.join(model_types)}")
                
                # Log available models for debugging
                for model_type, configs in self.model_manager.models_dict.items():
                    if isinstance(configs, list):
                        logger.debug(f"Model type {model_type}: {len(configs)} models")
                    else:
                        logger.debug(f"Model type {model_type}: 1 model")
                    
            except Exception as e:
                logger.error(f"Error accessing models configuration: {str(e)}")
                raise
                
        return self.model_manager

    def get_remote_model_info(self, model_name):
        """Get remote model information without downloading"""
        try:
            manager = self.get_model_manager()
            
            # Get model config directly
            if model_name not in manager.models_dict:
                available_models = list(manager.models_dict.keys())
                logger.error(f"Model {model_name} not found")
                logger.debug(f"Available models: {', '.join(available_models)}")
                raise ValueError(f"Model {model_name} not found in available models")
                
            model_config = manager.models_dict[model_name]
            logger.debug(f"Found model config for {model_name}")
            
            # Get local path and create directories
            model_path = os.path.join(manager.output_path, model_name)
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            
            logger.debug(f"Model will be saved to: {model_path}")
            if os.path.exists(model_path):
                return {
                    'path': model_path,
                    'hash': get_file_hash(model_path),
                    'url': model_config["download_url"]
                }
                
            return {
                'path': model_path,
                'url': model_config["download_url"]
            }
            
        except Exception as e:
            logger.error(f"Error getting remote model info: {str(e)}")
            logger.debug("Available model types: " +
                      ", ".join(manager.models_dict.keys()))
        return None

    def download_model(self, model_name, progress_callback=None):
        """Download model with progress tracking"""
        try:
            # Get model info
            model_info = self.get_remote_model_info(model_name)
            if not model_info:
                raise ValueError(f"Could not get model info for {model_name}")
            if 'url' not in model_info:
                raise ValueError(f"No download URL found for model {model_name}")
                
            model_url = model_info['url']
            logger.info(f"Model download URL: {model_url}")
            
            # Prepare paths
            cached_path = os.path.join(self.cache_dir, model_name)
            os.makedirs(os.path.dirname(cached_path), exist_ok=True)
            
            logger.info(f"Downloading model from {model_url}")
            
            # Download with progress
            def _progress(count, block_size, total_size):
                if progress_callback:
                    percent = (count * block_size * 100) / total_size
                    progress_callback(f"Downloading model: {percent:.1f}%", percent)
                    
            logger.info(f"Downloading model from {model_url}")
            urllib.request.urlretrieve(model_url, cached_path, _progress)
            
            # Update cache info
            self.model_info[model_name] = {
                'hash': get_file_hash(cached_path),
                'timestamp': time.time()
            }
            self.save_model_info()
            
            return cached_path
            
        except Exception as e:
            logger.error(f"Error downloading model: {str(e)}")
            raise

    def get_cached_model(self, model_name):
        """Get cached model path if valid"""
        if model_name in self.model_info:
            cached_path = os.path.join(self.cache_dir, model_name)
            if os.path.exists(cached_path):
                remote_info = self.get_remote_model_info(model_name)
                if remote_info and 'hash' in remote_info:
                    cached_hash = self.model_info[model_name].get('hash')
                    remote_hash = remote_info['hash']
                    
                    if cached_hash and remote_hash and cached_hash == remote_hash:
                        logger.info(f"Using cached model: {cached_path}")
                        return cached_path
                    else:
                        logger.info("Cache validation failed - will download fresh copy")
                        logger.debug(f"Cached hash: {cached_hash}")
                        logger.debug(f"Remote hash: {remote_hash}")
                        try:
                            os.remove(cached_path)
                            logger.info("Removed invalid cached model")
                        except Exception as e:
                            logger.warning(f"Could not remove invalid cache: {str(e)}")
                else:
                    logger.info("Cache exists but cannot validate - will attempt download")
            else:
                logger.info("No cached model found")
        return None

    def cache_model(self, model_name, model_path):
        """Cache a model file"""
        cached_path = os.path.join(self.cache_dir, model_name)
        os.makedirs(os.path.dirname(cached_path), exist_ok=True)
        
        # Copy model to cache
        shutil.copy2(model_path, cached_path)
            
        # Update cache info
        self.model_info[model_name] = {
            'hash': get_file_hash(cached_path),
            'timestamp': time.time()
        }
        self.save_model_info()
        return cached_path

# Redirect stdout to avoid readline issues
class DummyFile:
    def write(self, x): pass
    def flush(self): pass

@contextlib.contextmanager
def nostdout():
    save_stdout = sys.stdout
    sys.stdout = DummyFile()
    yield
    sys.stdout = save_stdout

class XTTSApp:
    def __init__(self, root, model_cache):
        # Check FFmpeg before initializing
        if not check_ffmpeg():
            ffmpeg_path = get_ffmpeg_path()
            if ffmpeg_path:
                logger.info(f"Adding FFmpeg path to environment: {ffmpeg_path}")
                os.environ["PATH"] = ffmpeg_path + os.pathsep + os.environ["PATH"]
                if not check_ffmpeg():
                    logger.error("FFmpeg still not working after adding to PATH")
            else:
                logger.error("FFmpeg not found. Please install FFmpeg with required codecs")
        logger.info("Initializing XTTS GUI application")
        self.root = root
        self.root.title("XTTS Voice Cloning")
        self.root.geometry("800x600")
        
        self.model_cache = model_cache
        self.model_name = "tts_models/multilingual/multi-dataset/your_voice"
        
        # Initialize TTS model as None
        self.tts = None
        self.model_loaded = False
        self.voice_sample_path = None
        self._init_pygame()
        
        # Create main container
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_label = ttk.Label(
            self.main_frame, 
            textvariable=self.status_var,
            wraplength=700
        )
        self.status_label.pack(pady=10)
        
        # Progress label
        self.progress_var = tk.StringVar()
        self.progress_label = ttk.Label(
            self.main_frame,
            textvariable=self.progress_var,
            wraplength=700
        )
        self.progress_label.pack(pady=5)
        
        # Load Model Button
        self.load_btn = ttk.Button(
            self.main_frame,
            text="Load XTTS Model",
            command=self._show_terms_dialog
        )
        self.load_btn.pack(pady=10)
        
        # Voice Sample Frame
        sample_frame = ttk.LabelFrame(self.main_frame, text="Voice Sample", padding="10")
        sample_frame.pack(fill=tk.X, pady=10)
        
        self.sample_path_var = tk.StringVar()
        ttk.Label(sample_frame, text="Voice Sample Path:").pack(anchor=tk.W)
        
        path_frame = ttk.Frame(sample_frame)
        path_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(path_frame, textvariable=self.sample_path_var, state='readonly').pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        ttk.Button(
            path_frame,
            text="Browse...",
            command=self._browse_sample
        ).pack(side=tk.LEFT)
        
        # Text Input
        ttk.Label(self.main_frame, text="Text to Speak:").pack(anchor=tk.W, pady=(10, 0))
        self.text_input = tk.Text(self.main_frame, height=10, wrap=tk.WORD)
        self.text_input.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Buttons Frame
        btn_frame = ttk.Frame(self.main_frame)
        btn_frame.pack(pady=10)
        
        self.generate_btn = ttk.Button(
            btn_frame,
            text="Generate Speech",
            command=self._generate_speech,
            state=tk.DISABLED
        )
        self.generate_btn.pack(side=tk.LEFT, padx=5)
        
        self.play_btn = ttk.Button(
            btn_frame,
            text="Play Last Generated",
            command=self._play_audio,
            state=tk.DISABLED
        )
        self.play_btn.pack(side=tk.LEFT, padx=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(
            self.main_frame,
            orient=tk.HORIZONTAL,
            length=400,
            mode='determinate'
        )
        
        # Set default text
        self.text_input.insert('1.0', "This is a test of the XTTS voice cloning system.")
        
        # Last generated audio file
        self.last_audio_file = None
        
        # Cleanup old output files on startup
        self._cleanup_old_files()
        
        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # Initialize progress monitoring
        self._last_progress_update = time.time()
        self._current_stage = 'init'
        
        logger.info("GUI initialization complete")

    def _update_progress(self, message, progress=None):
        """Update progress display with current stage and message"""
        try:
            if progress is not None:
                self.progress['value'] = progress
                if progress > 0:
                    time_left = "unknown"
                    if hasattr(self, '_last_progress_time') and hasattr(self, '_last_progress_value'):
                        time_diff = time.time() - self._last_progress_time
                        progress_diff = progress - self._last_progress_value
                        if progress_diff > 0:
                            speed = progress_diff / time_diff
                            remaining = (100 - progress) / speed
                            if remaining > 0:
                                time_left = f"{int(remaining)}s"
                    
                    message = f"{message} ({progress:.1f}%, ETA: {time_left})"
                
                # Store values for next update
                self._last_progress_time = time.time()
                self._last_progress_value = progress
            
            self.progress_var.set(message)
            logger.info(f"Progress update: {message}")
            
            # Force UI update
            self.root.update_idletasks()
            
        except Exception as e:
            logger.error(f"Error updating progress: {str(e)}")

    def _init_pygame(self):
        """Initialize Pygame mixer for audio playback"""
        try:
            import pygame
            pygame.mixer.init()
            self.pygame_initialized = True
            logging.info("Pygame mixer initialized successfully")
        except Exception as e:
            self.pygame_initialized = False
            logging.error(f"Failed to initialize Pygame mixer: {str(e)}")

    def _cleanup_old_files(self):
        """Clean up old output files"""
        try:
            if not os.path.exists("output"):
                os.makedirs("output")
                return
            
            current_time = time.time()
            for file in os.listdir("output"):
                try:
                    file_path = os.path.join("output", file)
                    if os.path.getctime(file_path) < current_time - 24*60*60:  # 24 hours
                        os.remove(file_path)
                except Exception as e:
                    logging.warning(f"Failed to remove old file {file}: {str(e)}")
        except Exception as e:
            logging.error(f"Error during file cleanup: {str(e)}")

    def _browse_sample(self):
        try:
            filetypes = (
                ('WAV files', '*.wav'),
                ('All files', '*.*')
            )
            filename = filedialog.askopenfilename(
                title='Select a voice sample',
                filetypes=filetypes
            )
            if filename:
                if not os.path.exists(filename):
                    logging.error(f"Selected file does not exist: {filename}")
                    messagebox.showerror("Error", "Selected file does not exist")
                    return
                    
                # Validate WAV file
                try:
                    import wave
                    with wave.open(filename, 'rb') as wav_file:
                        if wav_file.getnchannels() not in [1, 2]:
                            raise ValueError("Invalid audio channels")
                except Exception as e:
                    logging.error(f"Invalid WAV file: {str(e)}")
                    messagebox.showerror("Error", "Please select a valid WAV file")
                    return
                
                self.voice_sample_path = filename
                self.sample_path_var.set(os.path.basename(filename))
                self._update_ui_state()
                logging.info(f"Voice sample selected: {filename}")
        except Exception as e:
            logging.error(f"Error browsing for voice sample: {str(e)}")
            messagebox.showerror("Error", f"Failed to open file: {str(e)}")

    def _show_ffmpeg_error(self):
        """Show FFmpeg installation dialog"""
        if messagebox.askyesno("FFmpeg Required",
            "FFmpeg is required but not found. Would you like to install it automatically?",
            icon='question'):
            
            self.status_var.set("Installing FFmpeg...")
            self.progress.pack(pady=10)
            self.progress['value'] = 0
            
            # Install in a separate thread
            def install_thread():
                try:
                    if install_ffmpeg():
                        self.root.after(0, lambda: self._ffmpeg_install_complete(True))
                    else:
                        self.root.after(0, lambda: self._ffmpeg_install_complete(False))
                except Exception as e:
                    logger.error(f"FFmpeg installation error: {str(e)}")
                    self.root.after(0, lambda: self._ffmpeg_install_complete(False))
                    
            threading.Thread(target=install_thread, daemon=True).start()
        else:
            error_msg = """FFmpeg is required for audio processing. Please install it manually:

1. Download FFmpeg from:
   https://github.com/BtbN/FFmpeg-Builds/releases/
   (Download ffmpeg-master-latest-win64-gpl.zip)

2. Extract the ZIP file

3. Copy all files from the 'bin' folder to:
   %LOCALAPPDATA%\\ffmpeg\\bin
   
4. Restart the application

The application will now close."""
            
            messagebox.showerror("FFmpeg Required", error_msg)
            self.root.quit()
            
    def _ffmpeg_install_complete(self, success):
        """Handle FFmpeg installation completion"""
        self.progress.pack_forget()
        
        if success:
            messagebox.showinfo("Success", "FFmpeg installed successfully!")
            self._show_terms_dialog()  # Continue with normal startup
        else:
            messagebox.showerror("Error",
                "Failed to install FFmpeg automatically.\n"
                "Please try installing it manually.")
            self.root.quit()

    def _show_terms_dialog(self):
        if not check_ffmpeg():
            self._show_ffmpeg_error()
            return
            
        terms = """XTTS Voice Cloning - Terms of Use

By using this software, you agree to the following terms:

1. You will not use this software to create misleading or harmful content.
2. You will not use someone else's voice without their explicit permission.
3. You will not use this software for any illegal purposes.
4. You are responsible for any content you create with this software.

This software is provided "as is" without any warranties.

Note: Initial model download may take several minutes. Please be patient.
"""
        if messagebox.askyesno(
            "Terms of Service",
            terms + "\n\nDo you agree to these terms?",
            icon='question',
            detail="You must agree to the terms to continue."
        ):
            self._start_model_loading()
        else:
            logging.info("User declined terms of service")
            messagebox.showinfo(
                "Terms Not Accepted",
                "You must accept the terms to use this software."
            )

    def _start_model_loading(self):
        self.load_btn.config(state=tk.DISABLED)
        self.status_var.set("Loading XTTS model...")
        self.progress.pack(pady=10)
        self.progress['value'] = 0
        self._update_progress("Checking model cache...")
        logging.info("Starting model loading process")
        
        # Start model loading in a separate thread
        threading.Thread(target=self._load_model_thread, daemon=True).start()

    def _load_model_thread(self):
        try:
            # Check model cache first
            cached_model = self.model_cache.get_cached_model(self.model_name)
            
            try:
                # Check FFmpeg first
                if not check_ffmpeg():
                    raise RuntimeError("FFmpeg not available - required for audio processing")
                
                # Import TTS here to avoid loading at startup
                from TTS.api import TTS
                import torch
                
                def progress_callback(message, percent=None):
                    self.root.after(0, lambda: self._update_progress(message, percent))
                
                logger.info("Attempting to load XTTS model")
                
                # Check CUDA availability
                if torch.cuda.is_available():
                    logger.info(f"CUDA available - using GPU: {torch.cuda.get_device_name()}")
                else:
                    logger.info("CUDA not available - using CPU")
                
                if cached_model:
                    self._update_progress("Loading cached model...", 50)
                    try:
                        self.tts = TTS(cached_model)
                        logger.info("Successfully loaded cached model")
                    except Exception as e:
                        logger.error(f"Failed to load cached model: {str(e)}")
                        logger.info("Will attempt fresh download")
                        cached_model = None
                
                if not cached_model:
                    self._update_progress("Starting model download...", 0)
                    try:
                        # Download model with our progress tracking
                        cached_path = self.model_cache.download_model(
                            self.model_name,
                            progress_callback
                        )
                        
                        # Load the downloaded model
                        self._update_progress("Loading downloaded model...", 75)
                        self.tts = TTS(cached_path)
                        logger.info("Successfully loaded downloaded model")
                    except Exception as e:
                        raise RuntimeError(f"Failed to download/load model: {str(e)}")
                
                self.model_loaded = True
                success = True
                message = "Model loaded successfully!"
                logging.info("XTTS model loaded successfully")
                
            except Exception as e:
                success = False
                message = f"Error loading model: {str(e)}"
                logging.error(f"Model loading error: {str(e)}")
                print(f"Model loading error: {str(e)}", file=sys.stderr)
            
            # Run garbage collection after model loading
            gc.collect()
            
            # Update UI in the main thread
            self.root.after(0, lambda: self._model_loading_complete(success, message))
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logging.error(f"Unexpected error during model loading: {str(e)}")
            print(f"Unexpected error: {str(e)}", file=sys.stderr)
            self.root.after(0, lambda: self._model_loading_complete(False, error_msg))

    def _model_loading_complete(self, success, message):
        self.progress.pack_forget()
        
        if success:
            self._update_progress("Model loaded successfully!", 100)
            self.status_var.set(message)
            messagebox.showinfo("Success", message)
            self._update_ui_state()
        else:
            self.status_var.set("Failed to load model")
            self.progress_var.set("")
            messagebox.showerror("Error", message)
            self.load_btn.config(state=tk.NORMAL)

    def _update_ui_state(self):
        if self.model_loaded and self.voice_sample_path:
            self.generate_btn.config(state=tk.NORMAL)
        else:
            self.generate_btn.config(state=tk.DISABLED)

    def _generate_speech(self):
        if not self.model_loaded:
            logger.error("Attempted to generate speech without loaded model")
            messagebox.showerror("Error", "Model not loaded")
            return
            
        if not self.voice_sample_path or not os.path.exists(self.voice_sample_path):
            logger.error("Invalid or missing voice sample path")
            messagebox.showerror("Error", "Please select a valid voice sample")
            return
            
        text = self.text_input.get("1.0", tk.END).strip()
        if not text:
            logger.warning("Attempted to generate speech with empty text")
            messagebox.showerror("Error", "Please enter some text to speak")
            return
            
        if len(text) > 1000:  # Add reasonable text length limit
            logger.warning("Text input exceeds length limit")
            messagebox.showerror("Error", "Text is too long (maximum 1000 characters)")
            return
            
        # Disable buttons during generation
        self.generate_btn.config(state=tk.DISABLED)
        self.play_btn.config(state=tk.DISABLED)
        self.status_var.set("Generating speech...")
        self.progress.pack(pady=10)
        self.progress['value'] = 0
        
        logger.info("Starting speech generation")
        # Start generation in a separate thread
        threading.Thread(
            target=self._generate_speech_thread,
            args=(text,),
            daemon=True
        ).start()

    def _generate_speech_thread(self, text):
        try:
            # Create output directory if it doesn't exist
            os.makedirs("output", exist_ok=True)
            
            # Generate a unique filename with timestamp
            timestamp = int(time.time())
            output_file = os.path.join("output", f"output_{timestamp}.wav")
            
            logger.info(f"Generating speech to file: {output_file}")
            self._update_progress("Processing text...", 25)
            
            # Generate speech with the model
            with nostdout():
                self.tts.tts_to_file(
                    text=text,
                    speaker_wav=self.voice_sample_path,
                    language="en",
                    file_path=output_file
                )
            
            self.last_audio_file = output_file
            logger.info("Speech generation completed successfully")
            
            # Update UI in the main thread
            self.root.after(0, lambda: self._generation_complete(
                True, 
                "Speech generated successfully!",
                output_file
            ))
            
        except Exception as e:
            error_msg = f"Error generating speech: {str(e)}"
            logger.error(f"Generation error: {str(e)}")
            print(f"Generation error: {str(e)}", file=sys.stderr)
            self.root.after(0, lambda: self._generation_complete(False, error_msg, None))

    def _generation_complete(self, success, message, output_file):
        self.progress.pack_forget()
        
        if success:
            self._update_progress("Speech generated successfully!", 100)
            self.status_var.set(message)
            self.play_btn.config(state=tk.NORMAL)
            messagebox.showinfo("Success", message)
        else:
            self.status_var.set("Failed to generate speech")
            self.progress_var.set("")
            messagebox.showerror("Error", message)
        
        self.generate_btn.config(state=tk.NORMAL)

    def _play_audio(self):
        if not self.last_audio_file or not os.path.exists(self.last_audio_file):
            logger.error("No audio file available for playback")
            messagebox.showerror("Error", "No audio file to play")
            return
            
        if not self.pygame_initialized:
            logger.error("Pygame mixer not initialized")
            messagebox.showerror("Error", "Audio playback not available")
            return
            
        try:
            import pygame
            pygame.mixer.music.load(self.last_audio_file)
            pygame.mixer.music.play()
            logger.info("Playing audio file")
        except Exception as e:
            logger.error(f"Audio playback error: {str(e)}")
            messagebox.showerror("Error", f"Could not play audio: {str(e)}")

    def _on_closing(self):
        """Handle cleanup when closing the application"""
        try:
            if self.pygame_initialized:
                import pygame
                pygame.mixer.quit()
                logger.info("Pygame mixer cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
            
        self.root.destroy()

def main():
    logger.info("Starting XTTS GUI application")
    
    # Initialize model cache
    model_cache = ModelCache()
    
    # Set up the root window
    root = tk.Tk()
    root.title("XTTS Voice Cloning")
    
    # Set window icon if available
    try:
        root.iconbitmap(default='icon.ico')
    except Exception as e:
        logger.warning(f"Could not load window icon: {str(e)}")
    
    # Create and run the application
    app = XTTSApp(root, model_cache)
    root.mainloop()

if __name__ == "__main__":
    main()
