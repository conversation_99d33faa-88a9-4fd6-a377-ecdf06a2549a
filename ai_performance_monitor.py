#!/usr/bin/env python3
"""
AI Performance Monitor
Real-time AI monitoring and optimization for Egyptian TTS
"""

import os
import sys
import json
import time
import threading
from datetime import datetime

class AIPerformanceMonitor:
    """Real-time AI performance monitoring system"""
    
    def __init__(self):
        self.monitoring = False
        self.performance_data = []
        self.ai_insights = []
        self.optimization_queue = []
        
    def start_monitoring(self):
        """Start real-time performance monitoring"""
        print("🤖 Starting AI Performance Monitor")
        print("=" * 35)
        
        self.monitoring = True
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self._monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        print("✅ AI monitoring started")
        print("📊 Real-time performance tracking active")
        return True
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Collect performance metrics
                metrics = self._collect_metrics()
                
                # AI analysis of metrics
                insights = self._ai_analyze_metrics(metrics)
                
                # Store data
                self.performance_data.append(metrics)
                self.ai_insights.extend(insights)
                
                # Check for optimization opportunities
                self._check_optimization_opportunities()
                
                # Wait before next check
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {str(e)}")
                time.sleep(60)
    
    def _collect_metrics(self):
        """Collect current performance metrics"""
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "system_performance": self._get_system_performance(),
            "audio_quality": self._assess_audio_quality(),
            "training_status": self._check_training_status(),
            "user_activity": self._detect_user_activity()
        }
        return metrics
    
    def _get_system_performance(self):
        """Get system performance metrics"""
        # Simulate system performance monitoring
        return {
            "cpu_usage": 45 + (time.time() % 30),  # Simulated CPU usage
            "memory_usage": 60 + (time.time() % 20),  # Simulated memory usage
            "processing_speed": 85 + (time.time() % 15),  # Simulated speed
            "response_time": 1.2 + (time.time() % 2)  # Simulated response time
        }
    
    def _assess_audio_quality(self):
        """AI assessment of current audio quality"""
        # Check recent audio files
        recent_files = []
        
        # Look for recently generated files
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith('.wav') and 'test' in file.lower():
                    file_path = os.path.join(root, file)
                    mod_time = os.path.getmtime(file_path)
                    if time.time() - mod_time < 3600:  # Files from last hour
                        recent_files.append(file_path)
        
        if recent_files:
            # Simulate AI quality assessment
            quality_scores = []
            for file_path in recent_files:
                file_size = os.path.getsize(file_path)
                # Quality heuristic based on file size and name
                base_quality = min(95, (file_size / 100000) * 40 + 50)
                quality_scores.append(base_quality)
            
            return {
                "average_quality": sum(quality_scores) / len(quality_scores),
                "quality_trend": "improving" if len(quality_scores) > 1 else "stable",
                "files_analyzed": len(recent_files)
            }
        
        return {"average_quality": 75, "quality_trend": "unknown", "files_analyzed": 0}
    
    def _check_training_status(self):
        """Check current training status"""
        training_indicators = {
            "trained_samples_count": 0,
            "training_completeness": 0,
            "last_training_time": None
        }
        
        # Check for trained samples
        if os.path.exists("trained_egyptian_samples"):
            files = [f for f in os.listdir("trained_egyptian_samples") if f.endswith('.wav')]
            training_indicators["trained_samples_count"] = len(files)
            training_indicators["training_completeness"] = min(100, len(files) * 7.7)  # 13 files = 100%
        
        # Check for recent training activity
        training_files = ["ai_training_plan.json", "egyptian_model_config.json"]
        for file in training_files:
            if os.path.exists(file):
                mod_time = os.path.getmtime(file)
                if not training_indicators["last_training_time"] or mod_time > training_indicators["last_training_time"]:
                    training_indicators["last_training_time"] = mod_time
        
        return training_indicators
    
    def _detect_user_activity(self):
        """Detect user activity patterns"""
        # Check for recent file modifications (user activity indicator)
        recent_activity = False
        activity_files = 0
        
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith(('.wav', '.json', '.md')):
                    file_path = os.path.join(root, file)
                    mod_time = os.path.getmtime(file_path)
                    if time.time() - mod_time < 1800:  # Last 30 minutes
                        recent_activity = True
                        activity_files += 1
        
        return {
            "recent_activity": recent_activity,
            "activity_level": "high" if activity_files > 5 else "medium" if activity_files > 2 else "low",
            "files_modified": activity_files
        }
    
    def _ai_analyze_metrics(self, metrics):
        """AI analysis of collected metrics"""
        insights = []
        
        # Analyze system performance
        sys_perf = metrics["system_performance"]
        if sys_perf["cpu_usage"] > 80:
            insights.append({
                "type": "performance_warning",
                "message": "High CPU usage detected - consider optimizing processing",
                "priority": "medium",
                "suggestion": "Reduce batch size or enable GPU acceleration"
            })
        
        if sys_perf["response_time"] > 3:
            insights.append({
                "type": "performance_warning", 
                "message": "Slow response time detected",
                "priority": "high",
                "suggestion": "Check system resources and optimize parameters"
            })
        
        # Analyze audio quality
        audio_qual = metrics["audio_quality"]
        if audio_qual["average_quality"] < 70:
            insights.append({
                "type": "quality_alert",
                "message": "Audio quality below optimal threshold",
                "priority": "high",
                "suggestion": "Review training data and model parameters"
            })
        
        # Analyze training status
        training = metrics["training_status"]
        if training["training_completeness"] < 50:
            insights.append({
                "type": "training_recommendation",
                "message": "Training appears incomplete",
                "priority": "medium",
                "suggestion": "Continue training with additional samples"
            })
        
        # Analyze user activity
        activity = metrics["user_activity"]
        if activity["recent_activity"] and activity["activity_level"] == "high":
            insights.append({
                "type": "optimization_opportunity",
                "message": "High user activity detected - good time for optimization",
                "priority": "low",
                "suggestion": "Run AI parameter optimization"
            })
        
        return insights
    
    def _check_optimization_opportunities(self):
        """Check for AI optimization opportunities"""
        # Look for patterns in recent insights
        recent_insights = self.ai_insights[-10:]  # Last 10 insights
        
        # Count insight types
        insight_counts = {}
        for insight in recent_insights:
            insight_type = insight.get("type", "unknown")
            insight_counts[insight_type] = insight_counts.get(insight_type, 0) + 1
        
        # Generate optimization recommendations
        if insight_counts.get("performance_warning", 0) >= 3:
            self.optimization_queue.append({
                "type": "performance_optimization",
                "action": "Optimize system parameters for better performance",
                "urgency": "high"
            })
        
        if insight_counts.get("quality_alert", 0) >= 2:
            self.optimization_queue.append({
                "type": "quality_optimization",
                "action": "Retrain model with higher quality data",
                "urgency": "medium"
            })
    
    def get_ai_dashboard(self):
        """Get AI dashboard with current status"""
        if not self.performance_data:
            return {"status": "No data available"}
        
        latest_metrics = self.performance_data[-1]
        recent_insights = self.ai_insights[-5:]
        
        dashboard = {
            "monitoring_status": "active" if self.monitoring else "inactive",
            "last_update": latest_metrics["timestamp"],
            "current_performance": {
                "system_health": "good" if latest_metrics["system_performance"]["cpu_usage"] < 70 else "warning",
                "audio_quality": latest_metrics["audio_quality"]["average_quality"],
                "training_progress": latest_metrics["training_status"]["training_completeness"]
            },
            "recent_insights": recent_insights,
            "optimization_queue": self.optimization_queue,
            "recommendations": self._generate_recommendations()
        }
        
        return dashboard
    
    def _generate_recommendations(self):
        """Generate AI recommendations based on current state"""
        recommendations = []
        
        if self.performance_data:
            latest = self.performance_data[-1]
            
            # Performance recommendations
            if latest["system_performance"]["cpu_usage"] > 75:
                recommendations.append("Consider enabling GPU acceleration for better performance")
            
            # Quality recommendations
            if latest["audio_quality"]["average_quality"] < 80:
                recommendations.append("Collect additional high-quality training samples")
            
            # Training recommendations
            if latest["training_status"]["training_completeness"] < 80:
                recommendations.append("Complete training with remaining pronunciation patterns")
        
        return recommendations
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring = False
        print("🛑 AI monitoring stopped")
    
    def save_monitoring_report(self):
        """Save comprehensive monitoring report"""
        report_data = {
            "monitoring_session": {
                "start_time": self.performance_data[0]["timestamp"] if self.performance_data else None,
                "end_time": datetime.now().isoformat(),
                "total_metrics_collected": len(self.performance_data),
                "total_insights_generated": len(self.ai_insights)
            },
            "performance_summary": self._summarize_performance(),
            "ai_insights_summary": self._summarize_insights(),
            "optimization_recommendations": self.optimization_queue
        }
        
        with open("ai_monitoring_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print("✅ AI monitoring report saved: ai_monitoring_report.json")
        return report_data
    
    def _summarize_performance(self):
        """Summarize performance data"""
        if not self.performance_data:
            return {}
        
        # Calculate averages
        cpu_usage = [m["system_performance"]["cpu_usage"] for m in self.performance_data]
        audio_quality = [m["audio_quality"]["average_quality"] for m in self.performance_data]
        
        return {
            "average_cpu_usage": sum(cpu_usage) / len(cpu_usage),
            "average_audio_quality": sum(audio_quality) / len(audio_quality),
            "performance_trend": "improving" if cpu_usage[-1] < cpu_usage[0] else "stable"
        }
    
    def _summarize_insights(self):
        """Summarize AI insights"""
        insight_types = {}
        for insight in self.ai_insights:
            insight_type = insight.get("type", "unknown")
            insight_types[insight_type] = insight_types.get(insight_type, 0) + 1
        
        return {
            "total_insights": len(self.ai_insights),
            "insight_breakdown": insight_types,
            "most_common_insight": max(insight_types.items(), key=lambda x: x[1])[0] if insight_types else None
        }

def main():
    """Main AI performance monitor"""
    print("🤖 AI Performance Monitor for Egyptian TTS")
    print("=" * 45)
    
    monitor = AIPerformanceMonitor()
    
    try:
        # Start monitoring
        monitor.start_monitoring()
        
        print("\n📊 AI Performance Monitor Running...")
        print("Press Ctrl+C to stop monitoring and generate report")
        
        # Keep monitoring until interrupted
        while True:
            time.sleep(10)
            
            # Display dashboard every 60 seconds
            dashboard = monitor.get_ai_dashboard()
            if dashboard.get("monitoring_status") == "active":
                print(f"\n🤖 AI Status: {dashboard['current_performance']['system_health'].upper()}")
                print(f"🎯 Audio Quality: {dashboard['current_performance']['audio_quality']:.1f}/100")
                print(f"📈 Training Progress: {dashboard['current_performance']['training_progress']:.1f}%")
                
                if dashboard["recent_insights"]:
                    print(f"💡 Latest Insight: {dashboard['recent_insights'][-1]['message']}")
    
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping AI monitoring...")
        monitor.stop_monitoring()
        
        # Generate final report
        report = monitor.save_monitoring_report()
        
        print("\n📋 AI Monitoring Session Complete!")
        print(f"📊 Metrics Collected: {report['monitoring_session']['total_metrics_collected']}")
        print(f"💡 Insights Generated: {report['monitoring_session']['total_insights_generated']}")
        print(f"🎯 Optimization Opportunities: {len(report['optimization_recommendations'])}")

if __name__ == "__main__":
    main()
