#!/usr/bin/env python3
"""
Egyptian Model Training Script
Actual training process for Egyptian pronunciation
"""

import os
import sys
import torch
import json

def run_training():
    """Run the actual Egyptian model training"""
    print("🇪🇬 Starting Egyptian Model Training")
    print("=" * 40)

    # Check CUDA availability
    if torch.cuda.is_available():
        print(f"✅ Using GPU: {torch.cuda.get_device_name()}")
        device = "cuda"
    else:
        print("⚠️ Using CPU (training will be slower)")
        device = "cpu"

    try:
        # Import TTS training components
        from TTS.tts.configs.xtts_config import XttsConfig
        from TTS.tts.models.xtts import Xtts
        from trainer import Trainer, TrainerArgs

        print("🔧 Loading training configuration...")
        config = XttsConfig()
        config.load_json("egyptian_training_config.json")

        print("🤖 Initializing XTTS model...")
        model = Xtts.init_from_config(config)

        print("📥 Loading pretrained XTTS weights...")
        # Load the base XTTS model
        try:
            # Try to get the model path
            model_path = model.get_model_file_path()
            if model_path and os.path.exists(model_path):
                model.load_checkpoint(config, checkpoint_dir=os.path.dirname(model_path), eval=False)
                print("✅ Loaded pretrained XTTS weights")
            else:
                print("⚠️ Using default model initialization (no pretrained weights)")
        except Exception as e:
            print(f"⚠️ Could not load pretrained weights: {str(e)}")
            print("🔧 Continuing with default initialization")

        print("🏃 Starting Egyptian pronunciation training...")
        print("⏱️ This will take 1-3 hours depending on your hardware")
        print("📊 Monitor progress in: egyptian_trained_model/")

        # Setup trainer
        trainer_args = TrainerArgs()
        trainer = Trainer(
            trainer_args,
            config,
            output_path=config.output_path,
            model=model
        )

        # Start training
        trainer.fit()

        print("🎉 Egyptian model training complete!")
        print("📁 Trained model saved in: egyptian_trained_model/")

    except ImportError as e:
        print(f"❌ Missing dependency: {str(e)}")
        print("💡 Install with: pip install trainer")
        return False
    except Exception as e:
        print(f"❌ Training error: {str(e)}")
        return False

    return True

if __name__ == "__main__":
    run_training()
