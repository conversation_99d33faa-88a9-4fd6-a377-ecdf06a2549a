#!/usr/bin/env python3
"""
Simple Voice Training Script for Egyptian Accent
Test multiple voice samples and find the best ones
"""

import os
import sys
from TTS.api import TTS

def test_voice_samples():
    """Test multiple voice samples with same text"""
    
    # Initialize TTS
    tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)
    
    # Egyptian test text
    test_text = "أهلاً وسهلاً! إزيك النهاردة؟ أنا مصري من القاهرة."
    
    # Voice samples directory
    samples_dir = "voice_samples"
    if not os.path.exists(samples_dir):
        print(f"Create a '{samples_dir}' directory and add your voice samples")
        return
    
    # Test each voice sample
    for sample_file in os.listdir(samples_dir):
        if sample_file.endswith(('.wav', '.mp3')):
            print(f"Testing: {sample_file}")
            
            sample_path = os.path.join(samples_dir, sample_file)
            output_path = f"test_output_{sample_file}.wav"
            
            try:
                tts.tts_to_file(
                    text=test_text,
                    speaker_wav=sample_path,
                    language="ar",
                    file_path=output_path
                )
                print(f"✅ Generated: {output_path}")
            except Exception as e:
                print(f"❌ Error with {sample_file}: {str(e)}")

if __name__ == "__main__":
    test_voice_samples()
