@echo off
title Egyptian TTS with Emotions
color 0A

echo.
echo     🇪🇬 Egyptian TTS GUI with Emotions 🎭
echo     =====================================
echo.
echo     Starting the enhanced XTTS GUI...
echo     Features: Egyptian accent, emotion controls
echo.

REM Change to project directory
cd /d "%~dp0"

REM Run the Egyptian emotions GUI
C:\Users\<USER>\miniconda3\envs\xtts\python.exe xtts_gui_egyptian_emotions.py

REM If error, show message
if errorlevel 1 (
    echo.
    echo     ❌ Error starting the application
    echo     Check that Python and dependencies are installed
    echo.
    pause
)
