#!/usr/bin/env python3
"""
Egyptian Accent Model Training Guide
Complete setup for training XTTS model on Egyptian Arabic data
"""

import os
import sys
import json
import subprocess

def check_training_requirements():
    """Check if system meets training requirements"""
    print("🔍 Checking Training Requirements")
    print("=" * 40)
    
    requirements = {
        "Python Libraries": [
            "TTS >= 0.22.0",
            "torch >= 1.13.0", 
            "torchaudio >= 0.13.0",
            "trainer >= 0.0.32",
            "librosa >= 0.10.0",
            "pandas >= 1.4.0",
            "numpy >= 1.21.0"
        ],
        "Hardware": [
            "GPU with 8GB+ VRAM (recommended)",
            "16GB+ RAM",
            "50GB+ free disk space",
            "CUDA 11.8+ (for GPU training)"
        ],
        "Data Requirements": [
            "1-10 hours of Egyptian speech",
            "High-quality audio (22kHz+)",
            "Accurate Arabic transcriptions",
            "Multiple speakers (recommended)"
        ]
    }
    
    for category, items in requirements.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  • {item}")
    
    # Check current environment
    print("\n🔧 Current Environment Check:")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        if torch.cuda.is_available():
            print(f"✅ CUDA: {torch.version.cuda}")
            print(f"✅ GPU: {torch.cuda.get_device_name()}")
        else:
            print("⚠️ CUDA: Not available (CPU training will be very slow)")
    except ImportError:
        print("❌ PyTorch: Not installed")
    
    try:
        import TTS
        print(f"✅ TTS: {TTS.__version__}")
    except ImportError:
        print("❌ TTS: Not installed")

def create_dataset_structure():
    """Create proper dataset structure for training"""
    print("\n📁 Creating Dataset Structure")
    print("=" * 35)
    
    structure = {
        "egyptian_dataset/": {
            "wavs/": "Audio files (.wav format)",
            "metadata.csv": "Transcription file",
            "train.txt": "Training file list", 
            "eval.txt": "Evaluation file list",
            "config.json": "Training configuration"
        }
    }
    
    print("Required folder structure:")
    for folder, contents in structure.items():
        print(f"\n{folder}")
        if isinstance(contents, dict):
            for subfolder, description in contents.items():
                print(f"  ├── {subfolder} - {description}")
        else:
            print(f"  └── {contents}")
    
    # Create the structure
    base_dir = "egyptian_dataset"
    os.makedirs(os.path.join(base_dir, "wavs"), exist_ok=True)
    
    print(f"\n✅ Created: {base_dir}/wavs/")
    print("📝 Next steps:")
    print("1. Add your Egyptian audio files to wavs/ folder")
    print("2. Create metadata.csv with transcriptions")
    print("3. Run the training script")

def create_metadata_template():
    """Create metadata.csv template"""
    print("\n📝 Creating Metadata Template")
    print("=" * 35)
    
    metadata_content = """audio_file|text|speaker_name
speaker1_001.wav|أهلاً وسهلاً! إزيك النهاردة؟|speaker1
speaker1_002.wav|أنا مصري من القاهرة والحمد لله.|speaker1
speaker1_003.wav|يلا بينا نروح نتمشى شوية في وسط البلد.|speaker1
speaker2_001.wav|والله العظيم ده أحلى كلام سمعته النهاردة!|speaker2
speaker2_002.wav|اتفضل اشرب شاي واقعد معانا شوية.|speaker2
speaker2_003.wav|مصر أم الدنيا وشعبها كريم قوي.|speaker2"""
    
    with open("egyptian_dataset/metadata.csv", "w", encoding="utf-8") as f:
        f.write(metadata_content)
    
    print("✅ Created: egyptian_dataset/metadata.csv")
    print("\n📋 Metadata format:")
    print("• audio_file: filename in wavs/ folder")
    print("• text: Arabic transcription")
    print("• speaker_name: unique speaker identifier")
    print("\n⚠️ Important:")
    print("• Use | (pipe) as separator")
    print("• Ensure accurate Arabic transcriptions")
    print("• Include Egyptian dialect words")

def create_training_config():
    """Create training configuration"""
    print("\n⚙️ Creating Training Configuration")
    print("=" * 40)
    
    config = {
        "model_name": "xtts",
        "run_name": "egyptian_xtts",
        "run_description": "XTTS fine-tuned on Egyptian Arabic",
        
        # Dataset config
        "datasets": [
            {
                "name": "egyptian_dataset",
                "path": "./egyptian_dataset/",
                "meta_file_train": "metadata.csv",
                "language": "ar"
            }
        ],
        
        # Model config
        "model_args": {
            "gpt_batch_size": 1,
            "enable_redaction": False,
            "kv_cache": True,
            "gpt_max_audio_len": 229376,
            "gpt_max_text_len": 200,
            "gpt_max_new_tokens": 1024,
            "gpt_min_audio_len": 22050,
            "mel_norm_file": None,
            "dvae_checkpoint": None,
            "xtts_checkpoint": None,
            "tokenizer_file": None,
            "gpt_num_audio_tokens": 1024,
            "gpt_start_audio_token": 1024,
            "gpt_stop_audio_token": 1025,
            "gpt_use_masking_gt_prompt_approach": True,
            "gpt_use_perceiver_resampler": True
        },
        
        # Training config
        "audio": {
            "sample_rate": 22050,
            "output_sample_rate": 24000
        },
        
        "batch_size": 2,
        "eval_batch_size": 1,
        "num_loader_workers": 0,
        "num_eval_loader_workers": 0,
        "run_eval": True,
        "test_delay_epochs": 5,
        
        "epochs": 100,
        "save_step": 1000,
        "eval_step": 1000,
        "log_step": 100,
        "save_n_checkpoints": 5,
        
        "lr": 5e-06,
        "weight_decay": 1e-06,
        "optimizer": "AdamW",
        "scheduler": "MultiStepLR",
        "lr_scheduler_params": {
            "milestones": [50000, 150000, 300000],
            "gamma": 0.5,
            "last_epoch": -1
        },
        
        "output_path": "./egyptian_xtts_training/",
        
        # Logging
        "logger": "tensorboard",
        "logger_uri": None,
        
        # Mixed precision
        "mixed_precision": False,
        
        # Distributed training
        "distributed_backend": "nccl",
        "distributed_url": "tcp://localhost:54321",
        
        # Checkpointing
        "restore_path": None,
        "resume": False,
        
        # Evaluation
        "run_eval_steps": None,
        "save_all_best": False,
        "save_checkpoints": True,
        "print_step": 25,
        "plot_step": 100,
        "model_param_stats": False,
        
        # Early stopping
        "early_stop_patience": 10,
        "early_stop_threshold": 0.5,
        
        "use_phonemes": False,
        "phonemizer": None,
        "phoneme_language": None,
        
        "compute_input_seq_cache": True,
        "precompute_num_workers": 0,
        
        "start_by_longest": False
    }
    
    with open("egyptian_dataset/config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ Created: egyptian_dataset/config.json")
    print("\n🎯 Key training parameters:")
    print(f"• Epochs: {config['epochs']}")
    print(f"• Batch size: {config['batch_size']}")
    print(f"• Learning rate: {config['lr']}")
    print(f"• Save every: {config['save_step']} steps")

def create_training_script():
    """Create the actual training script"""
    print("\n💻 Creating Training Script")
    print("=" * 30)
    
    script_content = '''#!/usr/bin/env python3
"""
Egyptian XTTS Training Script
Fine-tune XTTS model on Egyptian Arabic data
"""

import os
import sys
import torch
from trainer import Trainer, TrainerArgs
from TTS.tts.configs.xtts_config import XttsConfig
from TTS.tts.models.xtts import Xtts

def main():
    """Main training function"""
    print("🇪🇬 Starting Egyptian XTTS Training")
    print("=" * 40)
    
    # Check CUDA
    if torch.cuda.is_available():
        print(f"✅ Using GPU: {torch.cuda.get_device_name()}")
        device = "cuda"
    else:
        print("⚠️ Using CPU (training will be very slow)")
        device = "cpu"
    
    # Load config
    config_path = "egyptian_dataset/config.json"
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        print("Run egyptian_model_training.py first to create config")
        return
    
    config = XttsConfig()
    config.load_json(config_path)
    
    # Initialize model
    print("🔧 Initializing XTTS model...")
    model = Xtts.init_from_config(config)
    
    # Load pretrained weights
    print("📥 Loading pretrained XTTS weights...")
    checkpoint_dir = model.get_model_file_path()
    model.load_checkpoint(config, checkpoint_dir=checkpoint_dir, eval=False)
    
    # Setup trainer
    trainer_args = TrainerArgs()
    trainer_args.restore_path = None
    
    trainer = Trainer(
        trainer_args,
        config,
        output_path=config.output_path,
        model=model,
        train_samples=None,
        eval_samples=None
    )
    
    # Start training
    print("🚀 Starting training...")
    print("📊 Monitor progress in: egyptian_xtts_training/")
    print("🔗 Tensorboard: tensorboard --logdir egyptian_xtts_training/")
    
    trainer.fit()

if __name__ == "__main__":
    main()
'''
    
    with open("train_egyptian_xtts.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ Created: train_egyptian_xtts.py")
    print("\n🚀 To start training:")
    print("1. Prepare your Egyptian dataset")
    print("2. Run: python train_egyptian_xtts.py")
    print("3. Monitor with: tensorboard --logdir egyptian_xtts_training/")

def create_data_preparation_script():
    """Create script to prepare Egyptian data"""
    print("\n📊 Creating Data Preparation Script")
    print("=" * 40)
    
    script_content = '''#!/usr/bin/env python3
"""
Egyptian Data Preparation Script
Prepare audio files and create training splits
"""

import os
import pandas as pd
import librosa
import soundfile as sf
from sklearn.model_selection import train_test_split

def prepare_audio_files():
    """Process and normalize audio files"""
    print("🎵 Processing audio files...")
    
    wavs_dir = "egyptian_dataset/wavs"
    if not os.path.exists(wavs_dir):
        print(f"❌ Directory not found: {wavs_dir}")
        return
    
    processed_count = 0
    
    for filename in os.listdir(wavs_dir):
        if filename.endswith('.wav'):
            filepath = os.path.join(wavs_dir, filename)
            
            # Load audio
            audio, sr = librosa.load(filepath, sr=22050)
            
            # Normalize audio
            audio = librosa.util.normalize(audio)
            
            # Save normalized audio
            sf.write(filepath, audio, 22050)
            processed_count += 1
    
    print(f"✅ Processed {processed_count} audio files")

def create_train_eval_split():
    """Create training and evaluation splits"""
    print("📝 Creating train/eval splits...")
    
    metadata_path = "egyptian_dataset/metadata.csv"
    if not os.path.exists(metadata_path):
        print(f"❌ Metadata file not found: {metadata_path}")
        return
    
    # Load metadata
    df = pd.read_csv(metadata_path, sep='|')
    
    # Split data (80% train, 20% eval)
    train_df, eval_df = train_test_split(df, test_size=0.2, random_state=42)
    
    # Save splits
    train_df.to_csv("egyptian_dataset/train.txt", sep='|', index=False)
    eval_df.to_csv("egyptian_dataset/eval.txt", sep='|', index=False)
    
    print(f"✅ Training samples: {len(train_df)}")
    print(f"✅ Evaluation samples: {len(eval_df)}")

def validate_dataset():
    """Validate the dataset"""
    print("🔍 Validating dataset...")
    
    issues = []
    
    # Check metadata
    metadata_path = "egyptian_dataset/metadata.csv"
    if os.path.exists(metadata_path):
        df = pd.read_csv(metadata_path, sep='|')
        
        for idx, row in df.iterrows():
            audio_file = f"egyptian_dataset/wavs/{row['audio_file']}"
            
            if not os.path.exists(audio_file):
                issues.append(f"Missing audio: {row['audio_file']}")
            
            if pd.isna(row['text']) or len(row['text'].strip()) == 0:
                issues.append(f"Empty text for: {row['audio_file']}")
    
    if issues:
        print("❌ Dataset issues found:")
        for issue in issues[:10]:  # Show first 10 issues
            print(f"  • {issue}")
        if len(issues) > 10:
            print(f"  ... and {len(issues) - 10} more issues")
    else:
        print("✅ Dataset validation passed!")

def main():
    """Main data preparation function"""
    print("🇪🇬 Egyptian Dataset Preparation")
    print("=" * 35)
    
    prepare_audio_files()
    create_train_eval_split()
    validate_dataset()
    
    print("\n🎯 Dataset ready for training!")

if __name__ == "__main__":
    main()
'''
    
    with open("prepare_egyptian_data.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ Created: prepare_egyptian_data.py")
    print("\n📋 Data preparation steps:")
    print("1. Add audio files to egyptian_dataset/wavs/")
    print("2. Update metadata.csv with transcriptions")
    print("3. Run: python prepare_egyptian_data.py")

def installation_guide():
    """Provide installation guide"""
    print("\n📦 Installation Guide")
    print("=" * 25)
    
    commands = [
        "# Install training dependencies",
        "pip install TTS[all]",
        "pip install trainer",
        "pip install librosa soundfile",
        "pip install pandas scikit-learn",
        "",
        "# For GPU training (if you have CUDA)",
        "pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118",
        "",
        "# Verify installation",
        "python -c \"import torch; print('CUDA available:', torch.cuda.is_available())\"",
        "python -c \"import TTS; print('TTS version:', TTS.__version__)\""
    ]
    
    print("Run these commands:")
    for cmd in commands:
        if cmd.startswith("#"):
            print(f"\n{cmd}")
        elif cmd == "":
            print()
        else:
            print(f"  {cmd}")

def main():
    """Main function"""
    print("🇪🇬 Egyptian XTTS Model Training Setup")
    print("=" * 45)
    
    print("This will create a complete training setup for Egyptian accent.")
    print("Choose what to create:")
    print("1. Check training requirements")
    print("2. Create dataset structure")
    print("3. Create training configuration")
    print("4. Create training scripts")
    print("5. Installation guide")
    print("6. Create everything")
    
    choice = input("\nEnter choice (1-6): ").strip()
    
    if choice == "1":
        check_training_requirements()
    elif choice == "2":
        create_dataset_structure()
        create_metadata_template()
    elif choice == "3":
        create_training_config()
    elif choice == "4":
        create_training_script()
        create_data_preparation_script()
    elif choice == "5":
        installation_guide()
    elif choice == "6":
        check_training_requirements()
        create_dataset_structure()
        create_metadata_template()
        create_training_config()
        create_training_script()
        create_data_preparation_script()
        installation_guide()
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    main()
