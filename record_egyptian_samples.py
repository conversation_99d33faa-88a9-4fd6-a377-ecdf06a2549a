#!/usr/bin/env python3
"""
Egyptian Voice Recording Script
Record high-quality Egyptian samples for training
"""

import sounddevice as sd
import soundfile as sf
import numpy as np
import time

def record_sample(filename, duration=10, sample_rate=22050):
    """Record a single audio sample"""
    print(f"🎤 Recording {filename} for {duration} seconds...")
    print("3... 2... 1... START!")
    
    # Record audio
    audio = sd.rec(int(duration * sample_rate), 
                   samplerate=sample_rate, 
                   channels=1, 
                   dtype=np.float32)
    sd.wait()  # Wait for recording to complete
    
    # Save audio
    sf.write(f"egyptian_dataset/wavs/{filename}", audio, sample_rate)
    print(f"✅ Saved: {filename}")

def main():
    """Record Egyptian samples"""
    print("🇪🇬 Egyptian Voice Recording Session")
    print("=" * 40)
    
    # Egyptian texts to record
    texts = [
        ("speaker1_001.wav", "أهلاً وسهلاً! إزيك النهاردة؟"),
        ("speaker1_002.wav", "أنا مصري من القاهرة والحمد لله."),
        ("speaker1_003.wav", "يلا بينا نروح نتمشى شوية في وسط البلد."),
        ("speaker1_004.wav", "والله العظيم ده أحلى كلام سمعته النهاردة!"),
        ("speaker1_005.wav", "اتفضل اشرب شاي واقعد معانا شوية."),
        ("speaker1_006.wav", "مصر أم الدنيا وشعبها كريم قوي."),
        ("speaker1_007.wav", "كان يا ما كان في قديم الزمان واحد مصري."),
        ("speaker1_008.wav", "القاهرة مدينة جميلة والنيل نهر عظيم."),
        ("speaker1_009.wav", "الطقس النهاردة حلو قوي ومناسب للخروج."),
        ("speaker1_010.wav", "ربنا يخليك ويحفظك من كل شر.")
    ]
    
    print("📝 You will record these Egyptian texts:")
    for i, (filename, text) in enumerate(texts, 1):
        print(f"{i:2d}. {text}")
    
    input("\nPress Enter when ready to start recording...")
    
    for filename, text in texts:
        print(f"\n📝 Next text: {text}")
        input("Press Enter to start recording...")
        record_sample(filename, duration=8)
        time.sleep(1)
    
    print("\n🎉 Recording session complete!")
    print("Next steps:")
    print("1. Check audio quality in egyptian_dataset/wavs/")
    print("2. Re-record any poor quality samples")
    print("3. Run: python prepare_egyptian_data.py")

if __name__ == "__main__":
    main()
