{"model_name": "xtts", "run_name": "egyptian_xtts_minimal", "run_description": "Minimal XTTS training on Egyptian Arabic", "datasets": [{"name": "egyptian_dataset", "path": "./egyptian_dataset/", "meta_file_train": "metadata.csv", "language": "ar"}], "audio": {"sample_rate": 22050, "output_sample_rate": 24000}, "batch_size": 1, "eval_batch_size": 1, "num_loader_workers": 0, "num_eval_loader_workers": 0, "run_eval": true, "test_delay_epochs": 2, "epochs": 10, "save_step": 100, "eval_step": 100, "log_step": 10, "save_n_checkpoints": 3, "lr": 1e-05, "weight_decay": 1e-06, "optimizer": "AdamW", "output_path": "./egyptian_xtts_minimal/", "mixed_precision": false, "use_phonemes": false, "compute_input_seq_cache": true, "precompute_num_workers": 0}