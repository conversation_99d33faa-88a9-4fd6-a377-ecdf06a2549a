# XTTS Setup for Windows

This project helps you set up and run XTTS (Extended Text-to-Speech) on Windows with support for multiple languages including Arabic.

## System Requirements

### Hardware Requirements
| Component | Minimum | Recommended |
|-----------|---------|-------------|
| CPU | 4-core (Intel/AMD) | 8-core+ |
| RAM | 8GB | 16GB+ |
| GPU | None (CPU-only, but slow) | NVIDIA GPU (4GB+ VRAM) (GTX 1650 / RTX 2060+) |
| Storage | 5GB free (for models) | SSD preferred |

### Software Requirements
- Windows 10 or 11
- Python 3.9 or 3.10
- CUDA 11.7+ (if using NVIDIA GPU)

## Performance Expectations
| Setup | Speed (Inference Time) | Quality |
|-------|------------------------|--------|
| CPU-only | ~30–60 sec per sentence | Lower |
| Entry-level GPU (GTX 1650) | ~5–10 sec per sentence | Good |
| High-end GPU (RTX 3090) | ~1–3 sec per sentence | Best |

## Installation Instructions

1. Make sure you have Python 3.9 or 3.10 installed
2. Open PowerShell as Administrator in this directory
3. Run the setup script:
   ```
   Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
   .\setup_xtts.ps1
   ```
4. Activate the virtual environment:
   ```
   .\venv\Scripts\Activate.ps1
   ```
5. Run the test script:
   ```
   python test_xtts.py
   ```

## Using Your Own Voice

To use your own voice for TTS:

1. Record a clear audio sample of your voice (WAV format, 16kHz, mono)
2. Place the file in the `samples` directory
3. Use it in your code:
   ```python
   tts.tts_to_file(
       text="Your text here",
       speaker_wav="samples/your_voice_sample.wav",
       language="en",  # or "ar" for Arabic
       file_path="output/your_output.wav"
   )
   ```

## Troubleshooting

- **Out of Memory Error**: Reduce batch size or use a smaller model
- **Slow CPU Performance**: Try quantized models (e.g., XTTS-v1.1)
- **Arabic Pronunciation Issues**: Use clean, accent-matched voice samples
- **CUDA Not Found**: Make sure you have the correct CUDA version installed

## Additional Resources

- [Coqui TTS Documentation](https://tts.readthedocs.io/)
- [XTTS Model Card](https://huggingface.co/coqui/XTTS-v2)
