#!/usr/bin/env python3
"""
Simple Egyptian Fine-tuning
Using TTS API for easier training
"""

import os
from TTS.api import TTS

def simple_finetune():
    """Simple fine-tuning approach"""
    print("🇪🇬 Simple Egyptian Fine-tuning")
    print("=" * 35)

    # Check if we have data
    if not os.path.exists("egyptian_dataset/wavs/speaker1_001.wav"):
        print("❌ No training data found")
        print("Run setup first!")
        return

    try:
        print("🔧 Initializing TTS model...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)

        print("📊 Preparing training data...")
        # Get list of audio files and texts
        audio_files = []
        texts = []

        # Read metadata
        import pandas as pd
        df = pd.read_csv("egyptian_dataset/metadata.csv", sep='|')

        for _, row in df.iterrows():
            audio_path = f"egyptian_dataset/wavs/{row['audio_file']}"
            if os.path.exists(audio_path):
                audio_files.append(audio_path)
                texts.append(row['text'])

        print(f"📁 Found {len(audio_files)} training samples")

        if len(audio_files) == 0:
            print("❌ No valid audio files found")
            return

        # Simple approach: Use the data to improve voice cloning
        print("🎯 Training approach: Enhanced voice cloning")
        print("📝 This will create a speaker embedding for better Egyptian accent")

        # Test generation with each sample to see quality
        for i, (audio_file, text) in enumerate(zip(audio_files[:3], texts[:3])):
            print(f"\n🧪 Testing sample {i+1}: {os.path.basename(audio_file)}")

            output_file = f"test_output_{i+1}.wav"

            try:
                tts.tts_to_file(
                    text=text,
                    speaker_wav=audio_file,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")
            except Exception as e:
                print(f"❌ Error: {str(e)}")

        print("\n🎉 Simple training/testing complete!")
        print("📁 Check the generated test_output_*.wav files")
        print("🎯 Use the best audio sample in your GUI for improved results")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    simple_finetune()
