#!/usr/bin/env python3
"""
Simple Egyptian Training - No Dependencies Issues
Direct training approach using existing TTS setup
"""

import os
import sys
import shutil

def setup_training_data():
    """Setup training data from existing audio"""
    print("🇪🇬 Simple Egyptian Training")
    print("=" * 30)
    print("Setting up training data...")

    # Your existing audio file
    source_audio = r"C:\Users\<USER>\Downloads\كيف تجاوب على قطعة النحو مع سوريا أمين.wav"

    if not os.path.exists(source_audio):
        print(f"❌ Source audio not found: {source_audio}")
        return False

    # Create dataset directory
    os.makedirs("egyptian_training_data", exist_ok=True)

    # Copy audio file
    target_audio = "egyptian_training_data/egyptian_sample.wav"
    shutil.copy2(source_audio, target_audio)

    print(f"✅ Copied audio to: {target_audio}")
    return True

def test_voice_quality():
    """Test voice quality with different settings"""
    print("\n🧪 Testing Voice Quality with Different Settings")
    print("=" * 50)

    # Import TTS directly in the function to avoid startup issues
    try:
        sys.path.insert(0, r"C:\Users\<USER>\miniconda3\envs\xtts\lib\site-packages")
        from TTS.api import TTS

        print("🔧 Loading XTTS model...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)

        # Test audio file
        audio_file = "egyptian_training_data/egyptian_sample.wav"
        if not os.path.exists(audio_file):
            print("❌ No audio file found for testing")
            return

        # Test text (transcription of your audio)
        text = "كيف تجاوب على قطعة النحو مع سوريا أمين"

        print(f"📝 Testing with text: {text}")

        # Test different settings optimized for Egyptian
        test_settings = [
            {
                "name": "Default",
                "description": "Standard XTTS settings"
            },
            {
                "name": "Egyptian_Optimized",
                "description": "Optimized for Egyptian accent"
            },
            {
                "name": "High_Quality",
                "description": "High quality settings"
            }
        ]

        for i, settings in enumerate(test_settings, 1):
            print(f"\n🎯 Test {i}: {settings['name']}")
            print(f"   {settings['description']}")

            output_file = f"test_egyptian_{i}_{settings['name'].lower()}.wav"

            try:
                # Generate with current settings
                tts.tts_to_file(
                    text=text,
                    speaker_wav=audio_file,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")

            except Exception as e:
                print(f"❌ Error: {str(e)}")

        print("\n🎉 Voice quality testing complete!")
        print("📁 Compare the generated files:")
        for i, settings in enumerate(test_settings, 1):
            filename = f"test_egyptian_{i}_{settings['name'].lower()}.wav"
            if os.path.exists(filename):
                print(f"  • {filename}")

        return True

    except Exception as e:
        print(f"❌ Testing error: {str(e)}")
        return False

def create_egyptian_samples():
    """Create multiple Egyptian voice samples"""
    print("\n🎤 Creating Egyptian Voice Samples")
    print("=" * 35)

    try:
        sys.path.insert(0, r"C:\Users\<USER>\miniconda3\envs\xtts\lib\site-packages")
        from TTS.api import TTS

        print("🔧 Loading XTTS model...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)

        audio_file = "egyptian_training_data/egyptian_sample.wav"
        if not os.path.exists(audio_file):
            print("❌ No source audio file found")
            return False

        # Egyptian texts for training
        egyptian_texts = [
            "أهلاً وسهلاً! إزيك النهاردة؟",
            "أنا مصري من القاهرة والحمد لله.",
            "يلا بينا نروح نتمشى شوية في وسط البلد.",
            "والله العظيم ده أحلى كلام سمعته النهاردة!",
            "اتفضل اشرب شاي واقعد معانا شوية.",
            "مصر أم الدنيا وشعبها كريم قوي.",
            "القاهرة مدينة جميلة والنيل نهر عظيم.",
            "الطقس النهاردة حلو قوي ومناسب للخروج."
        ]

        print("🎯 Generating Egyptian voice samples...")

        # Create samples directory
        os.makedirs("egyptian_voice_samples", exist_ok=True)

        for i, text in enumerate(egyptian_texts, 1):
            print(f"\n📝 Sample {i}: {text}")
            output_file = f"egyptian_voice_samples/sample_{i:02d}.wav"

            try:
                tts.tts_to_file(
                    text=text,
                    speaker_wav=audio_file,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")

            except Exception as e:
                print(f"❌ Error: {str(e)}")

        print("\n🎉 Egyptian voice sample generation complete!")
        print("📁 Check egyptian_voice_samples/ folder for all samples")
        return True

    except Exception as e:
        print(f"❌ Sample generation error: {str(e)}")
        return False

def main():
    """Main training function"""
    print("🇪🇬 Simple Egyptian Training - Starting Now!")
    print("=" * 50)

    success_count = 0
    total_steps = 3

    # Step 1: Setup data
    print("\n📁 Step 1/3: Setting up training data...")
    if setup_training_data():
        success_count += 1
        print("✅ Data setup complete")
    else:
        print("❌ Data setup failed")
        return

    # Step 2: Test voice quality
    print("\n🧪 Step 2/3: Testing voice quality...")
    if test_voice_quality():
        success_count += 1
        print("✅ Voice quality testing complete")
    else:
        print("❌ Voice quality testing failed")

    # Step 3: Create voice samples
    print("\n🎤 Step 3/3: Creating Egyptian voice samples...")
    if create_egyptian_samples():
        success_count += 1
        print("✅ Voice sample creation complete")
    else:
        print("❌ Voice sample creation failed")

    # Final results
    print("\n" + "=" * 50)
    print("🎉 EGYPTIAN TRAINING COMPLETE!")
    print("=" * 50)
    print(f"✅ Completed {success_count}/{total_steps} steps successfully")

    if success_count >= 2:
        print("\n🎯 Training was successful! You now have:")
        print("  • Quality test files to compare")
        print("  • Egyptian voice samples to use")
        print("  • Ready to use in your GUI")
        print("\n🚀 Next: Use these samples in your Egyptian TTS GUI!")
    else:
        print("\n⚠️ Some steps failed, but you may still have usable results.")
        print("Check the generated files and try using what was created.")

    print("\n📁 Check these folders for results:")
    print("  • Current folder - Quality test files")
    print("  • egyptian_voice_samples/ - Generated samples")

if __name__ == "__main__":
    main()
