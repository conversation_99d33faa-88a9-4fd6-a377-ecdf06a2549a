﻿**********************
Windows PowerShell transcript start
Start time: 20250525080601
Username: THEDAYIMETP\omara
RunAs User: THEDAYIMETP\omara
Configuration Name: 
Machine: THEDAYIMETP (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 36488
PSVersion: 5.1.26100.4061
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.26100.4061
BuildVersion: 10.0.26100.4061
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is xtts_update_log.txt
=== XTTS Update Script ===
This script will update your XTTS installation to the latest version.
Found virtual environment: xtts_venv_310
\nChecking Python version in virtual environment...
Python version in virtual environment: 3.10.11
\nUpdating pip and setuptools...
  Using cached setuptools-80.8.0-py3-none-any.whl (1.2 MB)
ERROR: To modify pip, please run the following command:
C:\Users\<USER>\Pictures\New folder\xtts-project\xtts_venv_310\Scripts\python.exe -m pip install --upgrade pip setuptools

[notice] A new release of pip is available: 23.0.1 -> 25.1.1
[notice] To update, run: C:\Users\<USER>\Pictures\New folder\xtts-project\xtts_venv_310\Scripts\python.exe -m pip install --upgrade pip
\nChecking for CUDA support...
CUDA is not available. Installing PyTorch CPU version...

PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
]633;D;1]633;A]633;P;Cwd=C:\x5cUsers\x5comara\x5cPictures\x5cNew folder\x5cxtts-projectPS C:\Users\<USER>\Pictures\New folder\xtts-project> ]633;B
PS>.\update_xtts.ps1
Transcript started, output file is xtts_update_log.txt
=== XTTS Update Script ===
This script will update your XTTS installation to the latest version.
Found virtual environment: xtts_venv_310
\nChecking Python version in virtual environment...
Python version in virtual environment: 3.10.11
\nUpdating pip and setuptools...

\nChecking for CUDA support...
CUDA is not available. Installing PyTorch CPU version...
-any.whl (1.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 2.9 MB/s eta 0:00:00
Downloading https://download.pytorch.org/whl/numpy-2.1.2-cp310-cp310-win_amd64.whl (12.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 12.9/12.9 MB 2.9 MB/s eta 0:00:00
Downloading https://download.pytorch.org/whl/sympy-1.13.3-py3-none-any.whl (6.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.2/6.2 MB 3.0 MB/s eta 0:00:00
Downloading https://download.pytorch.org/whl/typing_extensions-4.12.2-py3-none-any.whl (37 kB)
Installing collected packages: mpmath, urllib3, typing-extensions, sympy, pillow, numpy, networkx, MarkupSafe, idn
a, filelock, charset-normalizer, certifi, requests, jinja2, torch, torchvision, torchaudio
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╸━━━━━━━ 14   ━━━━━━━━━━━━━━━━━   ━━━━━━━━━━━━━╺━━ 1   ━━━━━━━━━━━━━━━━━━━━━━━━
Successfully installed MarkupSafe-2.1.5 certifi-2022.12.7 charset-normalizer-2.1.1 filelock-3.13.1 idna-3.4 jinja2
-3.1.4 mpmath-1.3.0 networkx-3.3 numpy-2.1.2 pillow-11.0.0 requests-2.28.1 sympy-1.13.3 torch-2.0.1+cpu torchaudio
-2.0.2+cpu torchvision-0.15.2+cpu typing-extensions-4.12.2 urllib3-1.26.13
\nInstalling/updating dependencies...
Downloading scipy-1.15.3-cp310-cp310-win_amd64.whl (41.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 41.3/41.3 MB 2.7 MB/s eta 0:00:00
Downloading soxr-0.5.0.post1-cp310-cp310-win_amd64.whl (166 kB)
Using cached threadpoolctl-3.6.0-py3-none-any.whl (18 kB)
Using cached pycparser-2.22-py3-none-any.whl (117 kB)
Installing collected packages: threadpoolctl, pygame, pycparser, platformdirs, packaging, numpy, msgpack, llvmlite
, joblib, decorator, audioread, soxr, scipy, pooch, numba, lazy-loader, cffi, soundfile, scikit-learn, librosa
  Attempting uninstall: numpy
    Found existing installation: numpy 2.1.2
    Uninstalling numpy-2.1.2:
      Successfully uninstalled numpy-2.1.2
Successfully installed audioread-3.0.1 cffi-1.17.1 decorator-5.2.1 joblib-1.5.1 lazy-loader-0.4 librosa-0.10.1 llv
mlite-0.44.0 msgpack-1.1.0 numba-0.61.2 numpy-1.24.3 packaging-25.0 platformdirs-4.3.8 pooch-1.8.2 pycparser-2.22
pygame-2.5.2 scikit-learn-1.6.1 scipy-1.15.3 soundfile-0.12.1 soxr-0.5.0.post1 threadpoolctl-3.6.0
\nInstalling latest coqui-tts package...
e2507d7daf5188
  Building wheel for gruut_lang_fr (pyproject.toml) ... done
  Created wheel for gruut_lang_fr: filename=gruut_lang_fr-2.0.2-py3-none-any.whl size=10968818 sha256=7219cd97a0e2
5b2fb5c6b7aa2da547f5157c67471fd30c2affa359f6e122bbf8
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\db\21\be\d0436e3f1cf9bf38b9bb9b4a476399c77a1a
b19f7172b45e19
Successfully built encodec gruut gruut-ipa gruut_lang_en docopt gruut_lang_de gruut_lang_es gruut_lang_fr
Installing collected packages: pytz, gruut_lang_fr, gruut_lang_es, gruut_lang_en, gruut_lang_de, docopt, werkzeug,
 tzdata, typeguard, tensorboard-data-server, six, safetensors, regex, pyyaml, python-crfsuite, pysbd, pyparsing, p
sutil, protobuf, propcache, numpy, num2words, multidict, more_itertools, markdown, kiwisolver, gruut-ipa, grpcio,
fsspec, frozenlist, fonttools, einops, cython, cycler, coqpit-config, colorama, Babel, attrs, async-timeout, anyas
cii, aiohappyeyeballs, absl-py, yarl, tzlocal, tqdm, torch, tensorboard, python-dateutil, monotonic-alignment-sear
ch, jsonlines, inflect, contourpy, aiosignal, torchaudio, matplotlib, huggingface-hub, dateparser, coqui-tts-train
er, aiohttp, tokenizers, librosa, gruut, encodec, transformers, coqui-tts
  Attempting uninstall: numpy
    Found existing installation: numpy 1.24.3
    Uninstalling numpy-1.24.3:
      Successfully uninstalled numpy-1.24.3
  Attempting uninstall: torch
    Found existing installation: torch 2.0.1+cpu
    Uninstalling torch-2.0.1+cpu:
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━╸━━━━━━━━━━━━ 45/65 [torch]ERROR: Could not install packages due to an OSError: [Win
Error 32] The process cannot access the file because it is being used by another process: 'c:\\users\\<USER>\\pictu
res\\new folder\\xtts-project\\xtts_venv_310\\lib\\site-packages\\torch\\include\\ATen\\ops\\_upsample_nearest_exa
ct2d_backward_meta_dispatch.h'
Check the permissions.
\nEnsuring project directories exist...


    Directory: C:\Users\<USER>\Pictures\New folder\xtts-project


Mode                 LastWriteTime         Length Name
----                 -------------         ------ ----
d-----         5/24/2025   5:09 PM                models
d-----         5/24/2025   5:09 PM                samples
d-----         5/24/2025   5:09 PM                output
\nUpdating test script for coqui-tts...
Updated test script created as test_xtts_updated.py
\nCreating updated launcher batch file...
Updated launcher batch file created as run_xtts_updated.bat
\n=== Update complete! ===

XTTS has been updated successfully! To test the updated installation:

1. Run the test script to verify the installation:
   .\xtts_venv_310\Scripts\activate.ps1
   python test_xtts_updated.py

2. To use the GUI application with the updated packages:
   Double-click run_xtts_updated.bat

Note: The first run will download the XTTS model (about 5GB) if it's not already downloaded.
Transcript stopped, output file is C:\Users\<USER>\Pictures\New folder\xtts-project\xtts_update_log.txt


]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5comara\x5cPictures\x5cNew folder\x5cxtts-projectPS C:\Users\<USER>\Pictures\New folder\xtts-project> ]633;B
PS>cd 'c:\Users\<USER>\Pictures\New folder\xtts-project'
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5comara\x5cPictures\x5cNew folder\x5cxtts-projectPS C:\Users\<USER>\Pictures\New folder\xtts-project> ]633;B
PS>cd 'c:\Users\<USER>\Pictures\New folder\xtts-project'
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5comara\x5cPictures\x5cNew folder\x5cxtts-projectPS C:\Users\<USER>\Pictures\New folder\xtts-project> ]633;B
