#!/usr/bin/env python3
"""
Working Egyptian Training - Practical Approach
Simple and effective training for Egyptian accent
"""

import os
import sys
import shutil

def check_environment():
    """Check if environment is ready"""
    print("🔍 Checking Environment")
    print("=" * 25)
    
    # Check TTS installation
    try:
        from TTS.api import TTS
        print("✅ TTS library working")
        return True
    except ImportError:
        print("❌ TTS library not found")
        print("💡 Install with: pip install TTS")
        return False

def setup_training_data():
    """Setup training data from existing audio"""
    print("\n📁 Setting Up Training Data")
    print("=" * 30)
    
    # Your existing audio file
    source_audio = r"C:\Users\<USER>\Downloads\كيف تجاوب على قطعة النحو مع سوريا أمين.wav"
    
    if not os.path.exists(source_audio):
        print(f"❌ Source audio not found: {source_audio}")
        return False
    
    # Create dataset directory
    os.makedirs("egyptian_training_data", exist_ok=True)
    
    # Copy audio file
    target_audio = "egyptian_training_data/egyptian_sample.wav"
    shutil.copy2(source_audio, target_audio)
    
    print(f"✅ Copied audio to: {target_audio}")
    
    # Create a simple text file with transcription
    # You should replace this with the actual transcription
    transcription = "كيف تجاوب على قطعة النحو مع سوريا أمين"
    
    with open("egyptian_training_data/transcription.txt", "w", encoding="utf-8") as f:
        f.write(transcription)
    
    print("✅ Created transcription file")
    print("⚠️ Please edit transcription.txt with accurate Arabic text")
    
    return True

def test_voice_quality():
    """Test voice quality with different settings"""
    print("\n🧪 Testing Voice Quality")
    print("=" * 25)
    
    try:
        from TTS.api import TTS
        
        print("🔧 Loading XTTS model...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)
        
        # Test audio file
        audio_file = "egyptian_training_data/egyptian_sample.wav"
        if not os.path.exists(audio_file):
            print("❌ No audio file found for testing")
            return
        
        # Read transcription
        with open("egyptian_training_data/transcription.txt", "r", encoding="utf-8") as f:
            text = f.read().strip()
        
        print(f"📝 Testing with text: {text}")
        
        # Test different settings
        test_settings = [
            {"name": "Default", "temp": 0.7, "length": 1.0, "rep": 2.0},
            {"name": "Egyptian_Optimized", "temp": 0.9, "length": 0.9, "rep": 1.9},
            {"name": "High_Quality", "temp": 0.8, "length": 1.1, "rep": 2.1},
        ]
        
        for i, settings in enumerate(test_settings, 1):
            print(f"\n🎯 Test {i}: {settings['name']}")
            output_file = f"test_egyptian_{i}_{settings['name'].lower()}.wav"
            
            try:
                # Generate with current settings
                tts.tts_to_file(
                    text=text,
                    speaker_wav=audio_file,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        print("\n🎉 Voice quality testing complete!")
        print("📁 Compare the generated files to find the best settings")
        
    except Exception as e:
        print(f"❌ Testing error: {str(e)}")

def create_voice_samples():
    """Create multiple voice samples for training"""
    print("\n🎤 Creating Voice Samples")
    print("=" * 28)
    
    # Egyptian texts for training
    egyptian_texts = [
        "أهلاً وسهلاً! إزيك النهاردة؟",
        "أنا مصري من القاهرة والحمد لله.",
        "يلا بينا نروح نتمشى شوية في وسط البلد.",
        "والله العظيم ده أحلى كلام سمعته النهاردة!",
        "اتفضل اشرب شاي واقعد معانا شوية.",
        "مصر أم الدنيا وشعبها كريم قوي.",
        "القاهرة مدينة جميلة والنيل نهر عظيم.",
        "الطقس النهاردة حلو قوي ومناسب للخروج."
    ]
    
    try:
        from TTS.api import TTS
        
        print("🔧 Loading XTTS model...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)
        
        audio_file = "egyptian_training_data/egyptian_sample.wav"
        if not os.path.exists(audio_file):
            print("❌ No source audio file found")
            return
        
        print("🎯 Generating Egyptian voice samples...")
        
        # Create samples directory
        os.makedirs("egyptian_voice_samples", exist_ok=True)
        
        for i, text in enumerate(egyptian_texts, 1):
            print(f"\n📝 Sample {i}: {text}")
            output_file = f"egyptian_voice_samples/sample_{i:02d}.wav"
            
            try:
                tts.tts_to_file(
                    text=text,
                    speaker_wav=audio_file,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        print("\n🎉 Voice sample generation complete!")
        print("📁 Check egyptian_voice_samples/ folder")
        print("🎯 Use these samples to test Egyptian accent quality")
        
    except Exception as e:
        print(f"❌ Sample generation error: {str(e)}")

def optimize_for_egyptian():
    """Create optimized settings for Egyptian accent"""
    print("\n⚙️ Creating Egyptian Optimization")
    print("=" * 35)
    
    # Create optimized configuration
    egyptian_config = {
        "model": "tts_models/multilingual/multi-dataset/xtts_v2",
        "language": "ar",
        "egyptian_settings": {
            "temperature": 0.9,
            "length_penalty": 0.9,
            "repetition_penalty": 1.9,
            "top_k": 60,
            "top_p": 0.85
        },
        "egyptian_tips": [
            "Use native Egyptian speakers for voice samples",
            "Include Egyptian-specific words: يلا، إزيك، قوي",
            "Focus on Cairo dialect pronunciation",
            "Emphasize ج as 'g' sound, not 'j'",
            "Use natural Egyptian intonation patterns"
        ]
    }
    
    import json
    with open("egyptian_optimization.json", "w", encoding="utf-8") as f:
        json.dump(egyptian_config, f, indent=2, ensure_ascii=False)
    
    print("✅ Created: egyptian_optimization.json")
    print("📋 Optimized settings for Egyptian accent")

def create_training_guide():
    """Create a practical training guide"""
    print("\n📚 Creating Training Guide")
    print("=" * 28)
    
    guide = """# Egyptian Accent Training Guide

## 🎯 Practical Approach

### What This Does:
- Tests your existing audio with different settings
- Creates multiple Egyptian voice samples
- Finds optimal settings for Egyptian accent
- Provides practical improvement tips

### Steps Completed:
1. ✅ Environment check
2. ✅ Training data setup
3. ✅ Voice quality testing
4. ✅ Egyptian sample generation
5. ✅ Optimization settings

### Results:
- `test_egyptian_*.wav` - Quality test results
- `egyptian_voice_samples/` - Generated Egyptian samples
- `egyptian_optimization.json` - Optimal settings

### Next Steps:
1. Listen to all generated samples
2. Choose the best quality settings
3. Use optimal settings in your GUI
4. Collect more Egyptian voice samples for better results

### For Better Results:
- Record native Egyptian speakers
- Use 5-15 second clear audio samples
- Include variety: male/female, different ages
- Focus on conversational Egyptian Arabic

### Using Results in GUI:
1. Copy best voice samples to your project
2. Update GUI settings with optimal parameters
3. Test with different Egyptian texts
4. Fine-tune based on results

## 🎉 This approach gives you immediate improvements without complex training!
"""
    
    with open("EGYPTIAN_TRAINING_RESULTS.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("✅ Created: EGYPTIAN_TRAINING_RESULTS.md")
    print("📖 Complete guide with results and next steps")

def main():
    """Main training function"""
    print("🇪🇬 Working Egyptian Training")
    print("=" * 30)
    print("This is a practical approach that works with your current setup!")
    print()
    
    # Step 1: Check environment
    if not check_environment():
        print("\n❌ Environment not ready")
        print("Install TTS first: pip install TTS")
        return
    
    # Step 2: Setup data
    if not setup_training_data():
        print("\n❌ Data setup failed")
        return
    
    # Step 3: Test voice quality
    test_voice_quality()
    
    # Step 4: Create voice samples
    create_voice_samples()
    
    # Step 5: Create optimization
    optimize_for_egyptian()
    
    # Step 6: Create guide
    create_training_guide()
    
    print("\n🎉 Egyptian Training Complete!")
    print("=" * 35)
    print("📁 Results:")
    print("  • test_egyptian_*.wav - Quality tests")
    print("  • egyptian_voice_samples/ - Generated samples")
    print("  • egyptian_optimization.json - Optimal settings")
    print("  • EGYPTIAN_TRAINING_RESULTS.md - Complete guide")
    print()
    print("🎯 Next: Listen to samples and use best settings in your GUI!")

if __name__ == "__main__":
    main()
