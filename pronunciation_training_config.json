{"model_name": "xtts_egyptian_pronunciation", "run_name": "egyptian_pronunciation_training", "run_description": "Training XTTS for correct Egyptian Arabic pronunciation", "datasets": [{"name": "egyptian_pronunciation", "path": "./pronunciation_training/", "meta_file_train": "pronunciation_metadata.csv", "language": "ar", "phoneme_language": "ar"}], "model_args": {"gpt_batch_size": 1, "enable_redaction": false, "kv_cache": true, "gpt_max_audio_len": 229376, "gpt_max_text_len": 200, "gpt_max_new_tokens": 1024, "gpt_min_audio_len": 22050, "gpt_num_audio_tokens": 1024, "gpt_start_audio_token": 1024, "gpt_stop_audio_token": 1025, "gpt_use_masking_gt_prompt_approach": true, "gpt_use_perceiver_resampler": true}, "audio": {"sample_rate": 22050, "output_sample_rate": 24000}, "batch_size": 1, "eval_batch_size": 1, "num_loader_workers": 0, "num_eval_loader_workers": 0, "run_eval": true, "test_delay_epochs": 3, "epochs": 50, "save_step": 500, "eval_step": 500, "log_step": 50, "save_n_checkpoints": 10, "lr": 1e-05, "weight_decay": 1e-06, "optimizer": "AdamW", "scheduler": "MultiStepLR", "lr_scheduler_params": {"milestones": [25000, 75000, 150000], "gamma": 0.5, "last_epoch": -1}, "output_path": "./egyptian_pronunciation_model/", "pronunciation_focus": {"egyptian_sounds": {"ج": "g_sound", "ق": "glottal_stop", "ث": "s_or_t_sound", "ذ": "z_or_d_sound"}, "training_emphasis": ["Character-level pronunciation accuracy", "Egyptian accent patterns", "Natural intonation", "Stress patterns", "Vowel modifications"]}, "use_phonemes": true, "phonemizer": "espeak", "phoneme_language": "ar", "compute_input_seq_cache": true, "precompute_num_workers": 0}