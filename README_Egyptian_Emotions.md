# XTTS GUI with Egyptian Accent & Emotion Controls

## 🎭 Enhanced Features

This enhanced version of the XTTS GUI includes specialized support for **Egyptian Arabic accent** and **emotion control** for more expressive speech generation.

## 🌟 New Features

### 1. **Egyptian Arabic Accent Support**
- **Language Selection**: Choose from 17 supported languages including Arabic
- **Accent Variants**: 
  - **Egyptian** (Default) - Cairo dialect
  - **Levantine** - Syrian/Lebanese dialect  
  - **Gulf** - UAE/Kuwait dialect
  - **Maghrebi** - Moroccan/Tunisian dialect
  - **Standard** - Modern Standard Arabic

### 2. **Emotion Control System**
- **7 Emotion Presets**:
  - **Neutral** - Balanced, natural speech
  - **Happy** - Cheerful, upbeat tone
  - **Sad** - Melancholic, slower pace
  - **Angry** - Intense, forceful delivery
  - **Excited** - Energetic, fast-paced
  - **Calm** - Peaceful, relaxed tone
  - **Dramatic** - Theatrical, expressive

### 3. **Advanced Voice Controls**
- **Temperature** (0.1-1.5): Controls creativity/randomness
  - Lower = More consistent, predictable
  - Higher = More creative, varied
- **Length Penalty** (0.5-2.0): Controls speech length
  - Lower = Shorter, more concise
  - Higher = Longer, more detailed
- **Repetition Penalty** (1.0-3.0): Prevents repetition
  - Higher = Less repetitive speech
- **Speech Speed** (0.5-2.0): Controls playback speed
  - 0.5 = Half speed (slower)
  - 2.0 = Double speed (faster)

### 4. **Sample Text Library**
- Pre-loaded sample texts for each Arabic accent
- **Egyptian**: "أهلاً وسهلاً! إزيك النهاردة؟ أنا بتكلم بالمصري الأصيل..."
- **Levantine**: "مرحبا! كيفك اليوم؟ أنا بحكي باللهجة الشامية..."
- **Gulf**: "السلام عليكم! شلونك اليوم؟ أنا أتكلم باللهجة الخليجية..."
- **Standard**: "السلام عليكم ورحمة الله وبركاته. كيف حالكم اليوم؟..."

### 5. **Enhanced Audio Support**
- Support for multiple audio formats (WAV, MP3, FLAC, OGG)
- Built-in audio player with play/stop controls
- Save generated audio to custom locations
- Automatic file management with timestamps

## 🚀 How to Use

### Step 1: Launch the Application
```bash
python xtts_gui_egyptian_emotions.py
```

### Step 2: Load the Model
1. Click **"Load Selected Model"**
2. Accept the terms of service
3. Wait for model download/loading (first time may take several minutes)

### Step 3: Select Voice Sample
1. Click **"Browse..."** to select a voice sample file
2. Choose a clear audio file (3-10 seconds recommended)
3. For Egyptian accent, use an Egyptian speaker's voice

### Step 4: Configure Language & Accent
1. Select **"ar"** (Arabic) from the Language dropdown
2. Choose **"Egyptian"** from the Arabic Accent dropdown
3. Click **"Load Sample Text"** to get Egyptian sample text

### Step 5: Set Emotion
1. Choose an emotion preset from the dropdown:
   - **Happy** for cheerful speech
   - **Dramatic** for expressive storytelling
   - **Calm** for peaceful narration
2. Fine-tune with advanced controls if needed

### Step 6: Enter Text
1. Type or paste Arabic text in the text area
2. Use Egyptian dialect for best results with Egyptian accent
3. Keep text under 2000 characters for optimal performance

### Step 7: Generate Speech
1. Click **"🎤 Generate Speech"**
2. Wait for processing (30 seconds to 2 minutes depending on text length)
3. Audio will be saved automatically with timestamp

### Step 8: Play & Save
1. Click **"▶️ Play Audio"** to listen
2. Click **"💾 Save Audio"** to save to custom location
3. Use **"⏹️ Stop Audio"** to stop playback

## 🎯 Emotion Preset Details

| Emotion | Temperature | Length Penalty | Repetition Penalty | Best For |
|---------|-------------|----------------|-------------------|----------|
| **Neutral** | 0.7 | 1.0 | 2.0 | General speech, news |
| **Happy** | 0.9 | 0.8 | 1.8 | Celebrations, greetings |
| **Sad** | 0.5 | 1.2 | 2.2 | Condolences, serious topics |
| **Angry** | 1.0 | 0.7 | 1.5 | Protests, complaints |
| **Excited** | 1.1 | 0.6 | 1.6 | Sports, announcements |
| **Calm** | 0.4 | 1.3 | 2.5 | Meditation, instructions |
| **Dramatic** | 1.2 | 0.5 | 1.4 | Storytelling, theater |

## 🔧 Technical Features

### Advanced Generation Options
- **Split Sentences**: Automatically splits long text for better quality
- **Text Splitting**: Enables processing of very long texts
- **Custom Parameters**: Direct access to XTTS model parameters

### Audio Processing
- **24kHz Output**: High-quality audio generation
- **Automatic Format Conversion**: Handles various input formats
- **Real-time Progress**: Live updates during generation

### Error Handling
- **Automatic FFmpeg Installation**: Downloads and configures FFmpeg if missing
- **Fallback Methods**: Multiple generation approaches for reliability
- **Comprehensive Logging**: Detailed logs for troubleshooting

## 📁 File Structure
```
xtts-project/
├── xtts_gui_egyptian_emotions.py  # Enhanced GUI with emotions
├── xtts_gui_fixed.py             # Original working GUI
├── output/                       # Generated audio files
├── xtts_gui.log                 # Application logs
└── README_Egyptian_Emotions.md   # This file
```

## 🎨 Egyptian Dialect Examples

### Greetings
- **Egyptian**: "أهلاً وسهلاً! إزيك؟ عامل إيه؟"
- **Translation**: "Welcome! How are you? How are you doing?"

### Common Phrases
- **Egyptian**: "القاهرة مدينة جميلة قوي والناس فيها طيبين"
- **Translation**: "Cairo is a very beautiful city and the people in it are kind"

### Emotional Expressions
- **Happy**: "فرحان قوي النهاردة! الجو حلو أوي!"
- **Sad**: "زعلان شوية... مش عارف أعمل إيه"
- **Excited**: "مش مصدق! ده حلم بقى حقيقة!"

## 🔍 Tips for Best Results

### Voice Sample Selection
- Use clear, noise-free audio (3-10 seconds)
- Egyptian speaker for Egyptian accent
- Single speaker only
- Good microphone quality

### Text Input
- Use authentic Egyptian dialect
- Avoid mixing dialects in same text
- Include proper Arabic diacritics when possible
- Keep sentences natural and conversational

### Emotion Tuning
- Start with presets, then fine-tune
- Higher temperature = more expressive
- Lower repetition penalty = more natural flow
- Adjust speed for different content types

## 🚨 Troubleshooting

### Common Issues
1. **Model Loading Fails**: Check internet connection, restart application
2. **Audio Not Playing**: Ensure pygame is installed, check audio drivers
3. **Generation Errors**: Try shorter text, check voice sample quality
4. **FFmpeg Issues**: Allow automatic installation or install manually

### Performance Tips
- Use GPU if available (automatic detection)
- Close other applications during generation
- Use shorter texts for faster processing
- Clear output folder periodically

## 📞 Support

For issues or questions:
1. Check the log file (`xtts_gui.log`)
2. Ensure all dependencies are installed
3. Try with different voice samples
4. Restart the application

---

**Enjoy creating expressive Egyptian Arabic speech with emotion! 🎭🇪🇬**
