﻿**********************
Windows PowerShell transcript start
Start time: 20250525082600
Username: THEDAYIMETP\omara
RunAs User: THEDAYIMETP\omara
Configuration Name: 
Machine: THEDAYIMETP (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 29352
PSVersion: 5.1.26100.4061
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.26100.4061
BuildVersion: 10.0.26100.4061
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is xtts_finish_update_log.txt
=== Finishing XTTS Update ===
Installing latest coqui-tts package...
Using cached torch-2.7.0-cp310-cp310-win_amd64.whl (212.5 MB)
Using cached torchaudio-2.7.0-cp310-cp310-win_amd64.whl (2.5 MB)
WARNING: Ignoring invalid distribution -orch (c:\users\<USER>\pictures\new folder\xtts-project\xtts_venv_310\lib\site-packages)
Installing collected packages: python-dateutil, monotonic-alignment-search, jsonlines, contourpy, aiosignal, torch, tensorboard, matplotlib, inflect
, huggingface-hub, dateparser, torchaudio, tokenizers, librosa, gruut, coqui-tts-trainer, aiohttp, transformers, encodec, coqui-tts
  Attempting uninstall: torchaudio
    Found existing installation: torchaudio 2.0.2+cpu
    Uninstalling torchaudio-2.0.2+cpu:
      Successfully uninstalled torchaudio-2.0.2+cpu
  Attempting uninstall: librosa
    Found existing installation: librosa 0.10.1
    Uninstalling librosa-0.10.1:
      Successfully uninstalled librosa-0.10.1
WARNING: Ignoring invalid distribution -orch (c:\users\<USER>\pictures\new folder\xtts-project\xtts_venv_310\lib\site-packages)
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the follo
wing dependency conflicts.
torchvision 0.15.2+cpu requires torch==2.0.1, but you have torch 2.7.0 which is incompatible.
Successfully installed aiohttp-3.12.0 aiosignal-1.3.2 contourpy-1.3.2 coqui-tts-0.26.2 coqui-tts-trainer-0.2.3 dateparser-1.1.8 encodec-0.1.1 gruut-
2.4.0 huggingface-hub-0.32.0 inflect-7.5.0 jsonlines-1.2.0 librosa-0.11.0 matplotlib-3.10.3 monotonic-alignment-search-0.2.0 python-dateutil-2.9.0.p
ost0 tensorboard-2.19.0 tokenizers-0.21.1 torch-2.7.0 torchaudio-2.7.0 transformers-4.51.3
\nUpdate complete!
\nTo test the updated installation:\n
1. Run the test script:\n   .\xtts_venv_310\Scripts\activate.ps1\n   python test_xtts_updated.py\n
2. To use the GUI application:\n   Double-click run_xtts_updated.bat\n
**********************
Windows PowerShell transcript end
End time: **************
**********************
**********************
Windows PowerShell transcript start
Start time: **************
Username: THEDAYIMETP\omara
RunAs User: THEDAYIMETP\omara
Configuration Name: 
Machine: THEDAYIMETP (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 29352
PSVersion: 5.1.26100.4061
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.26100.4061
BuildVersion: 10.0.26100.4061
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is xtts_finish_update_log.txt
=== Finishing XTTS Update ===
Installing latest coqui-tts package...

\nUpdate complete!
\nTo test the updated installation:\n
1. Run the test script:\n   .\xtts_venv_310\Scripts\activate.ps1\n   python test_xtts_updated.py\n
2. To use the GUI application:\n   Double-click run_xtts_updated.bat\n
**********************
Windows PowerShell transcript end
End time: 20250525082830
**********************
