#!/usr/bin/env python3
"""
Run Egyptian Pronunciation Training
Train the model to correctly pronounce Egyptian Arabic characters and accent
"""

import os
import sys
import json
import subprocess

def check_training_readiness():
    """Check if everything is ready for pronunciation training"""
    print("🔍 Checking Pronunciation Training Readiness")
    print("=" * 45)
    
    requirements = []
    
    # Check pronunciation samples
    if os.path.exists("pronunciation_samples"):
        sample_files = [f for f in os.listdir("pronunciation_samples") if f.endswith('.wav')]
        if sample_files:
            requirements.append(f"✅ Pronunciation samples: {len(sample_files)} files")
        else:
            requirements.append("❌ Pronunciation samples: No files found")
    else:
        requirements.append("❌ Pronunciation samples: Directory not found")
    
    # Check training configuration
    if os.path.exists("pronunciation_training_config.json"):
        requirements.append("✅ Training configuration: Found")
    else:
        requirements.append("❌ Training configuration: Not found")
    
    # Check metadata
    if os.path.exists("pronunciation_training/pronunciation_metadata.csv"):
        requirements.append("✅ Training metadata: Found")
    else:
        requirements.append("❌ Training metadata: Not found")
    
    # Check pronunciation data
    if os.path.exists("egyptian_pronunciation_data.json"):
        requirements.append("✅ Pronunciation data: Found")
    else:
        requirements.append("❌ Pronunciation data: Not found")
    
    # Display requirements
    for req in requirements:
        print(f"  {req}")
    
    # Check if ready
    ready = all("✅" in req for req in requirements)
    
    if ready:
        print("\n🎉 Ready for pronunciation training!")
        return True
    else:
        print("\n❌ Not ready for training. Run train_egyptian_pronunciation.py first.")
        return False

def run_simple_pronunciation_training():
    """Run simple pronunciation training approach"""
    print("\n🎓 Running Simple Pronunciation Training")
    print("=" * 40)
    
    try:
        from TTS.api import TTS
        
        print("🔧 Loading XTTS model...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)
        
        # Load pronunciation data
        with open("egyptian_pronunciation_data.json", "r", encoding="utf-8") as f:
            pronunciation_data = json.load(f)
        
        print("📚 Loaded Egyptian pronunciation patterns")
        
        # Test pronunciation with different examples
        print("\n🧪 Testing Egyptian Character Pronunciation")
        print("=" * 45)
        
        # Test ج sound (should be 'g' in Egyptian)
        print("\n🎯 Testing ج sound (Egyptian 'g' pronunciation):")
        j_examples = pronunciation_data["ج_sound"]["examples"]
        
        for i, example in enumerate(j_examples[:3], 1):
            text = example["text"]
            pronunciation = example["pronunciation"]
            meaning = example["meaning"]
            
            print(f"\n📝 Example {i}: {text}")
            print(f"   Pronunciation: {pronunciation}")
            print(f"   Meaning: {meaning}")
            
            output_file = f"pronunciation_test_j_{i}.wav"
            
            try:
                # Use best Egyptian audio
                speaker_wav = "egyptian_training_data/مراجعة النحو النحو للصف الثاني الاعدادي الترم الأول 2024.wav"
                
                tts.tts_to_file(
                    text=text,
                    speaker_wav=speaker_wav,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        # Test ق sound (should be glottal stop in Egyptian)
        print("\n🎯 Testing ق sound (Egyptian glottal stop):")
        q_examples = pronunciation_data["ق_sound"]["examples"]
        
        for i, example in enumerate(q_examples[:3], 1):
            text = example["text"]
            pronunciation = example["pronunciation"]
            meaning = example["meaning"]
            
            print(f"\n📝 Example {i}: {text}")
            print(f"   Pronunciation: {pronunciation}")
            print(f"   Meaning: {meaning}")
            
            output_file = f"pronunciation_test_q_{i}.wav"
            
            try:
                tts.tts_to_file(
                    text=text,
                    speaker_wav=speaker_wav,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        # Test Egyptian expressions
        print("\n🎯 Testing Egyptian Expressions:")
        expressions = pronunciation_data["egyptian_expressions"]["common_words"]
        
        for i, example in enumerate(expressions[:3], 1):
            text = example["text"]
            pronunciation = example["pronunciation"]
            meaning = example["meaning"]
            
            print(f"\n📝 Expression {i}: {text}")
            print(f"   Pronunciation: {pronunciation}")
            print(f"   Meaning: {meaning}")
            
            output_file = f"pronunciation_test_expr_{i}.wav"
            
            try:
                tts.tts_to_file(
                    text=text,
                    speaker_wav=speaker_wav,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        print("\n🎉 Pronunciation testing complete!")
        print("📁 Check the pronunciation_test_*.wav files")
        print("🎯 Compare with expected pronunciations listed above")
        
        return True
        
    except Exception as e:
        print(f"❌ Training error: {str(e)}")
        return False

def create_pronunciation_comparison():
    """Create comparison between standard and Egyptian pronunciation"""
    print("\n📊 Creating Pronunciation Comparison")
    print("=" * 40)
    
    try:
        from TTS.api import TTS
        
        print("🔧 Loading XTTS model...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)
        
        # Comparison texts
        comparison_texts = [
            {
                "text": "جميل جداً",
                "standard": "jameel jeddan",
                "egyptian": "gameel geddan",
                "note": "ج pronounced as 'g' in Egyptian"
            },
            {
                "text": "قال لي",
                "standard": "qaal li",
                "egyptian": "aal li",
                "note": "ق as glottal stop in Egyptian"
            },
            {
                "text": "يلا بينا",
                "standard": "yalla beena",
                "egyptian": "yalla beena",
                "note": "Pure Egyptian expression"
            },
            {
                "text": "قوي جداً",
                "standard": "qawi jeddan",
                "egyptian": "awi geddan",
                "note": "ق→ا and ج→g in Egyptian"
            }
        ]
        
        speaker_wav = "egyptian_training_data/مراجعة النحو النحو للصف الثاني الاعدادي الترم الأول 2024.wav"
        
        print("🎯 Generating pronunciation comparisons...")
        
        for i, comp in enumerate(comparison_texts, 1):
            text = comp["text"]
            standard = comp["standard"]
            egyptian = comp["egyptian"]
            note = comp["note"]
            
            print(f"\n📝 Comparison {i}: {text}")
            print(f"   Standard Arabic: {standard}")
            print(f"   Egyptian Arabic: {egyptian}")
            print(f"   Note: {note}")
            
            output_file = f"pronunciation_comparison_{i}.wav"
            
            try:
                tts.tts_to_file(
                    text=text,
                    speaker_wav=speaker_wav,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        print("\n🎉 Pronunciation comparison complete!")
        print("📁 Listen to pronunciation_comparison_*.wav files")
        print("🎯 Notice the Egyptian pronunciation patterns")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison error: {str(e)}")
        return False

def create_pronunciation_guide():
    """Create a guide for using pronunciation training results"""
    print("\n📚 Creating Pronunciation Training Guide")
    print("=" * 40)
    
    guide_content = """# Egyptian Pronunciation Training Results

## 🎯 Training Objective
Train the XTTS model to correctly pronounce Egyptian Arabic characters and accent patterns.

## 📊 Generated Training Data

### 🎤 Pronunciation Samples (19 files)
- `pronunciation_samples/pronunciation_01.wav` - ج sound training: "جميل جداً! الجو جميل النهاردة."
- `pronunciation_samples/pronunciation_02.wav` - ج sound: "جاي نروح نتجول في الجنينة."
- `pronunciation_samples/pronunciation_04.wav` - ق sound training: "قال لي إن القطر قريب من القاهرة."
- `pronunciation_samples/pronunciation_05.wav` - ق sound: "القلب قوي والروح قادرة على كل حاجة."
- `pronunciation_samples/pronunciation_07.wav` - Egyptian expressions: "يلا بينا نروح نتمشى شوية في وسط البلد."
- ... and 14 more samples

### 🧪 Pronunciation Tests
- `pronunciation_test_j_*.wav` - ج sound tests (should sound like 'g')
- `pronunciation_test_q_*.wav` - ق sound tests (should be glottal stop)
- `pronunciation_test_expr_*.wav` - Egyptian expression tests
- `pronunciation_comparison_*.wav` - Standard vs Egyptian comparisons

## 🎯 Key Egyptian Pronunciation Patterns

### ج Sound (Jim)
- **Standard Arabic**: /dʒ/ (like 'j' in "jam")
- **Egyptian Arabic**: /g/ (like 'g' in "go")
- **Examples**: جميل → "gameel", جديد → "gedeed"

### ق Sound (Qaf)
- **Standard Arabic**: /q/ (uvular stop)
- **Egyptian Arabic**: /ʔ/ (glottal stop) or /a/
- **Examples**: قال → "aal", قوي → "awi"

### ث Sound (Tha)
- **Standard Arabic**: /θ/ (like 'th' in "think")
- **Egyptian Arabic**: /s/ or /t/
- **Examples**: ثلاثة → "talata"

### ذ Sound (Dhal)
- **Standard Arabic**: /ð/ (like 'th' in "this")
- **Egyptian Arabic**: /z/ or /d/
- **Examples**: ذهب → "dahab"

## 🎭 Egyptian-Specific Features

### Common Expressions
- **يلا** (yalla) - "come on" / "let's go"
- **إزيك** (izzayyak) - "how are you"
- **قوي** (awi) - "very" / "strong"
- **خلاص** (khalas) - "finished" / "enough"

### Intonation Patterns
- Rising intonation for questions
- Stress on important words
- Natural rhythm and flow

## 🚀 How to Use Training Results

### 1. Listen to Pronunciation Samples
- Compare `pronunciation_test_*.wav` with expected pronunciations
- Notice Egyptian vs Standard Arabic differences
- Identify areas needing improvement

### 2. Test in Your GUI
- Use pronunciation samples as voice references
- Apply Egyptian-optimized settings
- Generate text with Egyptian characters

### 3. Evaluate Results
- Check if ج sounds like 'g'
- Verify ق sounds like glottal stop
- Assess overall Egyptian accent quality

## 📈 Training Effectiveness

### Success Indicators
- ✅ ج pronounced as 'g' (not 'j')
- ✅ ق pronounced as glottal stop (not uvular)
- ✅ Natural Egyptian intonation
- ✅ Correct stress patterns
- ✅ Authentic Egyptian expressions

### Areas for Improvement
- Collect more Egyptian pronunciation data
- Include regional variations
- Add emotional expressions
- Expand vocabulary coverage

## 🎯 Next Steps

1. **Evaluate Current Results**: Listen to all generated files
2. **Identify Improvements**: Note pronunciation accuracy
3. **Collect More Data**: Record native Egyptian speakers
4. **Retrain if Needed**: Use additional data for better results
5. **Integrate in GUI**: Use best samples in your application

## 🇪🇬 Egyptian Pronunciation Training Complete!

This training focused on teaching the model authentic Egyptian Arabic pronunciation patterns, character sounds, and accent features for more natural and accurate speech synthesis.
"""
    
    with open("PRONUNCIATION_TRAINING_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ Created: PRONUNCIATION_TRAINING_GUIDE.md")
    print("📖 Complete guide for pronunciation training results")
    return True

def main():
    """Main pronunciation training function"""
    print("🇪🇬 Egyptian Pronunciation Training")
    print("=" * 40)
    print("Training the model for correct Egyptian character pronunciation")
    print()
    
    success_count = 0
    total_steps = 4
    
    # Step 1: Check readiness
    print("🔍 Step 1/4: Checking training readiness...")
    if check_training_readiness():
        success_count += 1
        print("✅ Training readiness confirmed")
    else:
        print("❌ Training not ready")
        return
    
    # Step 2: Run pronunciation training
    print("\n🎓 Step 2/4: Running pronunciation training...")
    if run_simple_pronunciation_training():
        success_count += 1
        print("✅ Pronunciation training complete")
    else:
        print("❌ Pronunciation training failed")
    
    # Step 3: Create comparisons
    print("\n📊 Step 3/4: Creating pronunciation comparisons...")
    if create_pronunciation_comparison():
        success_count += 1
        print("✅ Pronunciation comparisons created")
    else:
        print("❌ Comparison creation failed")
    
    # Step 4: Create guide
    print("\n📚 Step 4/4: Creating pronunciation guide...")
    if create_pronunciation_guide():
        success_count += 1
        print("✅ Pronunciation guide created")
    else:
        print("❌ Guide creation failed")
    
    # Final results
    print("\n" + "=" * 40)
    print("🎓 PRONUNCIATION TRAINING COMPLETE!")
    print("=" * 40)
    print(f"✅ Completed {success_count}/{total_steps} steps successfully")
    
    if success_count >= 3:
        print("\n🎯 Training successful! You now have:")
        print("  • Pronunciation test files for Egyptian characters")
        print("  • Comparison files showing pronunciation differences")
        print("  • Complete pronunciation training guide")
        print("  • Ready-to-use pronunciation samples")
        
        print("\n🎧 Listen to these files to evaluate pronunciation:")
        print("  • pronunciation_test_j_*.wav - ج sound (should be 'g')")
        print("  • pronunciation_test_q_*.wav - ق sound (should be glottal stop)")
        print("  • pronunciation_comparison_*.wav - Standard vs Egyptian")
        
        print("\n🚀 Next: Use these results in your Egyptian TTS GUI!")
        print("The model now has better Egyptian pronunciation training!")
        
    else:
        print("\n⚠️ Some steps failed. Check the errors above.")
    
    print("\n🇪🇬 Egyptian pronunciation training complete!")

if __name__ == "__main__":
    main()
