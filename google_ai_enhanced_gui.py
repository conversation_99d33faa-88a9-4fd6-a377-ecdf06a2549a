#!/usr/bin/env python3
"""
Google AI Enhanced Egyptian TTS GUI
Smart voice cloning with Google Gemini AI integration
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading

class GoogleAIEgyptianTTS:
    """Google AI enhanced Egyptian TTS GUI"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 Google AI Smart Egyptian TTS 🇪🇬")
        self.root.geometry("1300x950")
        self.root.configure(bg='#0a0a0a')

        # Initialize Google AI
        self.google_ai = None
        self.current_analysis = None
        self.smart_suggestions = []
        self.ai_ready = False

        self.create_gui()
        self.initialize_google_ai()

    def create_gui(self):
        """Create the Google AI enhanced GUI"""

        # Main title
        title_frame = tk.Frame(self.root, bg='#0a0a0a')
        title_frame.pack(pady=15)

        title_label = tk.Label(title_frame, text="🤖 Google AI Smart Egyptian TTS 🇪🇬",
                              font=('Arial', 24, 'bold'), fg='#00ff88', bg='#0a0a0a')
        title_label.pack()

        subtitle_label = tk.Label(title_frame, text="Powered by Google Gemini AI for Authentic Egyptian Voice Synthesis",
                                 font=('Arial', 14), fg='#88ff88', bg='#0a0a0a')
        subtitle_label.pack()

        # Google AI status
        self.ai_status_label = tk.Label(title_frame, text="🔄 Initializing Google AI...",
                                       font=('Arial', 11, 'bold'), fg='#ffaa00', bg='#0a0a0a')
        self.ai_status_label.pack(pady=8)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=15, pady=10)

        # Smart Text Generation Tab
        self.create_smart_text_tab()

        # AI Analysis Tab
        self.create_ai_analysis_tab()

        # Smart Cloning Tab
        self.create_smart_cloning_tab()

        # AI Training Tab
        self.create_ai_training_tab()

    def create_smart_text_tab(self):
        """Create smart text generation tab"""
        text_frame = ttk.Frame(self.notebook)
        self.notebook.add(text_frame, text="🧠 Smart Text Generation")

        # AI prompt input
        prompt_frame = tk.LabelFrame(text_frame, text="💭 Google AI Prompt",
                                    font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        prompt_frame.pack(fill='x', padx=10, pady=10)

        self.prompt_text = tk.Text(prompt_frame, height=3, font=('Arial', 12),
                                  bg='#2a2a2a', fg='white', wrap='word')
        self.prompt_text.pack(fill='x', padx=8, pady=8)

        # Quick prompts
        quick_prompts_frame = tk.Frame(prompt_frame, bg='#1a1a1a')
        quick_prompts_frame.pack(fill='x', padx=8, pady=5)

        tk.Label(quick_prompts_frame, text="🚀 Quick Egyptian Prompts:",
                font=('Arial', 11, 'bold'), fg='#88ff88', bg='#1a1a1a').pack(anchor='w')

        quick_prompts = [
            "اكتب جملة مصرية جميلة عن القاهرة",
            "اكتب حوار مصري بين صديقين في المقهى",
            "اكتب نص تعليمي بالعامية المصرية عن التاريخ",
            "اكتب تعبير عن الفرح بالطريقة المصرية الأصيلة"
        ]

        for i, prompt in enumerate(quick_prompts):
            btn = tk.Button(quick_prompts_frame, text=f"{i+1}. {prompt[:35]}...",
                           command=lambda p=prompt: self.insert_prompt(p),
                           bg='#3a3a3a', fg='white', font=('Arial', 9))
            btn.pack(fill='x', padx=2, pady=1)

        # Context and generation controls
        controls_frame = tk.Frame(prompt_frame, bg='#1a1a1a')
        controls_frame.pack(fill='x', padx=8, pady=8)

        # Context selection
        tk.Label(controls_frame, text="🎯 Context:",
                font=('Arial', 11, 'bold'), fg='#88ff88', bg='#1a1a1a').pack(side='left')

        self.context_var = tk.StringVar(value="conversational")
        contexts = ["conversational", "educational", "emotional", "formal"]

        for context in contexts:
            rb = tk.Radiobutton(controls_frame, text=context, variable=self.context_var,
                               value=context, bg='#1a1a1a', fg='white',
                               selectcolor='#3a3a3a', font=('Arial', 10))
            rb.pack(side='left', padx=8)

        # Generate buttons
        generate_frame = tk.Frame(prompt_frame, bg='#1a1a1a')
        generate_frame.pack(pady=10)

        tk.Button(generate_frame, text="🧠 Generate with Google AI",
                 command=self.generate_smart_text, bg='#00aa44', fg='white',
                 font=('Arial', 13, 'bold')).pack(side='left', padx=5)

        tk.Button(generate_frame, text="🎭 Generate Variations",
                 command=self.generate_variations, bg='#4a4a4a', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)

        tk.Button(generate_frame, text="💡 Get AI Suggestions",
                 command=self.get_ai_suggestions, bg='#4a4a4a', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)

        # Generated text display
        result_frame = tk.LabelFrame(text_frame, text="✨ Google AI Generated Text",
                                    font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        result_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.generated_text = tk.Text(result_frame, height=12, font=('Arial', 12),
                                     bg='#2a2a2a', fg='#88ff88', wrap='word')
        gen_scrollbar = tk.Scrollbar(result_frame, orient='vertical', command=self.generated_text.yview)
        self.generated_text.configure(yscrollcommand=gen_scrollbar.set)

        self.generated_text.pack(side='left', fill='both', expand=True, padx=8, pady=8)
        gen_scrollbar.pack(side='right', fill='y')

        # Action buttons
        action_frame = tk.Frame(result_frame, bg='#1a1a1a')
        action_frame.pack(fill='x', padx=8, pady=5)

        tk.Button(action_frame, text="🎤 Use for TTS",
                 command=self.use_generated_for_tts, bg='#4a4a4a', fg='white',
                 font=('Arial', 10)).pack(side='left', padx=5)

        tk.Button(action_frame, text="🔍 Analyze with AI",
                 command=self.analyze_generated_text, bg='#4a4a4a', fg='white',
                 font=('Arial', 10)).pack(side='left', padx=5)

        tk.Button(action_frame, text="💾 Save Text",
                 command=self.save_generated_text, bg='#4a4a4a', fg='white',
                 font=('Arial', 10)).pack(side='left', padx=5)

    def create_ai_analysis_tab(self):
        """Create AI analysis tab"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="🔍 AI Analysis")

        # Text input for analysis
        input_frame = tk.LabelFrame(analysis_frame, text="📝 Text for Google AI Analysis",
                                   font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        input_frame.pack(fill='x', padx=10, pady=10)

        self.analysis_input = tk.Text(input_frame, height=4, font=('Arial', 12),
                                     bg='#2a2a2a', fg='white', wrap='word')
        self.analysis_input.pack(fill='x', padx=8, pady=8)

        # Sample texts for quick testing
        samples_frame = tk.Frame(input_frame, bg='#1a1a1a')
        samples_frame.pack(fill='x', padx=8, pady=5)

        tk.Label(samples_frame, text="📋 Sample Egyptian Texts:",
                font=('Arial', 11, 'bold'), fg='#88ff88', bg='#1a1a1a').pack(anchor='w')

        sample_texts = [
            "يلا بينا نروح نتمشى شوية في وسط البلد",
            "والله العظيم ده أحلى كلام سمعته النهاردة!",
            "معلش يا جدع، مش مشكلة، هنعمل اللي نقدر عليه"
        ]

        for text in sample_texts:
            btn = tk.Button(samples_frame, text=text,
                           command=lambda t=text: self.insert_sample_text(t),
                           bg='#3a3a3a', fg='white', font=('Arial', 9))
            btn.pack(fill='x', padx=2, pady=1)

        # Analysis controls
        controls_frame = tk.Frame(input_frame, bg='#1a1a1a')
        controls_frame.pack(fill='x', padx=8, pady=8)

        tk.Button(controls_frame, text="🧠 Analyze with Google AI",
                 command=self.analyze_text_with_ai, bg='#00aa44', fg='white',
                 font=('Arial', 12, 'bold')).pack(side='left', padx=5)

        tk.Button(controls_frame, text="⚙️ Optimize Parameters",
                 command=self.optimize_parameters, bg='#4a4a4a', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)

        # Analysis results
        results_frame = tk.LabelFrame(analysis_frame, text="📊 Google AI Analysis Results",
                                     font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.analysis_results = tk.Text(results_frame, height=15, font=('Consolas', 10),
                                       bg='#2a2a2a', fg='#88ff88', wrap='word')
        analysis_scrollbar = tk.Scrollbar(results_frame, orient='vertical', command=self.analysis_results.yview)
        self.analysis_results.configure(yscrollcommand=analysis_scrollbar.set)

        self.analysis_results.pack(side='left', fill='both', expand=True, padx=8, pady=8)
        analysis_scrollbar.pack(side='right', fill='y')

    def create_smart_cloning_tab(self):
        """Create smart cloning tab"""
        cloning_frame = ttk.Frame(self.notebook)
        self.notebook.add(cloning_frame, text="🎤 Smart Cloning")

        # Voice selection with AI recommendations
        voice_frame = tk.LabelFrame(cloning_frame, text="🎭 AI-Recommended Voice Selection",
                                   font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        voice_frame.pack(fill='x', padx=10, pady=10)

        self.voice_var = tk.StringVar()

        # AI will recommend best voices based on text analysis
        voice_options = [
            "🤖 Let Google AI Choose Best Voice",
            "🎯 trained_01_ج_sound.wav (AI: Best for ج pronunciation)",
            "🎭 trained_07_expressions.wav (AI: Best for expressions)",
            "📚 trained_11_educational.wav (AI: Best for educational)",
            "📁 Browse Custom Voice..."
        ]

        for option in voice_options:
            rb = tk.Radiobutton(voice_frame, text=option, variable=self.voice_var,
                               value=option, bg='#1a1a1a', fg='white',
                               selectcolor='#3a3a3a', font=('Arial', 11))
            rb.pack(anchor='w', padx=10, pady=3)

        self.voice_var.set(voice_options[0])  # Default to AI choice

        # Text input with AI suggestions
        text_input_frame = tk.LabelFrame(cloning_frame, text="📝 Text Input with Google AI Suggestions",
                                        font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        text_input_frame.pack(fill='x', padx=10, pady=10)

        self.cloning_text = tk.Text(text_input_frame, height=4, font=('Arial', 12),
                                   bg='#2a2a2a', fg='white', wrap='word')
        self.cloning_text.pack(fill='x', padx=8, pady=8)

        # AI suggestions panel
        suggestions_frame = tk.Frame(text_input_frame, bg='#1a1a1a')
        suggestions_frame.pack(fill='x', padx=8, pady=5)

        tk.Label(suggestions_frame, text="🧠 Google AI Smart Suggestions:",
                font=('Arial', 11, 'bold'), fg='#88ff88', bg='#1a1a1a').pack(anchor='w')

        self.suggestions_frame = tk.Frame(suggestions_frame, bg='#1a1a1a')
        self.suggestions_frame.pack(fill='x', pady=3)

        tk.Button(suggestions_frame, text="🔄 Get New AI Suggestions",
                 command=self.refresh_ai_suggestions, bg='#3a3a3a', fg='white',
                 font=('Arial', 10)).pack(anchor='w', pady=3)

        # AI-optimized parameters display
        params_frame = tk.LabelFrame(cloning_frame, text="⚙️ Google AI Optimized Parameters",
                                    font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        params_frame.pack(fill='x', padx=10, pady=10)

        self.ai_params_text = tk.Text(params_frame, height=4, font=('Consolas', 10),
                                     bg='#2a2a2a', fg='#88ff88', state='disabled')
        self.ai_params_text.pack(fill='x', padx=8, pady=8)

        # Smart generation controls
        generation_frame = tk.LabelFrame(cloning_frame, text="🚀 Google AI Enhanced Generation",
                                        font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        generation_frame.pack(fill='x', padx=10, pady=10)

        gen_controls_frame = tk.Frame(generation_frame, bg='#1a1a1a')
        gen_controls_frame.pack(pady=10)

        tk.Button(gen_controls_frame, text="🤖 Smart Generate with Google AI",
                 command=self.smart_generate_speech, bg='#00aa44', fg='white',
                 font=('Arial', 13, 'bold')).pack(side='left', padx=5)

        tk.Button(gen_controls_frame, text="🧪 AI Quality Test",
                 command=self.ai_quality_test, bg='#4a4a4a', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)

        # Progress and status
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(generation_frame, variable=self.progress_var,
                                           maximum=100, length=600)
        self.progress_bar.pack(pady=8)

        self.status_label = tk.Label(generation_frame, text="🤖 Ready for Google AI enhanced generation",
                                    font=('Arial', 11), fg='#88ff88', bg='#1a1a1a')
        self.status_label.pack()

    def create_ai_training_tab(self):
        """Create AI training tab"""
        training_frame = ttk.Frame(self.notebook)
        self.notebook.add(training_frame, text="🎓 AI Training")

        # Google AI status
        status_frame = tk.LabelFrame(training_frame, text="🤖 Google AI Status",
                                    font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        status_frame.pack(fill='x', padx=10, pady=10)

        self.google_status_text = tk.Text(status_frame, height=5, font=('Consolas', 10),
                                         bg='#2a2a2a', fg='#88ff88', state='disabled')
        self.google_status_text.pack(fill='x', padx=8, pady=8)

        # AI training plan
        training_plan_frame = tk.LabelFrame(training_frame, text="🎓 Google AI Training Plan",
                                           font=('Arial', 13, 'bold'), fg='#00ff88', bg='#1a1a1a')
        training_plan_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.training_plan_text = tk.Text(training_plan_frame, height=12, font=('Consolas', 10),
                                         bg='#2a2a2a', fg='#88ff88', wrap='word')
        training_scrollbar = tk.Scrollbar(training_plan_frame, orient='vertical', command=self.training_plan_text.yview)
        self.training_plan_text.configure(yscrollcommand=training_scrollbar.set)

        self.training_plan_text.pack(side='left', fill='both', expand=True, padx=8, pady=8)
        training_scrollbar.pack(side='right', fill='y')

        # AI controls
        ai_controls_frame = tk.Frame(training_frame, bg='#1a1a1a')
        ai_controls_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(ai_controls_frame, text="🧠 Generate AI Training Plan",
                 command=self.generate_training_plan, bg='#00aa44', fg='white',
                 font=('Arial', 12, 'bold')).pack(side='left', padx=5)

        tk.Button(ai_controls_frame, text="🔄 Refresh Google AI Status",
                 command=self.refresh_google_status, bg='#4a4a4a', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)

        tk.Button(ai_controls_frame, text="📊 Export AI Report",
                 command=self.export_google_report, bg='#4a4a4a', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)

    def initialize_google_ai(self):
        """Initialize Google AI in background"""
        def init_google():
            try:
                from google_ai_integration import GoogleAISmartCloning
                self.google_ai = GoogleAISmartCloning()

                if self.google_ai.ai_ready:
                    self.ai_ready = True
                    self.root.after(0, self.update_google_status_success)
                else:
                    self.ai_ready = False
                    self.root.after(0, self.update_google_status_fallback)

            except Exception as e:
                self.ai_ready = False
                self.root.after(0, lambda: self.update_google_status_error(str(e)))

        threading.Thread(target=init_google, daemon=True).start()

    def update_google_status_success(self):
        """Update status on successful Google AI initialization"""
        self.ai_status_label.config(text="✅ Google AI Connected - Smart Features Active", fg='#00ff88')
        self.update_google_status("✅ Google Gemini AI connected successfully")
        self.update_google_status("🧠 Smart text generation ready")
        self.update_google_status("🔍 AI analysis capabilities active")
        self.update_google_status("⚙️ Parameter optimization available")
        self.update_google_status("🎓 Training plan generation ready")

        # Get initial AI suggestions
        self.refresh_ai_suggestions()

    def update_google_status_fallback(self):
        """Update status when using fallback methods"""
        self.ai_status_label.config(text="⚠️ Google AI Fallback Mode - Intelligent Features Active", fg='#ffaa00')
        self.update_google_status("⚠️ Google AI using fallback mode")
        self.update_google_status("🧠 Intelligent fallback text generation ready")
        self.update_google_status("🔍 Smart analysis using local algorithms")
        self.update_google_status("💡 All features available with intelligent fallbacks")

    def update_google_status_error(self, error):
        """Update status on Google AI initialization error"""
        self.ai_status_label.config(text="❌ Google AI Error - Check Connection", fg='#ff4444')
        self.update_google_status(f"❌ Google AI error: {error}")
        self.update_google_status("💡 Using intelligent fallback methods")

    def update_google_status(self, message):
        """Update Google AI status display"""
        if hasattr(self, 'google_status_text'):
            self.google_status_text.config(state='normal')
            self.google_status_text.insert(tk.END, f"{message}\n")
            self.google_status_text.see(tk.END)
            self.google_status_text.config(state='disabled')

    def insert_prompt(self, prompt):
        """Insert prompt into text area"""
        self.prompt_text.delete("1.0", tk.END)
        self.prompt_text.insert("1.0", prompt)

    def generate_smart_text(self):
        """Generate smart text using Google AI"""
        if not self.google_ai:
            messagebox.showerror("Error", "Google AI not initialized")
            return

        prompt = self.prompt_text.get("1.0", tk.END).strip()
        if not prompt:
            messagebox.showwarning("Warning", "Please enter a prompt")
            return

        context = self.context_var.get()

        def generate():
            try:
                self.root.after(0, lambda: self.update_status("🧠 Generating with Google AI..."))

                result = self.google_ai.generate_smart_text(prompt, context)

                if result:
                    generated_text = result.get("generated_text", "")
                    self.root.after(0, lambda: self.display_generated_text(generated_text))
                else:
                    self.root.after(0, lambda: self.update_status("❌ Generation failed"))

            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"❌ Error: {str(e)}"))

        threading.Thread(target=generate, daemon=True).start()

    def display_generated_text(self, text):
        """Display generated text"""
        self.generated_text.delete("1.0", tk.END)
        self.generated_text.insert("1.0", text)
        self.update_status("✅ Text generated successfully")

    def generate_variations(self):
        """Generate variations of current text"""
        if not self.google_ai:
            messagebox.showerror("Error", "Google AI not initialized")
            return

        current_text = self.generated_text.get("1.0", tk.END).strip()
        if not current_text:
            messagebox.showwarning("Warning", "No text to create variations from")
            return

        def generate():
            try:
                self.root.after(0, lambda: self.update_status("🎭 Generating variations..."))

                variations = self.google_ai.generate_egyptian_variations(current_text)

                if variations:
                    variations_text = "\n\n".join([f"Variation {i+1}: {var}" for i, var in enumerate(variations)])
                    self.root.after(0, lambda: self.display_generated_text(variations_text))
                else:
                    self.root.after(0, lambda: self.update_status("❌ Variation generation failed"))

            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"❌ Error: {str(e)}"))

        threading.Thread(target=generate, daemon=True).start()

    def get_ai_suggestions(self):
        """Get AI suggestions"""
        if not self.google_ai:
            messagebox.showwarning("Warning", "Google AI not available - using fallback suggestions")
            self.display_fallback_suggestions()
            return

        context = self.context_var.get()

        def get_suggestions():
            try:
                self.root.after(0, lambda: self.update_status("💡 Getting AI suggestions..."))

                suggestions = self.google_ai.generate_smart_suggestions(context)

                if suggestions:
                    suggestions_text = "\n\n".join([f"Suggestion {i+1}: {sug}" for i, sug in enumerate(suggestions)])
                    self.root.after(0, lambda: self.display_generated_text(suggestions_text))
                else:
                    self.root.after(0, lambda: self.display_fallback_suggestions())

            except Exception as e:
                self.root.after(0, lambda: self.display_fallback_suggestions())

        threading.Thread(target=get_suggestions, daemon=True).start()

    def display_fallback_suggestions(self):
        """Display fallback suggestions"""
        fallback_suggestions = [
            "جميل قوي! إزيك النهاردة؟",
            "يلا بينا نروح نتمشى شوية في وسط البلد.",
            "والله العظيم ده أحلى كلام سمعته النهاردة!",
            "مصر أم الدنيا وشعبها كريم قوي.",
            "الدرس النهاردة مهم جداً لازم نركز فيه قوي."
        ]

        suggestions_text = "\n\n".join([f"Suggestion {i+1}: {sug}" for i, sug in enumerate(fallback_suggestions)])
        self.display_generated_text(suggestions_text)
        self.update_status("💡 Fallback suggestions displayed")

    def use_generated_for_tts(self):
        """Use generated text for TTS"""
        text = self.generated_text.get("1.0", tk.END).strip()
        if text:
            self.cloning_text.delete("1.0", tk.END)
            self.cloning_text.insert("1.0", text)
            self.notebook.select(2)  # Switch to cloning tab
            messagebox.showinfo("Success", "Text moved to TTS generation")
        else:
            messagebox.showwarning("Warning", "No text to use")

    def analyze_generated_text(self):
        """Analyze generated text"""
        text = self.generated_text.get("1.0", tk.END).strip()
        if text:
            self.analysis_input.delete("1.0", tk.END)
            self.analysis_input.insert("1.0", text)
            self.notebook.select(1)  # Switch to analysis tab
            self.analyze_text_with_ai()
        else:
            messagebox.showwarning("Warning", "No text to analyze")

    def save_generated_text(self):
        """Save generated text"""
        text = self.generated_text.get("1.0", tk.END).strip()
        if text:
            file_path = filedialog.asksaveasfilename(
                title="Save Generated Text",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if file_path:
                try:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(text)
                    messagebox.showinfo("Success", f"Text saved: {file_path}")
                except Exception as e:
                    messagebox.showerror("Error", f"Save failed: {str(e)}")
        else:
            messagebox.showwarning("Warning", "No text to save")

    def insert_sample_text(self, text):
        """Insert sample text for analysis"""
        self.analysis_input.delete("1.0", tk.END)
        self.analysis_input.insert("1.0", text)

    def analyze_text_with_ai(self):
        """Analyze text with Google AI"""
        text = self.analysis_input.get("1.0", tk.END).strip()
        if not text:
            messagebox.showwarning("Warning", "Please enter text to analyze")
            return

        if not self.google_ai:
            self.display_fallback_analysis(text)
            return

        def analyze():
            try:
                self.root.after(0, lambda: self.update_analysis_status("🔍 Analyzing with Google AI..."))

                analysis = self.google_ai.analyze_text_for_cloning(text)

                if analysis:
                    self.root.after(0, lambda: self.display_analysis_results(analysis))
                else:
                    self.root.after(0, lambda: self.display_fallback_analysis(text))

            except Exception as e:
                self.root.after(0, lambda: self.display_fallback_analysis(text))

        threading.Thread(target=analyze, daemon=True).start()

    def display_analysis_results(self, analysis):
        """Display analysis results"""
        self.analysis_results.delete("1.0", tk.END)

        result_text = f"🔍 Google AI Analysis Results\n"
        result_text += f"=" * 50 + "\n\n"

        result_text += f"📊 Egyptian Dialect Score: {analysis.get('egyptian_dialect_score', 0):.2f}/1.0\n"
        result_text += f"🎯 Pronunciation Complexity: {analysis.get('pronunciation_complexity', 0):.2f}/1.0\n"
        result_text += f"😊 Emotional Content: {analysis.get('emotional_content', 0):.2f}/1.0\n"
        result_text += f"🎭 Recommended Voice: {analysis.get('recommended_voice_type', 'Unknown')}\n\n"

        # TTS Parameters
        tts_params = analysis.get('tts_parameters', {})
        if tts_params:
            result_text += f"⚙️ Recommended TTS Parameters:\n"
            for key, value in tts_params.items():
                result_text += f"  {key}: {value}\n"
            result_text += "\n"

        # Pronunciation notes
        notes = analysis.get('pronunciation_notes', [])
        if notes:
            result_text += f"📝 Pronunciation Notes:\n"
            for note in notes:
                result_text += f"  • {note}\n"

        self.analysis_results.insert("1.0", result_text)
        self.update_analysis_status("✅ Analysis complete")

    def display_fallback_analysis(self, text):
        """Display fallback analysis"""
        self.analysis_results.delete("1.0", tk.END)

        # Simple analysis
        egyptian_words = ["يلا", "خلاص", "معلش", "قوي", "النهاردة", "شوية", "حاجة", "كده"]
        egyptian_score = sum(1 for word in egyptian_words if word in text) / len(egyptian_words)

        result_text = f"🔍 Fallback Analysis Results\n"
        result_text += f"=" * 50 + "\n\n"
        result_text += f"📊 Egyptian Dialect Score: {egyptian_score:.2f}/1.0\n"
        result_text += f"🎯 Text Length: {len(text)} characters\n"
        result_text += f"🎭 Recommended Voice: egyptian_standard\n\n"
        result_text += f"💡 Fallback analysis used - Google AI not available\n"

        self.analysis_results.insert("1.0", result_text)
        self.update_analysis_status("⚠️ Fallback analysis complete")

    def update_analysis_status(self, message):
        """Update analysis status"""
        print(f"Analysis: {message}")

    def optimize_parameters(self):
        """Optimize parameters based on analysis"""
        messagebox.showinfo("Info", "Parameter optimization feature coming soon!")

    def smart_generate_speech(self):
        """Smart generate speech with actual audio output"""
        text = self.cloning_text.get("1.0", tk.END).strip()
        if not text:
            messagebox.showwarning("Warning", "Please enter text to generate")
            return

        def generate():
            try:
                self.root.after(0, lambda: self.update_status("🚀 Initializing Google AI Voice Cloning..."))
                self.root.after(0, lambda: self.progress_var.set(10))

                # Initialize voice cloning system
                from google_ai_voice_cloning import GoogleAIVoiceCloning
                voice_cloning = GoogleAIVoiceCloning()

                self.root.after(0, lambda: self.update_status("🧠 Analyzing text with Google AI..."))
                self.root.after(0, lambda: self.progress_var.set(30))

                # Generate speech
                output_file = voice_cloning.generate_smart_speech(text)

                self.root.after(0, lambda: self.progress_var.set(100))

                if output_file and os.path.exists(output_file):
                    filename = os.path.basename(output_file)
                    self.root.after(0, lambda: self.update_status(f"✅ Generated: {filename}"))
                    self.root.after(0, lambda: self.show_success_dialog(output_file))
                else:
                    self.root.after(0, lambda: self.update_status("❌ Generation failed"))
                    self.root.after(0, lambda: messagebox.showerror("Error", "Speech generation failed"))

            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"❌ Error: {str(e)}"))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Generation error: {str(e)}"))

        threading.Thread(target=generate, daemon=True).start()

    def show_success_dialog(self, output_file):
        """Show success dialog with options"""
        filename = os.path.basename(output_file)
        folder = os.path.dirname(output_file)

        result = messagebox.askyesnocancel(
            "Success!",
            f"✅ Speech generated successfully!\n\n"
            f"📁 File: {filename}\n"
            f"📂 Folder: {folder}\n\n"
            f"🔊 Play audio file? (Yes)\n"
            f"📂 Open folder? (No)\n"
            f"❌ Close dialog? (Cancel)"
        )

        if result is True:  # Yes - Play file
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(output_file)
                else:  # Linux/Mac
                    import subprocess
                    subprocess.run(['xdg-open', output_file])
            except Exception as e:
                messagebox.showerror("Error", f"Could not play file: {str(e)}")
        elif result is False:  # No - Open folder
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(folder)
                else:  # Linux/Mac
                    import subprocess
                    subprocess.run(['xdg-open', folder])
            except Exception as e:
                messagebox.showerror("Error", f"Could not open folder: {str(e)}")

    def ai_quality_test(self):
        """AI quality test"""
        messagebox.showinfo("Info", "AI quality test feature coming soon!")

    def refresh_ai_suggestions(self):
        """Refresh AI suggestions"""
        self.get_ai_suggestions()

    def generate_training_plan(self):
        """Generate training plan"""
        if not self.google_ai:
            self.display_fallback_training_plan()
            return

        def generate():
            try:
                self.root.after(0, lambda: self.update_training_status("🎓 Generating training plan..."))

                current_perf = {"quality": 75, "egyptian_authenticity": 70, "training_progress": 60}
                plan = self.google_ai.create_smart_training_plan(current_perf)

                if plan:
                    self.root.after(0, lambda: self.display_training_plan(plan))
                else:
                    self.root.after(0, lambda: self.display_fallback_training_plan())

            except Exception as e:
                self.root.after(0, lambda: self.display_fallback_training_plan())

        threading.Thread(target=generate, daemon=True).start()

    def display_training_plan(self, plan):
        """Display training plan"""
        self.training_plan_text.delete("1.0", tk.END)

        plan_text = f"🎓 Google AI Training Plan\n"
        plan_text += f"=" * 50 + "\n\n"

        plan_text += f"⏰ Timeline: {plan.get('timeline', 'Unknown')}\n\n"

        # Priority areas
        areas = plan.get('priority_areas', [])
        if areas:
            plan_text += f"🎯 Priority Areas:\n"
            for i, area in enumerate(areas, 1):
                plan_text += f"  {i}. {area}\n"
            plan_text += "\n"

        # Training steps
        steps = plan.get('training_steps', [])
        if steps:
            plan_text += f"📋 Training Steps:\n"
            for i, step in enumerate(steps, 1):
                plan_text += f"  {i}. {step}\n"
            plan_text += "\n"

        # Success metrics
        metrics = plan.get('success_metrics', {})
        if metrics:
            plan_text += f"📊 Success Metrics:\n"
            for key, value in metrics.items():
                plan_text += f"  {key}: {value}\n"

        self.training_plan_text.insert("1.0", plan_text)
        self.update_training_status("✅ Training plan generated")

    def display_fallback_training_plan(self):
        """Display fallback training plan"""
        self.training_plan_text.delete("1.0", tk.END)

        plan_text = f"🎓 Fallback Training Plan\n"
        plan_text += f"=" * 50 + "\n\n"
        plan_text += f"⏰ Timeline: 2-4 weeks\n\n"
        plan_text += f"🎯 Priority Areas:\n"
        plan_text += f"  1. Egyptian pronunciation accuracy\n"
        plan_text += f"  2. Accent authenticity improvement\n"
        plan_text += f"  3. Voice quality enhancement\n\n"
        plan_text += f"📋 Training Steps:\n"
        plan_text += f"  1. Collect more Egyptian voice samples\n"
        plan_text += f"  2. Practice ج and ق pronunciation\n"
        plan_text += f"  3. Train with Egyptian expressions\n"
        plan_text += f"  4. Optimize TTS parameters\n\n"
        plan_text += f"💡 Fallback plan used - Google AI not available\n"

        self.training_plan_text.insert("1.0", plan_text)
        self.update_training_status("⚠️ Fallback training plan displayed")

    def update_training_status(self, message):
        """Update training status"""
        print(f"Training: {message}")

    def refresh_google_status(self):
        """Refresh Google status"""
        if self.ai_ready:
            self.update_google_status("✅ Google AI status refreshed - all systems operational")
        else:
            self.update_google_status("⚠️ Google AI not available - using fallback methods")

    def export_google_report(self):
        """Export Google report"""
        messagebox.showinfo("Info", "Export feature coming soon!")

    def update_status(self, message):
        """Update status"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)

    def run(self):
        """Run the GUI"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🤖 Starting Google AI Enhanced Egyptian TTS GUI...")
    app = GoogleAIEgyptianTTS()
    app.run()

if __name__ == "__main__":
    main()
