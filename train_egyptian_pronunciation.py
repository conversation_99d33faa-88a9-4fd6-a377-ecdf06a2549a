#!/usr/bin/env python3
"""
Train Egyptian Pronunciation & Accent
Advanced training for correct Egyptian Arabic character pronunciation
"""

import os
import sys
import json
import shutil

def create_pronunciation_training_data():
    """Create training data focused on Egyptian pronunciation"""
    print("🎯 Creating Egyptian Pronunciation Training Data")
    print("=" * 50)
    
    # Egyptian pronunciation patterns and examples
    pronunciation_data = {
        "ج_sound": {
            "standard": "ج",
            "egyptian_sound": "g",
            "examples": [
                {"text": "جميل", "pronunciation": "gameel", "meaning": "beautiful"},
                {"text": "جديد", "pronunciation": "gedeed", "meaning": "new"},
                {"text": "جاي", "pronunciation": "gay", "meaning": "coming"},
                {"text": "جوة", "pronunciation": "gowa", "meaning": "inside"},
                {"text": "جنب", "pronunciation": "ganb", "meaning": "beside"}
            ]
        },
        "ق_sound": {
            "standard": "ق",
            "egyptian_sound": "glottal_stop_or_a",
            "examples": [
                {"text": "قال", "pronunciation": "aal", "meaning": "he said"},
                {"text": "قلب", "pronunciation": "alb", "meaning": "heart"},
                {"text": "قوي", "pronunciation": "awi", "meaning": "strong/very"},
                {"text": "قاعد", "pronunciation": "aa'ed", "meaning": "sitting"},
                {"text": "قريب", "pronunciation": "areeb", "meaning": "close"}
            ]
        },
        "ث_sound": {
            "standard": "ث",
            "egyptian_sound": "s_or_t",
            "examples": [
                {"text": "ثلاثة", "pronunciation": "talata", "meaning": "three"},
                {"text": "ثاني", "pronunciation": "tani", "meaning": "second"},
                {"text": "ثقيل", "pronunciation": "te'eel", "meaning": "heavy"}
            ]
        },
        "ذ_sound": {
            "standard": "ذ",
            "egyptian_sound": "z_or_d",
            "examples": [
                {"text": "ذهب", "pronunciation": "dahab", "meaning": "gold"},
                {"text": "ذكي", "pronunciation": "zaki", "meaning": "smart"},
                {"text": "ذراع", "pronunciation": "deraa'", "meaning": "arm"}
            ]
        },
        "egyptian_expressions": {
            "greetings": [
                {"text": "أهلاً وسهلاً", "pronunciation": "ahlan wa sahlan", "meaning": "welcome"},
                {"text": "إزيك", "pronunciation": "izzayyak", "meaning": "how are you"},
                {"text": "إزي الحال", "pronunciation": "izzi el-hal", "meaning": "how are things"}
            ],
            "common_words": [
                {"text": "يلا", "pronunciation": "yalla", "meaning": "come on/let's go"},
                {"text": "خلاص", "pronunciation": "khalas", "meaning": "finished/enough"},
                {"text": "معلش", "pronunciation": "ma'alesh", "meaning": "never mind"},
                {"text": "إن شاء الله", "pronunciation": "inshallah", "meaning": "God willing"},
                {"text": "ماشي", "pronunciation": "mashi", "meaning": "okay/walking"}
            ],
            "intensifiers": [
                {"text": "قوي", "pronunciation": "awi", "meaning": "very/strong"},
                {"text": "جداً", "pronunciation": "geddan", "meaning": "very"},
                {"text": "أوي", "pronunciation": "awi", "meaning": "very"}
            ]
        }
    }
    
    # Save pronunciation data
    with open("egyptian_pronunciation_data.json", "w", encoding="utf-8") as f:
        json.dump(pronunciation_data, f, indent=2, ensure_ascii=False)
    
    print("✅ Created: egyptian_pronunciation_data.json")
    print("📋 Contains Egyptian pronunciation patterns and examples")
    return pronunciation_data

def create_pronunciation_training_texts():
    """Create training texts focused on pronunciation"""
    print("\n📝 Creating Pronunciation Training Texts")
    print("=" * 40)
    
    # Training texts that emphasize Egyptian pronunciation
    training_texts = [
        # ج sound training
        "جميل جداً! الجو جميل النهاردة.",
        "جاي نروح نتجول في الجنينة.",
        "الجامعة جنب الجسر الجديد.",
        
        # ق sound training  
        "قال لي إن القطر قريب من القاهرة.",
        "القلب قوي والروح قادرة على كل حاجة.",
        "قاعد في القهوة وبشرب قهوة قوية.",
        
        # Mixed Egyptian sounds
        "يلا بينا نروح نتمشى شوية في وسط البلد.",
        "والله العظيم ده أحلى كلام سمعته النهاردة!",
        "اتفضل اشرب شاي واقعد معانا شوية.",
        "مصر أم الدنيا وشعبها كريم قوي.",
        
        # Educational content with Egyptian pronunciation
        "الدرس النهاردة مهم جداً لازم نركز فيه قوي.",
        "النحو مش صعب لو فهمناه صح وطبقناه.",
        "القواعد دي مهمة عشان نكتب ونتكلم صح.",
        
        # Conversational Egyptian
        "إزيك يا حبيبي؟ إيه أخبارك النهاردة؟",
        "خلاص كده، يلا نشوف إيه اللي جاي.",
        "معلش، مش مشكلة، هنعمل اللي نقدر عليه.",
        
        # Emotional expressions
        "ربنا يخليك ويحفظك من كل شر.",
        "الحمد لله على كل حاجة حلوة في حياتنا.",
        "إن شاء الله كله هيبقى تمام وهنفرح قريب."
    ]
    
    # Create training directory
    os.makedirs("pronunciation_training", exist_ok=True)
    
    # Save training texts
    with open("pronunciation_training/training_texts.txt", "w", encoding="utf-8") as f:
        for i, text in enumerate(training_texts, 1):
            f.write(f"{i:02d}. {text}\n")
    
    print(f"✅ Created {len(training_texts)} pronunciation training texts")
    print("📁 Saved to: pronunciation_training/training_texts.txt")
    return training_texts

def create_pronunciation_samples():
    """Create pronunciation training samples"""
    print("\n🎤 Creating Pronunciation Training Samples")
    print("=" * 45)
    
    try:
        from TTS.api import TTS
        
        print("🔧 Loading XTTS model...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=False)
        
        # Use the best Egyptian audio from training data
        training_dir = "egyptian_training_data"
        if os.path.exists(training_dir):
            audio_files = [f for f in os.listdir(training_dir) if f.endswith(('.wav', '.mp3'))]
            if audio_files:
                # Use the largest/best quality file
                best_audio = max(audio_files, key=lambda f: os.path.getsize(os.path.join(training_dir, f)))
                speaker_wav = os.path.join(training_dir, best_audio)
                print(f"🎯 Using speaker audio: {best_audio}")
            else:
                print("❌ No audio files found in training data")
                return False
        else:
            print("❌ Training data directory not found")
            return False
        
        # Load training texts
        with open("pronunciation_training/training_texts.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        training_texts = [line.split('. ', 1)[1].strip() for line in lines if '. ' in line]
        
        # Create pronunciation samples directory
        os.makedirs("pronunciation_samples", exist_ok=True)
        
        print("🎯 Generating pronunciation training samples...")
        
        for i, text in enumerate(training_texts, 1):
            print(f"\n📝 Sample {i:02d}: {text}")
            output_file = f"pronunciation_samples/pronunciation_{i:02d}.wav"
            
            try:
                tts.tts_to_file(
                    text=text,
                    speaker_wav=speaker_wav,
                    language="ar",
                    file_path=output_file
                )
                print(f"✅ Generated: {output_file}")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        print(f"\n🎉 Pronunciation sample generation complete!")
        print(f"📁 Generated {len(training_texts)} samples in pronunciation_samples/")
        return True
        
    except Exception as e:
        print(f"❌ Sample generation error: {str(e)}")
        return False

def create_pronunciation_training_config():
    """Create configuration for pronunciation training"""
    print("\n⚙️ Creating Pronunciation Training Configuration")
    print("=" * 50)
    
    # Advanced configuration for pronunciation training
    pronunciation_config = {
        "model_name": "xtts_egyptian_pronunciation",
        "run_name": "egyptian_pronunciation_training",
        "run_description": "Training XTTS for correct Egyptian Arabic pronunciation",
        
        # Dataset configuration
        "datasets": [
            {
                "name": "egyptian_pronunciation",
                "path": "./pronunciation_training/",
                "meta_file_train": "pronunciation_metadata.csv",
                "language": "ar",
                "phoneme_language": "ar"
            }
        ],
        
        # Model configuration for pronunciation
        "model_args": {
            "gpt_batch_size": 1,
            "enable_redaction": False,
            "kv_cache": True,
            "gpt_max_audio_len": 229376,
            "gpt_max_text_len": 200,
            "gpt_max_new_tokens": 1024,
            "gpt_min_audio_len": 22050,
            "gpt_num_audio_tokens": 1024,
            "gpt_start_audio_token": 1024,
            "gpt_stop_audio_token": 1025,
            "gpt_use_masking_gt_prompt_approach": True,
            "gpt_use_perceiver_resampler": True
        },
        
        # Audio configuration
        "audio": {
            "sample_rate": 22050,
            "output_sample_rate": 24000
        },
        
        # Training configuration for pronunciation
        "batch_size": 1,
        "eval_batch_size": 1,
        "num_loader_workers": 0,
        "num_eval_loader_workers": 0,
        "run_eval": True,
        "test_delay_epochs": 3,
        
        "epochs": 50,  # More epochs for pronunciation training
        "save_step": 500,
        "eval_step": 500,
        "log_step": 50,
        "save_n_checkpoints": 10,
        
        # Learning rate for pronunciation fine-tuning
        "lr": 1e-05,  # Lower learning rate for fine-tuning
        "weight_decay": 1e-06,
        "optimizer": "AdamW",
        "scheduler": "MultiStepLR",
        "lr_scheduler_params": {
            "milestones": [25000, 75000, 150000],
            "gamma": 0.5,
            "last_epoch": -1
        },
        
        "output_path": "./egyptian_pronunciation_model/",
        
        # Pronunciation-specific settings
        "pronunciation_focus": {
            "egyptian_sounds": {
                "ج": "g_sound",
                "ق": "glottal_stop",
                "ث": "s_or_t_sound", 
                "ذ": "z_or_d_sound"
            },
            "training_emphasis": [
                "Character-level pronunciation accuracy",
                "Egyptian accent patterns",
                "Natural intonation",
                "Stress patterns",
                "Vowel modifications"
            ]
        },
        
        "use_phonemes": True,  # Enable phoneme training
        "phonemizer": "espeak",
        "phoneme_language": "ar",
        
        "compute_input_seq_cache": True,
        "precompute_num_workers": 0
    }
    
    with open("pronunciation_training_config.json", "w", encoding="utf-8") as f:
        json.dump(pronunciation_config, f, indent=2, ensure_ascii=False)
    
    print("✅ Created: pronunciation_training_config.json")
    print("📋 Advanced configuration for Egyptian pronunciation training")
    return True

def create_pronunciation_metadata():
    """Create metadata for pronunciation training"""
    print("\n📊 Creating Pronunciation Training Metadata")
    print("=" * 45)
    
    # Create metadata CSV for pronunciation training
    metadata_lines = ["audio_file|text|speaker_name|pronunciation_focus"]
    
    # Load training texts
    if os.path.exists("pronunciation_training/training_texts.txt"):
        with open("pronunciation_training/training_texts.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        training_texts = [line.split('. ', 1)[1].strip() for line in lines if '. ' in line]
        
        for i, text in enumerate(training_texts, 1):
            # Determine pronunciation focus
            focus = "general"
            if "ج" in text:
                focus = "ج_sound"
            elif "ق" in text:
                focus = "ق_sound"
            elif any(word in text for word in ["يلا", "إزيك", "قوي"]):
                focus = "egyptian_expressions"
            
            metadata_lines.append(f"pronunciation_{i:02d}.wav|{text}|egyptian_speaker|{focus}")
    
    # Save metadata
    with open("pronunciation_training/pronunciation_metadata.csv", "w", encoding="utf-8") as f:
        f.write("\n".join(metadata_lines))
    
    print(f"✅ Created metadata with {len(metadata_lines)-1} entries")
    print("📁 Saved to: pronunciation_training/pronunciation_metadata.csv")
    return True

def main():
    """Main pronunciation training setup"""
    print("🇪🇬 Egyptian Pronunciation Training Setup")
    print("=" * 50)
    print("This will create advanced training for correct Egyptian pronunciation")
    print()
    
    success_count = 0
    total_steps = 5
    
    # Step 1: Create pronunciation data
    print("📚 Step 1/5: Creating pronunciation data...")
    try:
        create_pronunciation_training_data()
        success_count += 1
        print("✅ Pronunciation data created")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Step 2: Create training texts
    print("\n📝 Step 2/5: Creating training texts...")
    try:
        create_pronunciation_training_texts()
        success_count += 1
        print("✅ Training texts created")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Step 3: Create pronunciation samples
    print("\n🎤 Step 3/5: Creating pronunciation samples...")
    try:
        if create_pronunciation_samples():
            success_count += 1
            print("✅ Pronunciation samples created")
        else:
            print("❌ Pronunciation sample creation failed")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Step 4: Create training config
    print("\n⚙️ Step 4/5: Creating training configuration...")
    try:
        create_pronunciation_training_config()
        success_count += 1
        print("✅ Training configuration created")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Step 5: Create metadata
    print("\n📊 Step 5/5: Creating training metadata...")
    try:
        create_pronunciation_metadata()
        success_count += 1
        print("✅ Training metadata created")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Final results
    print("\n" + "=" * 50)
    print("🎓 EGYPTIAN PRONUNCIATION TRAINING SETUP COMPLETE!")
    print("=" * 50)
    print(f"✅ Completed {success_count}/{total_steps} steps successfully")
    
    if success_count >= 4:
        print("\n🎯 Setup successful! You now have:")
        print("  • Egyptian pronunciation data and patterns")
        print("  • Training texts focused on pronunciation")
        print("  • Generated pronunciation samples")
        print("  • Advanced training configuration")
        print("  • Complete metadata for training")
        
        print("\n🚀 Next steps:")
        print("1. Review the generated pronunciation samples")
        print("2. Run the pronunciation training")
        print("3. Test the trained model")
        print("4. Use in your GUI for correct Egyptian pronunciation!")
        
        print("\n📁 Generated files:")
        print("  • egyptian_pronunciation_data.json - Pronunciation patterns")
        print("  • pronunciation_training/ - Training data")
        print("  • pronunciation_samples/ - Generated samples")
        print("  • pronunciation_training_config.json - Training config")
        
    else:
        print("\n⚠️ Some steps failed. Check the errors above.")
    
    print("\n🎭 This training will teach the model correct Egyptian pronunciation!")

if __name__ == "__main__":
    main()
