@echo off
title Install Training Dependencies
color 0E

echo.
echo     📦 Installing Training Dependencies 🔧
echo     =====================================
echo.
echo     Installing required packages for Egyptian training...
echo.

REM Change to project directory
cd /d "%~dp0"

echo Installing trainer...
C:\Users\<USER>\miniconda3\envs\xtts\python.exe -m pip install trainer

echo Installing audio processing libraries...
C:\Users\<USER>\miniconda3\envs\xtts\python.exe -m pip install librosa soundfile

echo Installing data processing libraries...
C:\Users\<USER>\miniconda3\envs\xtts\python.exe -m pip install pandas scikit-learn

echo.
echo     ✅ Installation complete!
echo.
pause
