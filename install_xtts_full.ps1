# Comprehensive XTTS Installation Script for Windows

# Configuration
$python_url = "https://www.python.org/ftp/python/3.10.11/python-3.10.11-amd64.exe"
$python_installer = "$env:TEMP\python-3.10.11-amd64.exe"
$python_install_dir = "$env:LOCALAPPDATA\Programs\Python\Python310"
$python_exe = "$python_install_dir\python.exe"
$venv_name = "xtts_venv"
$cuda_version = "12.7"  # Updated to match user's CUDA version

# Create log file
$log_file = "xtts_install_log.txt"
Start-Transcript -Path $log_file -Append

# Function to check if a command exists
function Test-CommandExists {
    param ($command)
    $exists = $null -ne (Get-Command $command -ErrorAction SilentlyContinue)
    return $exists
}

# Function to check if Python 3.10 is installed
function Test-Python310Installed {
    if (Test-Path $python_exe) {
        $version = & $python_exe -c "import sys; print('.'.join(map(str, sys.version_info[:3])))"
        if ($version -like "3.10*") {
            return $true
        }
    }
    return $false
}

# Function to check if CUDA is installed
function Test-CudaInstalled {
    # Always return true since user confirmed CUDA is already installed
    return $true
}

# Function to check if GPU is available
function Test-GpuAvailable {
    $gpu_info = Get-WmiObject -Class Win32_VideoController | Where-Object { $_.Name -like "*NVIDIA*" }
    return ($null -ne $gpu_info)
}

# Step 1: Check and install Python 3.10
Write-Host "Step 1: Checking Python 3.10 installation..." -ForegroundColor Cyan
if (-not (Test-Python310Installed)) {
    Write-Host "Python 3.10 not found. Installing Python 3.10..." -ForegroundColor Yellow
    
    # Download Python installer
    Write-Host "Downloading Python 3.10.11..." -ForegroundColor Green
    Invoke-WebRequest -Uri $python_url -OutFile $python_installer
    
    # Install Python
    Write-Host "Installing Python 3.10.11..." -ForegroundColor Green
    Start-Process -FilePath $python_installer -ArgumentList "/quiet", "InstallAllUsers=0", "PrependPath=1", "Include_test=0", "Include_pip=1", "Include_doc=0", "Include_launcher=1", "InstallLauncherAllUsers=0", "CompileAll=1", "TargetDir=$python_install_dir" -Wait
    
    # Clean up installer
    Remove-Item -Path $python_installer -Force
    
    # Verify installation
    if (Test-Python310Installed) {
        Write-Host "Python 3.10.11 installed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Python 3.10.11 installation failed. Please install manually." -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "Python 3.10 is already installed." -ForegroundColor Green
}

# Step 2: Check and install CUDA if GPU is available
Write-Host "\nStep 2: Checking GPU and CUDA installation..." -ForegroundColor Cyan
if (Test-GpuAvailable) {
    Write-Host "NVIDIA GPU detected!" -ForegroundColor Green
    
    if (-not (Test-CudaInstalled)) {
        Write-Host "CUDA $cuda_version not found. Installing CUDA..." -ForegroundColor Yellow
        Write-Host "NOTE: CUDA installation requires admin privileges and may take some time." -ForegroundColor Yellow
        Write-Host "You can also install CUDA manually from: https://developer.nvidia.com/cuda-11-8-0-download-archive" -ForegroundColor Yellow
        
        $install_cuda = Read-Host "Do you want to download and install CUDA $cuda_version now? (y/n)"
        if ($install_cuda -eq "y") {
            # Download CUDA installer
            Write-Host "Downloading CUDA $cuda_version..." -ForegroundColor Green
            Invoke-WebRequest -Uri $cuda_url -OutFile $cuda_installer
            
            # Install CUDA
            Write-Host "Installing CUDA $cuda_version..." -ForegroundColor Green
            Write-Host "Please follow the installation wizard. Select 'Express' installation." -ForegroundColor Yellow
            Start-Process -FilePath $cuda_installer -Wait
            
            # Clean up installer
            Remove-Item -Path $cuda_installer -Force
            
            # Verify installation
            if (Test-CudaInstalled) {
                Write-Host "CUDA $cuda_version installed successfully!" -ForegroundColor Green
            } else {
                Write-Host "CUDA installation may not have completed successfully. Continuing anyway..." -ForegroundColor Yellow
            }
        } else {
            Write-Host "Skipping CUDA installation. XTTS will run on CPU only (slower)." -ForegroundColor Yellow
        }
    } else {
        Write-Host "CUDA $cuda_version is already installed." -ForegroundColor Green
    }
} else {
    Write-Host "No NVIDIA GPU detected. XTTS will run on CPU only (slower)." -ForegroundColor Yellow
}

# Step 3: Create virtual environment
Write-Host "\nStep 3: Creating virtual environment..." -ForegroundColor Cyan
if (-not (Test-Path $venv_name)) {
    & $python_exe -m venv $venv_name
    Write-Host "Virtual environment '$venv_name' created successfully!" -ForegroundColor Green
} else {
    Write-Host "Virtual environment '$venv_name' already exists." -ForegroundColor Green
}

# Step 4: Activate virtual environment and install dependencies
Write-Host "\nStep 4: Installing dependencies..." -ForegroundColor Cyan
. ".\$venv_name\Scripts\Activate.ps1"

# Upgrade pip
pip install --upgrade pip

# Install PyTorch with CUDA if available
if (Test-GpuAvailable -and (Test-CudaInstalled)) {
    Write-Host "Installing PyTorch with CUDA support..." -ForegroundColor Green
    pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu117
    # Using cu117 which is compatible with CUDA 12.7 for PyTorch 2.0.1
} else {
    Write-Host "Installing PyTorch without CUDA support..." -ForegroundColor Green
    pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu
}

# Install other dependencies
Write-Host "Installing other dependencies..." -ForegroundColor Green
pip install numpy==1.24.3 soundfile==0.12.1 librosa==0.10.1 pygame==2.5.2

# Install TTS from GitHub
Write-Host "Installing TTS from GitHub..." -ForegroundColor Green
pip install git+https://github.com/coqui-ai/TTS.git@v0.13.0

# Step 5: Create necessary directories
Write-Host "\nStep 5: Creating project directories..." -ForegroundColor Cyan
New-Item -ItemType Directory -Force -Path .\models
New-Item -ItemType Directory -Force -Path .\samples
New-Item -ItemType Directory -Force -Path .\output

# Step 6: Download a sample voice file if none exists
Write-Host "\nStep 6: Downloading sample voice file..." -ForegroundColor Cyan
if (-not (Test-Path .\samples\sample_voice.wav)) {
    Write-Host "Downloading a sample voice file..." -ForegroundColor Green
    Invoke-WebRequest -Uri "https://github.com/coqui-ai/TTS/raw/dev/tests/inputs/ljspeech/LJ001-0001.wav" -OutFile ".\samples\sample_voice.wav"
    Write-Host "Sample voice file downloaded successfully!" -ForegroundColor Green
} else {
    Write-Host "Sample voice file already exists." -ForegroundColor Green
}

# Step 7: Create a test script
Write-Host "\nStep 7: Creating test script..." -ForegroundColor Cyan
$test_script = @"
import os
import time
import sys

print("Python version:", sys.version)

try:
    from TTS.api import TTS
    print("TTS package imported successfully!")
except ImportError as e:
    print(f"Error importing TTS: {e}")
    sys.exit(1)

print("\nChecking available models...")
try:
    # List available models
    print(TTS().list_models())
    print("Models listed successfully!")
except Exception as e:
    print(f"Error listing models: {e}")

print("\nInitializing XTTS model...")
try:
    # Initialize TTS with XTTS model
    start_time = time.time()
    tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=True)
    load_time = time.time() - start_time
    print(f"Model loaded in {load_time:.2f} seconds")

    # List available languages
    print("\nAvailable languages:")
    print(tts.languages)

    # Generate speech
    print("\nGenerating English speech...")
    start_time = time.time()
    
    os.makedirs("output", exist_ok=True)
    tts.tts_to_file(
        text="Hello, this is a test of the XTTS text to speech system running on Windows.",
        speaker_wav="samples/sample_voice.wav",
        language="en",
        file_path="output/test_output_en.wav"
    )
    
    generation_time = time.time() - start_time
    print(f"Speech generated in {generation_time:.2f} seconds")
    print("Output saved to output/test_output_en.wav")
    
    # Generate Arabic speech
    print("\nGenerating Arabic speech...")
    start_time = time.time()
    
    tts.tts_to_file(
        text="مرحبا، هذا اختبار لنظام تحويل النص إلى كلام باستخدام إكس تي تي إس.",
        speaker_wav="samples/sample_voice.wav",
        language="ar",
        file_path="output/test_output_ar.wav"
    )
    
    generation_time = time.time() - start_time
    print(f"Arabic speech generated in {generation_time:.2f} seconds")
    print("Output saved to output/test_output_ar.wav")
    
    print("\nTest completed successfully!")
except Exception as e:
    print(f"Error during test: {e}")
"@

Set-Content -Path .\test_xtts.py -Value $test_script
Write-Host "Test script created successfully!" -ForegroundColor Green

# Step 8: Create a batch file to run the GUI
Write-Host "\nStep 8: Creating launcher batch file..." -ForegroundColor Cyan
$batch_script = @"
@echo off
echo Starting XTTS Voice Generator...

:: Activate virtual environment
echo Activating virtual environment...
call $venv_name\Scripts\activate.bat

:: Create necessary directories
if not exist samples mkdir samples
if not exist output mkdir output
if not exist models mkdir models

echo Starting XTTS GUI...
python xtts_gui.py

:: Deactivate virtual environment when done
call $venv_name\Scripts\deactivate.bat
"@

Set-Content -Path .\run_xtts_gui.bat -Value $batch_script
Write-Host "Launcher batch file created successfully!" -ForegroundColor Green

# Step 9: Finish installation
Write-Host "\nStep 9: Installation complete!" -ForegroundColor Cyan
Write-Host "
XTTS has been installed successfully! To test XTTS:

1. Run the test script to verify the installation:
   .\$venv_name\Scripts\activate.ps1
   python test_xtts.py

2. To use the GUI application:
   Double-click run_xtts_gui.bat

Note: The first run will download the XTTS model (about 5GB).
" -ForegroundColor Green

Stop-Transcript
