import os
import sys
import time
import torch
import platform
import subprocess

def log_system_info():
    print("\n=== System Information ===")
    print(f"Python version: {sys.version}")
    print(f"Platform: {platform.platform()}")
    print(f"Architecture: {platform.machine()}")
    print(f"CPU count: {os.cpu_count()}")
    
    # Virtual environment check
    print(f"\nVirtual env: {'VIRTUAL_ENV' in os.environ}")
    if 'VIRTUAL_ENV' in os.environ:
        print(f"Virtual env path: {os.environ['VIRTUAL_ENV']}")

def log_cuda_info():
    print("\n=== CUDA Information ===")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"CUDA version: {torch.version.cuda}")
        print(f"PyTorch CUDA version: {torch.backends.cudnn.version()}")
        print(f"PyTorch CUDA build version: {torch.version.cuda_version}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
        
        # Detailed CUDA test operations
        try:
            # Basic tensor operation
            x = torch.randn(100).cuda()
            y = x * 2
            print("\nCUDA Tests:")
            print("1. Basic tensor operation: Success")
            
            # Memory transfer test
            cpu_tensor = torch.randn(1000, 1000)
            gpu_tensor = cpu_tensor.cuda()
            print("2. Memory transfer test: Success")
            
            # Compute capability
            compute_capability = torch.cuda.get_device_capability(0)
            print(f"3. GPU Compute Capability: {compute_capability[0]}.{compute_capability[1]}")
            
            # Current memory usage
            print(f"4. Current GPU Memory Usage: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
            print(f"5. Max GPU Memory Usage: {torch.cuda.max_memory_allocated() / 1024**2:.1f} MB")
            
        except Exception as e:
            print(f"CUDA test operation failed: {e}")
            print("Please check CUDA and PyTorch compatibility")

def log_dependencies():
    print("\n=== Dependencies ===")
    packages = ['TTS', 'torch', 'torchaudio', 'numpy', 'soundfile', 'librosa', 'pygame']
    for package in packages:
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            print(f"{package}: {version}")
        except ImportError:
            print(f"{package}: Not installed")

# Run diagnostics
print("=== XTTS Diagnostics ===")
log_system_info()
log_cuda_info()
log_dependencies()

# Create directories if they don't exist
os.makedirs('samples', exist_ok=True)
os.makedirs('output', exist_ok=True)

# Download a sample voice file if it doesn't exist
sample_path = 'samples/sample_voice.wav'
if not os.path.exists(sample_path):
    print("Downloading sample voice file...")
    import urllib.request
    urllib.request.urlretrieve(
        "https://raw.githubusercontent.com/coqui-ai/TTS/main/tests/inputs/ljspeech/LJ001-0001.wav",
        sample_path
    )
    print(f"Sample voice file downloaded to {sample_path}")
    
    # If the above URL fails, try an alternative source
    if not os.path.exists(sample_path) or os.path.getsize(sample_path) == 0:
        print("First download failed, trying alternative source...")
        try:
            urllib.request.urlretrieve(
                "https://huggingface.co/datasets/libriheavy/test-files/resolve/main/LJ001-0001.wav",
                sample_path
            )
            print(f"Sample voice file downloaded from alternative source to {sample_path}")
        except Exception as e:
            print(f"Failed to download sample file: {e}")
            print("Please download a WAV file manually and place it in the 'samples' folder as 'sample_voice.wav'")

# Try importing TTS
try:
    print("\nImporting TTS...")
    from TTS.api import TTS
    print("TTS imported successfully!")
    
    # List available models
    print("\nListing available models...")
    tts = TTS()
    print("Available models:")
    for model in tts.list_models():
        if "xtts" in model.lower():
            print(f" - {model} (XTTS model)")
        else:
            print(f" - {model}")
    
    # Load XTTS model
    print("\nLoading XTTS model...")
    start_time = time.time()
    model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
    tts = TTS(model_name, gpu=True)
    load_time = time.time() - start_time
    print(f"Model loaded in {load_time:.2f} seconds")
    
    # Generate speech
    print("\nGenerating speech...")
    start_time = time.time()
    output_path = "output/test_output.wav"
    
    tts.tts_to_file(
        text="Hello, this is a test of the XTTS text to speech system running on Windows with CUDA acceleration.",
        speaker_wav=sample_path,
        language="en",
        file_path=output_path
    )
    
    generation_time = time.time() - start_time
    print(f"Speech generated in {generation_time:.2f} seconds")
    print(f"Output saved to {output_path}")
    
    # Generate Arabic speech if requested
    generate_arabic = input("\nGenerate Arabic speech sample? (y/n): ").lower() == 'y'
    if generate_arabic:
        print("Generating Arabic speech...")
        start_time = time.time()
        arabic_output_path = "output/arabic_output.wav"
        
        tts.tts_to_file(
            text="مرحبا، هذا اختبار لنظام تحويل النص إلى كلام باستخدام إكس تي تي إس.",
            speaker_wav=sample_path,
            language="ar",
            file_path=arabic_output_path
        )
        
        generation_time = time.time() - start_time
        print(f"Arabic speech generated in {generation_time:.2f} seconds")
        print(f"Output saved to {arabic_output_path}")
    
    print("\n=== Test Results ===")
    print("Model loading time:", f"{load_time:.2f} seconds")
    print("Speech generation time:", f"{generation_time:.2f} seconds")
    print("Memory usage after test:", f"{torch.cuda.memory_allocated() / 1024**3:.2f} GB")
    print("\nTest completed successfully!")
    
except ImportError as e:
    print(f"Error importing TTS: {e}")
    print("\nTrying to install TTS...")
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "TTS"])
    print("\nPlease run this script again after TTS installation completes.")
    
except Exception as e:
    print(f"\nError: {e}")
