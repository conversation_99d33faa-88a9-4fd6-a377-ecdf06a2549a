#!/usr/bin/env python3
"""
Google Cloud Speech API Setup for Egyptian TTS
Setup guide and configuration for Google Cloud integration
"""

import os
import json

def create_google_cloud_setup_guide():
    """Create setup guide for Google Cloud Speech API"""
    
    guide_content = """# Google Cloud Speech API Setup Guide

## 🚀 Quick Setup for Egyptian TTS

### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing project
3. Enable the Speech-to-Text API

### Step 2: Create Service Account
1. Go to IAM & Admin > Service Accounts
2. Click "Create Service Account"
3. Name: "egyptian-tts-speech-api"
4. Grant role: "Cloud Speech Client"
5. Create and download JSON key file

### Step 3: Setup Credentials
1. Rename downloaded file to: `google_cloud_credentials.json`
2. Place in your Egyptian TTS project folder
3. The advanced AI system will automatically detect it

### Step 4: Enable Egyptian Arabic
Google Cloud Speech API supports:
- `ar-EG` - Egyptian Arabic (primary)
- `ar-SA` - Saudi Arabic (fallback)
- `ar` - Standard Arabic (fallback)

## 🎯 Features You'll Get

### Advanced Quality Analysis
- **Speech Recognition Confidence**: 0-100% accuracy
- **Text Accuracy Comparison**: Original vs recognized text
- **Word-level Analysis**: Individual word confidence scores
- **Egyptian Dialect Detection**: Identifies Egyptian expressions

### Pronunciation Assessment
- **Character Accuracy**: ج, ق, ث, ذ pronunciation scoring
- **Accent Authenticity**: Egyptian vs Standard Arabic detection
- **Clarity Scoring**: Speech clarity and intelligibility
- **Improvement Recommendations**: AI-generated suggestions

### Real-time Feedback
- **Live Quality Monitoring**: Continuous assessment
- **Performance Tracking**: Historical quality trends
- **Optimization Alerts**: Automatic improvement suggestions

## 💰 Pricing (as of 2024)
- First 60 minutes per month: **FREE**
- Additional usage: $0.006 per 15 seconds
- Perfect for Egyptian TTS development and testing

## 🔧 Configuration Options

### Basic Configuration (Automatic)
```json
{
    "encoding": "LINEAR16",
    "sample_rate_hertz": 22050,
    "language_code": "ar-EG"
}
```

### Advanced Configuration (Custom)
```json
{
    "encoding": "LINEAR16", 
    "sample_rate_hertz": 22050,
    "language_code": "ar-EG",
    "alternative_language_codes": ["ar-SA", "ar"],
    "enable_automatic_punctuation": true,
    "enable_word_confidence": true,
    "enable_word_time_offsets": true,
    "model": "latest_long"
}
```

## 🎭 Egyptian Arabic Optimization

### Supported Egyptian Features
- **Dialect Recognition**: يلا، خلاص، معلش، قوي
- **Pronunciation Patterns**: ج→g, ق→glottal stop
- **Regional Variations**: Cairo, Alexandria dialects
- **Code-switching**: Arabic-English mixing

### Quality Metrics
- **Confidence Score**: Google's recognition confidence
- **Dialect Score**: Egyptian authenticity percentage  
- **Accuracy Score**: Text matching accuracy
- **Clarity Score**: Pronunciation clarity rating

## 🚀 Integration with Your TTS

### Automatic Integration
The advanced AI system automatically:
1. Detects Google Cloud credentials
2. Configures Egyptian Arabic settings
3. Runs parallel analysis with neural networks
4. Combines results for comprehensive assessment

### Manual Testing
```python
# Test Google Cloud integration
from advanced_ai_system import AdvancedEgyptianAI

ai = AdvancedEgyptianAI()
results = ai.google_cloud_quality_comparison(
    "your_audio.wav", 
    "جميل قوي!"
)
print(f"Confidence: {results['overall_confidence']:.1f}%")
```

## 🎉 Benefits for Egyptian TTS

### Quality Assurance
- **Objective Scoring**: Industry-standard speech recognition
- **Comparative Analysis**: Multiple AI systems validation
- **Continuous Improvement**: Data-driven optimization

### Development Acceleration  
- **Rapid Testing**: Instant quality feedback
- **Automated Assessment**: No manual evaluation needed
- **Performance Tracking**: Historical improvement monitoring

### Professional Results
- **Enterprise-grade Analysis**: Google's advanced AI
- **Multi-language Support**: Arabic dialect expertise
- **Scalable Solution**: From development to production

## 🔒 Privacy & Security

### Data Handling
- Audio processed in Google Cloud (encrypted)
- No permanent storage by default
- GDPR and privacy compliant
- Can be configured for on-premises processing

### Best Practices
- Use service account (not personal account)
- Limit API key permissions
- Monitor usage and costs
- Regular credential rotation

## 🆘 Troubleshooting

### Common Issues
1. **"Credentials not found"**
   - Check `google_cloud_credentials.json` exists
   - Verify file permissions
   - Ensure correct project ID

2. **"API not enabled"**
   - Enable Speech-to-Text API in console
   - Wait 5-10 minutes for activation
   - Check billing account is active

3. **"Audio format error"**
   - Ensure WAV format, 22050 Hz
   - Check file size < 10MB
   - Verify audio duration < 60 seconds

### Support Resources
- [Google Cloud Speech Documentation](https://cloud.google.com/speech-to-text/docs)
- [Arabic Language Support](https://cloud.google.com/speech-to-text/docs/languages)
- [Pricing Calculator](https://cloud.google.com/products/calculator)

## 🎯 Next Steps

1. **Setup Google Cloud** (5 minutes)
2. **Download credentials** (2 minutes)  
3. **Run advanced AI analysis** (automatic)
4. **Enjoy enhanced Egyptian TTS** (immediate)

Your Egyptian TTS will have professional-grade quality analysis powered by Google's advanced AI!
"""
    
    with open("GOOGLE_CLOUD_SETUP_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ Created: GOOGLE_CLOUD_SETUP_GUIDE.md")
    return True

def create_sample_credentials_template():
    """Create sample credentials template"""
    
    template = ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    
    with open("google_cloud_credentials_template.json", "w", encoding="utf-8") as f:
        json.dump(template, f, indent=2)
    
    print("✅ Created: google_cloud_credentials_template.json")
    print("💡 Replace with your actual Google Cloud credentials")
    return True

def check_google_cloud_setup():
    """Check if Google Cloud is properly setup"""
    print("🔍 Checking Google Cloud Setup")
    print("=" * 30)
    
    setup_status = {
        "credentials_file": False,
        "speech_library": False,
        "project_configured": False,
        "ready_for_use": False
    }
    
    # Check credentials file
    if os.path.exists("google_cloud_credentials.json"):
        setup_status["credentials_file"] = True
        print("✅ Credentials file found")
        
        # Check if it's a valid JSON
        try:
            with open("google_cloud_credentials.json", "r") as f:
                creds = json.load(f)
                if "project_id" in creds and "private_key" in creds:
                    setup_status["project_configured"] = True
                    print(f"✅ Project configured: {creds.get('project_id', 'Unknown')}")
                else:
                    print("⚠️ Credentials file incomplete")
        except:
            print("❌ Invalid credentials file format")
    else:
        print("❌ Credentials file not found")
        print("💡 Create google_cloud_credentials.json with your service account key")
    
    # Check Google Cloud Speech library
    try:
        import google.cloud.speech
        setup_status["speech_library"] = True
        print("✅ Google Cloud Speech library available")
    except ImportError:
        print("❌ Google Cloud Speech library not installed")
        print("💡 Run: pip install google-cloud-speech")
    
    # Overall readiness
    if all([setup_status["credentials_file"], setup_status["speech_library"], setup_status["project_configured"]]):
        setup_status["ready_for_use"] = True
        print("\n🎉 Google Cloud Speech API ready for use!")
    else:
        print("\n⚠️ Google Cloud setup incomplete")
        print("📖 Check GOOGLE_CLOUD_SETUP_GUIDE.md for instructions")
    
    return setup_status

def install_google_cloud_dependencies():
    """Install Google Cloud dependencies"""
    print("📦 Installing Google Cloud Dependencies")
    print("=" * 40)
    
    import subprocess
    import sys
    
    dependencies = [
        "google-cloud-speech>=2.16.0",
        "google-auth>=2.0.0",
        "google-api-core>=2.0.0"
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ {dep} installed successfully")
            else:
                print(f"⚠️ {dep} installation warning")
        except Exception as e:
            print(f"❌ Error installing {dep}: {str(e)}")
    
    print("✅ Google Cloud dependencies installation complete")

def main():
    """Main Google Cloud setup function"""
    print("☁️ Google Cloud Speech API Setup for Egyptian TTS")
    print("=" * 50)
    
    # Create setup guide
    print("📖 Creating setup guide...")
    create_google_cloud_setup_guide()
    
    # Create credentials template
    print("\n📄 Creating credentials template...")
    create_sample_credentials_template()
    
    # Install dependencies
    print("\n📦 Installing dependencies...")
    install_google_cloud_dependencies()
    
    # Check current setup
    print("\n🔍 Checking current setup...")
    setup_status = check_google_cloud_setup()
    
    print("\n" + "=" * 50)
    print("☁️ GOOGLE CLOUD SETUP COMPLETE!")
    print("=" * 50)
    
    if setup_status["ready_for_use"]:
        print("🎉 Google Cloud Speech API is ready!")
        print("🚀 Your Egyptian TTS now has enterprise-grade quality analysis!")
    else:
        print("📋 Next steps:")
        if not setup_status["credentials_file"]:
            print("  1. Create Google Cloud project and service account")
            print("  2. Download credentials as google_cloud_credentials.json")
        if not setup_status["speech_library"]:
            print("  3. Install Google Cloud Speech library")
        print("  4. Run advanced AI analysis with Google Cloud integration")
    
    print("\n📖 Read GOOGLE_CLOUD_SETUP_GUIDE.md for detailed instructions")
    print("🤖 Advanced AI system will automatically use Google Cloud when available")

if __name__ == "__main__":
    main()
