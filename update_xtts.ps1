# XTTS Update Script for Windows
# May 2025

# Configuration
$venv_name = "xtts_venv_310"
$python_exe = ".\$venv_name\Scripts\python.exe"

# Create log file
$log_file = "xtts_update_log.txt"
Start-Transcript -Path $log_file -Append

Write-Host "=== XTTS Update Script ===" -ForegroundColor Cyan
Write-Host "This script will update your XTTS installation to the latest version." -ForegroundColor Cyan

# Check if virtual environment exists
if (-not (Test-Path $venv_name)) {
    Write-Host "Error: Virtual environment '$venv_name' not found!" -ForegroundColor Red
    Write-Host "Please run the install_python310.ps1 script first to set up the Python 3.10 environment." -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "Found virtual environment: $venv_name" -ForegroundColor Green
}

# Check Python version in virtual environment
Write-Host "\nChecking Python version in virtual environment..." -ForegroundColor Cyan
$python_version = & $python_exe -c "import sys; print('.'.join(map(str, sys.version_info[:3])))"
Write-Host "Python version in virtual environment: $python_version" -ForegroundColor Green

if ($python_version -notlike "3.10*") {
    Write-Host "Warning: XTTS works best with Python 3.10. You are using $python_version" -ForegroundColor Yellow
    $continue = Read-Host "Continue anyway? (y/n)"
    if ($continue -ne "y") {
        exit 1
    }
}

# Activate virtual environment and update packages
Write-Host "\nUpdating pip and setuptools..." -ForegroundColor Cyan
& $python_exe -m pip install --upgrade pip setuptools

# Check for CUDA
Write-Host "\nChecking for CUDA support..." -ForegroundColor Cyan
$cuda_check_script = @"
import sys
try:
    import torch
    print(torch.cuda.is_available())
    sys.exit(0)
except Exception:
    print(False)
    sys.exit(1)
"@

$cuda_script_path = "cuda_check_temp.py"
Set-Content -Path $cuda_script_path -Value $cuda_check_script

$cuda_available = & $python_exe $cuda_script_path
Remove-Item -Path $cuda_script_path -Force

if ($cuda_available -eq "True") {
    Write-Host "CUDA is available! Installing PyTorch with CUDA support..." -ForegroundColor Green
    & $python_exe -m pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118
} else {
    Write-Host "CUDA is not available. Installing PyTorch CPU version..." -ForegroundColor Yellow
    & $python_exe -m pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu
}

# Install other dependencies
Write-Host "\nInstalling/updating dependencies..." -ForegroundColor Cyan
& $python_exe -m pip install numpy==1.24.3 soundfile==0.12.1 librosa==0.10.1 pygame==2.5.2

# Install coqui-tts (the updated version of TTS)
Write-Host "\nInstalling latest coqui-tts package..." -ForegroundColor Cyan
& $python_exe -m pip install coqui-tts>=0.26.1

# Create necessary directories
Write-Host "\nEnsuring project directories exist..." -ForegroundColor Cyan
New-Item -ItemType Directory -Force -Path .\models
New-Item -ItemType Directory -Force -Path .\samples
New-Item -ItemType Directory -Force -Path .\output

# Update the test script for the new package name
Write-Host "\nUpdating test script for coqui-tts..." -ForegroundColor Cyan
$test_script = @"
import os
import time
import sys
import torch

print("=== XTTS Test Script ===")
print("Python version:", sys.version)

try:
    # Import from coqui-tts package
    from TTS.api import TTS
    print("\ncoqui-tts package imported successfully!")
except ImportError as e:
    print(f"Error importing TTS: {e}")
    sys.exit(1)

print("\nTorch version:", torch.__version__)
print("CUDA available:", torch.cuda.is_available())
if torch.cuda.is_available():
    print("CUDA device:", torch.cuda.get_device_name(0))

print("\nChecking available models...")
try:
    # List available models
    tts = TTS()
    print("\nAvailable models:")
    for model in tts.list_models():
        if "xtts" in model.lower():
            print(f" - {model} (XTTS model)")
        else:
            print(f" - {model}")
    print("\nModels listed successfully!")
except Exception as e:
    print(f"Error listing models: {e}")
    sys.exit(1)

print("\nInitializing XTTS model...")
try:
    # Initialize TTS with XTTS model
    start_time = time.time()
    model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
    tts = TTS(model_name, gpu=torch.cuda.is_available())
    load_time = time.time() - start_time
    print(f"Model loaded in {load_time:.2f} seconds")

    # List available languages
    print("\nAvailable languages:")
    print(tts.languages)

    # Ask if user wants to generate speech
    generate = input("\nGenerate test speech? (y/n): ").lower() == 'y'
    if generate:
        # Generate speech
        print("\nGenerating English speech...")
        start_time = time.time()
        
        os.makedirs("output", exist_ok=True)
        tts.tts_to_file(
            text="Hello, this is a test of the XTTS text to speech system running on Windows.",
            speaker_wav="samples/sample_voice.wav",
            language="en",
            file_path="output/test_output_en.wav"
        )
        
        generation_time = time.time() - start_time
        print(f"Speech generated in {generation_time:.2f} seconds")
        print("Output saved to output/test_output_en.wav")
        
        # Generate Arabic speech
        print("\nGenerating Arabic speech...")
        start_time = time.time()
        
        tts.tts_to_file(
            text="مرحبا، هذا اختبار لنظام تحويل النص إلى كلام باستخدام إكس تي تي إس.",
            speaker_wav="samples/sample_voice.wav",
            language="ar",
            file_path="output/test_output_ar.wav"
        )
        
        generation_time = time.time() - start_time
        print(f"Arabic speech generated in {generation_time:.2f} seconds")
        print("Output saved to output/test_output_ar.wav")

    print("\nTest completed successfully!")
except Exception as e:
    print(f"Error during test: {e}")
"@

Set-Content -Path .\test_xtts_updated.py -Value $test_script
Write-Host "Updated test script created as test_xtts_updated.py" -ForegroundColor Green

# Create a batch file to run the updated GUI with the new environment
Write-Host "\nCreating updated launcher batch file..." -ForegroundColor Cyan
$batch_script = @"
@echo off
echo Starting XTTS Voice Generator (Updated May 2025)...

:: Activate virtual environment
echo Activating Python 3.10 virtual environment...
call $venv_name\Scripts\activate.bat

:: Create necessary directories
if not exist samples mkdir samples
if not exist output mkdir output
if not exist models mkdir models

echo Starting XTTS GUI...
python xtts_gui.py

:: Deactivate virtual environment when done
call $venv_name\Scripts\deactivate.bat
"@

Set-Content -Path .\run_xtts_updated.bat -Value $batch_script
Write-Host "Updated launcher batch file created as run_xtts_updated.bat" -ForegroundColor Green

# Finish update
Write-Host "\n=== Update complete! ===" -ForegroundColor Cyan
Write-Host "
XTTS has been updated successfully! To test the updated installation:

1. Run the test script to verify the installation:
   .\$venv_name\Scripts\activate.ps1
   python test_xtts_updated.py

2. To use the GUI application with the updated packages:
   Double-click run_xtts_updated.bat

Note: The first run will download the XTTS model (about 5GB) if it's not already downloaded.
" -ForegroundColor Green

Stop-Transcript
