2025-05-25 11:47:31,968 - ERROR - Fatal error: bad option "-width": must be -after, -anchor, -before, -expand, -fill, -in, -ipadx, -ipady, -padx, -pady, or -side
2025-05-25 11:48:41,714 - INFO - GUI initialization complete
2025-05-25 11:48:42,933 - INFO - Pygame mixer initialized successfully
2025-05-25 11:48:42,934 - INFO - Starting XTTS GUI application
2025-05-25 11:50:08,226 - ERROR - FFmpeg not found in PATH
2025-05-25 11:50:09,958 - INFO - Starting FFmpeg installation...
2025-05-25 11:50:09,983 - INFO - Downloading FFmpeg...
2025-05-25 11:50:58,527 - INFO - Extracting FFmpeg...
2025-05-25 11:51:14,871 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 11:51:15,443 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:15,443 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:15,445 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:15,445 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:15,446 - INFO - FFmpeg installed successfully
2025-05-25 11:51:19,734 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:19,735 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:19,735 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:19,735 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:21,480 - INFO - Progress update: Checking model cache...
2025-05-25 11:51:21,639 - INFO - Starting model loading process
2025-05-25 11:51:21,664 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 11:51:21,764 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:21,764 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:21,764 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:21,764 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:38,674 - INFO - Attempting to load XTTS model
2025-05-25 11:51:38,674 - INFO - CUDA not available - using CPU
2025-05-25 11:51:38,677 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
