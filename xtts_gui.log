2025-05-25 11:47:31,968 - ERROR - Fatal error: bad option "-width": must be -after, -anchor, -before, -expand, -fill, -in, -ipadx, -ipady, -padx, -pady, or -side
2025-05-25 11:48:41,714 - INFO - GUI initialization complete
2025-05-25 11:48:42,933 - INFO - Pygame mixer initialized successfully
2025-05-25 11:48:42,934 - INFO - Starting XTTS GUI application
2025-05-25 11:50:08,226 - ERROR - FFmpeg not found in PATH
2025-05-25 11:50:09,958 - INFO - Starting FFmpeg installation...
2025-05-25 11:50:09,983 - INFO - Downloading FFmpeg...
2025-05-25 11:50:58,527 - INFO - Extracting FFmpeg...
2025-05-25 11:51:14,871 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 11:51:15,443 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:15,443 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:15,445 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:15,445 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:15,446 - INFO - FFmpeg installed successfully
2025-05-25 11:51:19,734 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:19,735 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:19,735 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:19,735 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:21,480 - INFO - Progress update: Checking model cache...
2025-05-25 11:51:21,639 - INFO - Starting model loading process
2025-05-25 11:51:21,664 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 11:51:21,764 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:21,764 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:21,764 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:21,764 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:38,674 - INFO - Attempting to load XTTS model
2025-05-25 11:51:38,674 - INFO - CUDA not available - using CPU
2025-05-25 11:51:38,677 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:01:04,526 - INFO - GUI initialization complete
2025-05-25 12:01:04,529 - ERROR - Failed to initialize Pygame mixer: No module named 'pygame'
2025-05-25 12:01:04,529 - INFO - Starting XTTS GUI application
2025-05-25 12:01:07,642 - ERROR - FFmpeg not found in PATH
2025-05-25 12:01:10,021 - INFO - Starting FFmpeg installation...
2025-05-25 12:01:10,022 - INFO - Downloading FFmpeg...
2025-05-25 12:01:54,155 - INFO - Extracting FFmpeg...
2025-05-25 12:02:08,410 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:02:09,343 - INFO - FFmpeg check return code: 0
2025-05-25 12:02:09,343 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:02:09,343 - INFO - FFmpeg check stderr: 
2025-05-25 12:02:09,344 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:02:09,344 - INFO - FFmpeg installed successfully
2025-05-25 12:02:11,454 - INFO - FFmpeg check return code: 0
2025-05-25 12:02:11,455 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:02:11,455 - INFO - FFmpeg check stderr: 
2025-05-25 12:02:11,455 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:02:12,864 - INFO - Progress update: Checking model cache...
2025-05-25 12:02:12,892 - INFO - Starting model loading process
2025-05-25 12:02:12,892 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:02:12,921 - INFO - FFmpeg check return code: 0
2025-05-25 12:02:12,922 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:02:12,922 - INFO - FFmpeg check stderr: 
2025-05-25 12:02:12,922 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:02:12,924 - ERROR - Model loading error: No module named 'TTS'
2025-05-25 12:02:24,161 - INFO - Application closing
2025-05-25 12:02:27,820 - INFO - GUI initialization complete
2025-05-25 12:02:27,822 - ERROR - Failed to initialize Pygame mixer: No module named 'pygame'
2025-05-25 12:02:27,822 - INFO - Starting XTTS GUI application
2025-05-25 12:02:30,358 - ERROR - FFmpeg not found in PATH
2025-05-25 12:02:31,953 - INFO - Starting FFmpeg installation...
2025-05-25 12:02:31,954 - INFO - Downloading FFmpeg...
2025-05-25 12:04:35,736 - INFO - GUI initialization complete
2025-05-25 12:04:35,737 - ERROR - Failed to initialize Pygame mixer: No module named 'pygame'
2025-05-25 12:04:35,738 - INFO - Starting XTTS GUI application
2025-05-25 12:04:37,898 - ERROR - FFmpeg not found in PATH
2025-05-25 12:04:39,906 - INFO - Starting FFmpeg installation...
2025-05-25 12:04:39,908 - INFO - Downloading FFmpeg...
2025-05-25 12:05:24,361 - INFO - Extracting FFmpeg...
2025-05-25 12:05:26,983 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:05:27,465 - INFO - FFmpeg check return code: 0
2025-05-25 12:05:27,465 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:05:27,466 - INFO - FFmpeg check stderr: 
2025-05-25 12:05:27,466 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:05:27,466 - INFO - FFmpeg installed successfully
2025-05-25 12:05:31,079 - INFO - FFmpeg check return code: 0
2025-05-25 12:05:31,080 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:05:31,080 - INFO - FFmpeg check stderr: 
2025-05-25 12:05:31,080 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:05:33,815 - INFO - Progress update: Checking model cache...
2025-05-25 12:05:33,824 - INFO - Starting model loading process
2025-05-25 12:05:33,825 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:05:33,852 - INFO - FFmpeg check return code: 0
2025-05-25 12:05:33,853 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:05:33,853 - INFO - FFmpeg check stderr: 
2025-05-25 12:05:33,853 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:05:33,854 - ERROR - Model loading error: No module named 'TTS'
2025-05-25 12:12:41,055 - INFO - Application closing
2025-05-25 12:15:25,345 - INFO - GUI initialization complete
2025-05-25 12:15:25,347 - ERROR - Failed to initialize Pygame mixer: No module named 'pygame'
2025-05-25 12:15:25,347 - INFO - Starting XTTS GUI application
2025-05-25 12:15:27,629 - ERROR - FFmpeg not found in PATH
2025-05-25 12:15:29,961 - INFO - Starting FFmpeg installation...
2025-05-25 12:15:29,961 - INFO - Downloading FFmpeg...
2025-05-25 12:16:14,111 - INFO - Extracting FFmpeg...
2025-05-25 12:16:17,099 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:16:17,415 - INFO - FFmpeg check return code: 0
2025-05-25 12:16:17,416 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:16:17,416 - INFO - FFmpeg check stderr: 
2025-05-25 12:16:17,416 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:16:17,416 - INFO - FFmpeg installed successfully
2025-05-25 12:16:19,108 - INFO - FFmpeg check return code: 0
2025-05-25 12:16:19,109 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:16:19,109 - INFO - FFmpeg check stderr: 
2025-05-25 12:16:19,109 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:16:20,745 - INFO - Progress update: Checking model cache...
2025-05-25 12:16:20,758 - INFO - Starting model loading process
2025-05-25 12:16:20,759 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:16:20,792 - INFO - FFmpeg check return code: 0
2025-05-25 12:16:20,792 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:16:20,792 - INFO - FFmpeg check stderr: 
2025-05-25 12:16:20,793 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:16:20,793 - ERROR - Model loading error: No module named 'TTS'
2025-05-25 12:16:23,857 - INFO - Application closing
2025-05-25 12:17:25,423 - INFO - GUI initialization complete
2025-05-25 12:17:26,105 - INFO - Pygame mixer initialized successfully
2025-05-25 12:17:26,105 - INFO - Starting XTTS GUI application
2025-05-25 12:17:30,098 - ERROR - FFmpeg not found in PATH
2025-05-25 12:17:30,098 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:17:30,124 - INFO - FFmpeg check return code: 0
2025-05-25 12:17:30,124 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:17:30,124 - INFO - FFmpeg check stderr: 
2025-05-25 12:17:30,124 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:17:31,608 - INFO - Progress update: Checking model cache...
2025-05-25 12:17:31,622 - INFO - Starting model loading process
2025-05-25 12:17:31,623 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:17:31,664 - INFO - FFmpeg check return code: 0
2025-05-25 12:17:31,664 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:17:31,664 - INFO - FFmpeg check stderr: 
2025-05-25 12:17:31,664 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:17:42,231 - INFO - Attempting to load XTTS model
2025-05-25 12:17:42,231 - INFO - CUDA not available - using CPU
2025-05-25 12:17:42,234 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:18:08,997 - INFO - XTTS model loaded successfully
2025-05-25 12:18:13,910 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 12:18:31,364 - INFO - Starting speech generation
2025-05-25 12:18:31,368 - INFO - Generating speech to file: output\output_1748164711.wav
2025-05-25 12:18:31,375 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:18:31,377 - WARNING - Advanced generation failed, falling back to standard: 'TTS' object has no attribute 'model'
2025-05-25 12:18:31,391 - ERROR - Generation error: generator didn't stop after throw()
2025-05-25 12:19:11,926 - INFO - Starting speech generation
2025-05-25 12:19:11,927 - INFO - Generating speech to file: output\output_1748164751.wav
2025-05-25 12:19:11,936 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:19:11,938 - WARNING - Advanced generation failed, falling back to standard: 'TTS' object has no attribute 'model'
2025-05-25 12:19:11,939 - ERROR - Generation error: generator didn't stop after throw()
2025-05-25 12:19:50,638 - INFO - GUI initialization complete
2025-05-25 12:19:52,023 - INFO - Pygame mixer initialized successfully
2025-05-25 12:19:52,023 - INFO - Starting XTTS GUI application
2025-05-25 12:19:55,578 - INFO - Application closing
2025-05-25 12:20:02,376 - ERROR - FFmpeg not found in PATH
2025-05-25 12:20:02,376 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:20:02,421 - INFO - FFmpeg check return code: 0
2025-05-25 12:20:02,421 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:20:02,421 - INFO - FFmpeg check stderr: 
2025-05-25 12:20:02,421 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:20:04,034 - INFO - Progress update: Checking model cache...
2025-05-25 12:20:04,043 - INFO - Starting model loading process
2025-05-25 12:20:04,044 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:20:04,090 - INFO - FFmpeg check return code: 0
2025-05-25 12:20:04,090 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:20:04,090 - INFO - FFmpeg check stderr: 
2025-05-25 12:20:04,090 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:20:15,206 - INFO - Attempting to load XTTS model
2025-05-25 12:20:15,207 - INFO - CUDA not available - using CPU
2025-05-25 12:20:15,207 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:20:40,547 - INFO - XTTS model loaded successfully
2025-05-25 12:20:42,477 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 12:20:45,366 - INFO - Starting speech generation
2025-05-25 12:20:45,406 - INFO - Generating speech to file: output\output_1748164845.wav
2025-05-25 12:20:45,413 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:20:45,433 - ERROR - Generation error: generator didn't stop after throw()
2025-05-25 12:21:18,330 - INFO - GUI initialization complete
2025-05-25 12:21:18,770 - INFO - Pygame mixer initialized successfully
2025-05-25 12:21:18,770 - INFO - Starting XTTS GUI application
2025-05-25 12:21:41,619 - INFO - Starting speech generation
2025-05-25 12:21:41,621 - INFO - Generating speech to file: output\output_1748164901.wav
2025-05-25 12:21:41,628 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:21:41,631 - ERROR - Generation error: generator didn't stop after throw()
2025-05-25 12:21:55,431 - INFO - Application closing
2025-05-25 12:22:00,125 - INFO - GUI initialization complete
2025-05-25 12:22:00,134 - WARNING - Pygame not available - audio playback will use system default
2025-05-25 12:22:00,135 - INFO - Starting XTTS GUI application
2025-05-25 12:22:02,992 - ERROR - FFmpeg not found in PATH
2025-05-25 12:22:02,993 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:22:03,034 - INFO - FFmpeg check return code: 0
2025-05-25 12:22:03,035 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:22:03,035 - INFO - FFmpeg check stderr: 
2025-05-25 12:22:03,035 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:22:04,582 - INFO - Progress update: Checking model cache...
2025-05-25 12:22:04,590 - INFO - Starting model loading process
2025-05-25 12:22:04,591 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:22:04,617 - INFO - FFmpeg check return code: 0
2025-05-25 12:22:04,617 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:22:04,617 - INFO - FFmpeg check stderr: 
2025-05-25 12:22:04,617 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:22:04,618 - ERROR - Model loading error: TTS library not found. Please install with: pip install TTS torch. Error: No module named 'TTS'
2025-05-25 12:22:09,422 - INFO - Application closing
2025-05-25 12:23:14,942 - INFO - GUI initialization complete
2025-05-25 12:23:15,381 - INFO - Pygame mixer initialized successfully
2025-05-25 12:23:15,381 - INFO - Starting XTTS GUI application
2025-05-25 12:23:21,680 - ERROR - FFmpeg not found in PATH
2025-05-25 12:23:21,681 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:23:21,706 - INFO - FFmpeg check return code: 0
2025-05-25 12:23:21,706 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:23:21,707 - INFO - FFmpeg check stderr: 
2025-05-25 12:23:21,707 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:23:23,831 - INFO - Progress update: Checking model cache...
2025-05-25 12:23:23,839 - INFO - Starting model loading process
2025-05-25 12:23:23,840 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:23:23,869 - INFO - FFmpeg check return code: 0
2025-05-25 12:23:23,869 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:23:23,870 - INFO - FFmpeg check stderr: 
2025-05-25 12:23:23,870 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:23:31,345 - INFO - Attempting to load XTTS model
2025-05-25 12:23:31,345 - INFO - CUDA not available - using CPU
2025-05-25 12:23:31,345 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:23:56,840 - INFO - XTTS model loaded successfully
2025-05-25 12:24:00,383 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 12:24:20,377 - INFO - Voice sample selected: C:/Users/<USER>/Downloads/كيف تجاوب على قطعة النحو مع سوريا أمين.wav
2025-05-25 12:24:23,294 - INFO - Starting speech generation
2025-05-25 12:24:23,298 - INFO - Generating speech to file: output\output_1748165063.wav
2025-05-25 12:24:23,304 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:25:11,017 - INFO - Speech generation completed successfully
2025-05-25 12:25:11,019 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 12:25:15,523 - INFO - Playing audio: output\output_1748165063.wav
2025-05-25 12:25:19,991 - INFO - Playing audio: output\output_1748165063.wav
2025-05-25 12:25:51,684 - INFO - Starting speech generation
2025-05-25 12:25:51,687 - INFO - Generating speech to file: output\output_1748165151.wav
2025-05-25 12:25:51,697 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:26:00,275 - INFO - Speech generation completed successfully
2025-05-25 12:26:00,276 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 12:26:03,512 - INFO - Playing audio: output\output_1748165151.wav
2025-05-25 12:26:09,867 - INFO - Loaded sample text for accent: Egyptian
2025-05-25 12:26:13,074 - INFO - Loaded sample text for accent: Egyptian
2025-05-25 12:26:15,084 - INFO - Playing audio: output\output_1748165151.wav
2025-05-25 12:27:15,363 - INFO - Playing audio: output\output_1748165151.wav
2025-05-25 12:28:44,071 - INFO - Playing audio: output\output_1748165151.wav
2025-05-25 12:29:35,994 - INFO - Starting speech generation
2025-05-25 12:29:36,007 - INFO - Generating speech to file: output\output_1748165376.wav
2025-05-25 12:29:36,018 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:30:35,344 - INFO - Speech generation completed successfully
2025-05-25 12:30:35,347 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 12:30:43,518 - INFO - Playing audio: output\output_1748165376.wav
2025-05-25 12:31:08,707 - INFO - Application closing
2025-05-25 12:38:25,267 - INFO - GUI initialization complete
2025-05-25 12:38:26,108 - INFO - Pygame mixer initialized successfully
2025-05-25 12:38:26,109 - INFO - Starting XTTS GUI application
2025-05-25 12:38:35,990 - ERROR - FFmpeg not found in PATH
2025-05-25 12:38:35,991 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:38:36,346 - INFO - FFmpeg check return code: 0
2025-05-25 12:38:36,346 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:38:36,346 - INFO - FFmpeg check stderr: 
2025-05-25 12:38:36,346 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:38:37,876 - INFO - Progress update: Checking model cache...
2025-05-25 12:38:37,897 - INFO - Starting model loading process
2025-05-25 12:38:37,898 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:38:37,928 - INFO - FFmpeg check return code: 0
2025-05-25 12:38:37,928 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:38:37,928 - INFO - FFmpeg check stderr: 
2025-05-25 12:38:37,929 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:38:50,287 - INFO - Attempting to load XTTS model
2025-05-25 12:38:50,287 - INFO - CUDA not available - using CPU
2025-05-25 12:38:50,289 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:38:55,578 - INFO - Voice sample selected: C:/Users/<USER>/Downloads/كيف تجاوب على قطعة النحو مع سوريا أمين.wav
2025-05-25 12:39:22,160 - INFO - XTTS model loaded successfully
2025-05-25 12:39:26,474 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 12:39:31,891 - INFO - Starting speech generation
2025-05-25 12:39:31,925 - INFO - Generating speech to file: output\output_1748165971.wav
2025-05-25 12:39:31,935 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:40:11,003 - INFO - Speech generation completed successfully
2025-05-25 12:40:11,004 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 12:40:16,023 - INFO - Playing audio: output\output_1748165971.wav
2025-05-25 12:49:21,135 - INFO - GUI initialization complete
2025-05-25 12:49:21,964 - INFO - Pygame mixer initialized successfully
2025-05-25 12:49:21,964 - INFO - Starting XTTS GUI application
2025-05-25 12:50:00,578 - ERROR - FFmpeg not found in PATH
2025-05-25 12:50:00,579 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:50:01,084 - INFO - FFmpeg check return code: 0
2025-05-25 12:50:01,084 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:50:01,086 - INFO - FFmpeg check stderr: 
2025-05-25 12:50:01,086 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:50:02,676 - INFO - Progress update: Checking model cache...
2025-05-25 12:50:02,692 - INFO - Starting model loading process
2025-05-25 12:50:02,693 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:50:02,721 - INFO - FFmpeg check return code: 0
2025-05-25 12:50:02,721 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:50:02,721 - INFO - FFmpeg check stderr: 
2025-05-25 12:50:02,722 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:50:15,516 - INFO - Attempting to load XTTS model
2025-05-25 12:50:15,516 - INFO - CUDA not available - using CPU
2025-05-25 12:50:15,518 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:51:14,049 - INFO - XTTS model loaded successfully
2025-05-25 12:51:22,993 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 12:51:30,845 - INFO - Application closing
2025-05-25 12:58:31,599 - INFO - GUI initialization complete
2025-05-25 12:58:32,081 - INFO - Pygame mixer initialized successfully
2025-05-25 12:58:32,081 - INFO - Starting XTTS GUI application
2025-05-25 12:58:39,253 - INFO - Application closing
2025-05-25 13:09:23,115 - INFO - GUI initialization complete
2025-05-25 13:09:23,802 - INFO - Pygame mixer initialized successfully
2025-05-25 13:09:23,802 - INFO - Starting XTTS GUI application
2025-05-25 13:09:25,614 - ERROR - FFmpeg not found in PATH
2025-05-25 13:09:25,630 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 13:09:25,948 - INFO - FFmpeg check return code: 0
2025-05-25 13:09:25,948 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 13:09:25,948 - INFO - FFmpeg check stderr: 
2025-05-25 13:09:25,949 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 13:09:27,511 - INFO - Progress update: Checking model cache...
2025-05-25 13:09:27,525 - INFO - Starting model loading process
2025-05-25 13:09:27,525 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 13:09:27,552 - INFO - FFmpeg check return code: 0
2025-05-25 13:09:27,553 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 13:09:27,553 - INFO - FFmpeg check stderr: 
2025-05-25 13:09:27,553 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 13:09:37,992 - INFO - Attempting to load XTTS model
2025-05-25 13:09:37,992 - INFO - CUDA not available - using CPU
2025-05-25 13:09:37,995 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 13:10:07,124 - INFO - XTTS model loaded successfully
2025-05-25 13:10:09,366 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 13:11:30,438 - INFO - Voice sample selected: C:/Users/<USER>/Pictures/New folder/xtts-project/egyptian_dataset/wavs/speaker1_001.wav
2025-05-25 13:11:33,904 - INFO - Starting speech generation
2025-05-25 13:11:33,907 - INFO - Generating speech to file: output\output_1748167893.wav
2025-05-25 13:11:33,914 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 13:12:03,857 - INFO - Speech generation completed successfully
2025-05-25 13:12:03,859 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 13:12:06,817 - INFO - Playing audio: output\output_1748167893.wav
2025-05-25 13:12:29,516 - INFO - Voice sample selected: C:/Users/<USER>/Pictures/New folder/xtts-project/egyptian_training_data/egyptian_sample.wav
2025-05-25 13:12:33,106 - INFO - Starting speech generation
2025-05-25 13:12:33,108 - INFO - Generating speech to file: output\output_1748167953.wav
2025-05-25 13:12:33,114 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 13:13:03,074 - INFO - Speech generation completed successfully
2025-05-25 13:13:03,076 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 13:13:06,121 - INFO - Playing audio: output\output_1748167953.wav
2025-05-25 13:13:17,580 - INFO - Application closing
2025-05-25 13:13:26,130 - INFO - GUI initialization complete
2025-05-25 13:13:26,131 - WARNING - Pygame not available - audio playback will use system default
2025-05-25 13:13:26,131 - INFO - Starting XTTS GUI application
2025-05-25 13:13:31,818 - INFO - Application closing
2025-05-25 13:14:26,832 - INFO - GUI initialization complete
2025-05-25 13:14:27,304 - INFO - Pygame mixer initialized successfully
2025-05-25 13:14:27,304 - INFO - Starting XTTS GUI application
2025-05-25 13:14:29,896 - INFO - Application closing
2025-05-25 13:22:46,297 - INFO - GUI initialization complete
2025-05-25 13:22:47,094 - INFO - Pygame mixer initialized successfully
2025-05-25 13:22:47,094 - INFO - Starting XTTS GUI application
2025-05-25 13:27:53,858 - INFO - Application closing
2025-05-25 13:46:37,684 - INFO - GUI initialization complete
2025-05-25 13:46:38,336 - INFO - Pygame mixer initialized successfully
2025-05-25 13:46:38,336 - INFO - Starting XTTS GUI application
2025-05-25 13:46:40,979 - ERROR - FFmpeg not found in PATH
2025-05-25 13:46:40,980 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 13:46:41,714 - INFO - FFmpeg check return code: 0
2025-05-25 13:46:41,715 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 13:46:41,715 - INFO - FFmpeg check stderr: 
2025-05-25 13:46:41,715 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 13:46:43,340 - INFO - Progress update: Checking model cache...
2025-05-25 13:46:43,350 - INFO - Starting model loading process
2025-05-25 13:46:43,351 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 13:46:43,377 - INFO - FFmpeg check return code: 0
2025-05-25 13:46:43,377 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 13:46:43,377 - INFO - FFmpeg check stderr: 
2025-05-25 13:46:43,377 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 13:46:53,572 - INFO - Attempting to load XTTS model
2025-05-25 13:46:53,572 - INFO - CUDA not available - using CPU
2025-05-25 13:46:53,573 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 13:47:04,735 - INFO - Voice sample selected: C:/Users/<USER>/Pictures/New folder/xtts-project/egyptian_training_data/egyptian_sample.wav
2025-05-25 13:47:08,654 - INFO - Loaded sample text for accent: Egyptian
2025-05-25 13:47:10,390 - INFO - Loaded sample text for accent: Egyptian
2025-05-25 13:47:11,140 - INFO - Loaded sample text for accent: Egyptian
2025-05-25 13:47:21,799 - INFO - XTTS model loaded successfully
2025-05-25 13:47:23,857 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 13:47:29,618 - INFO - Loaded trained voice sample: egyptian_voice_samples\sample_01.wav
2025-05-25 13:47:38,029 - INFO - Starting speech generation
2025-05-25 13:47:38,063 - INFO - Generating speech to file: output\output_1748170058.wav
2025-05-25 13:47:38,069 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 13:48:05,017 - INFO - Speech generation completed successfully
2025-05-25 13:48:05,018 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 13:48:09,140 - INFO - Playing audio: output\output_1748170058.wav
2025-05-25 13:48:22,824 - INFO - Loaded optimal Egyptian settings from training results
2025-05-25 13:48:24,454 - INFO - Starting speech generation
2025-05-25 13:48:24,457 - INFO - Generating speech to file: output\output_1748170104.wav
2025-05-25 13:48:24,461 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 13:48:52,853 - INFO - Speech generation completed successfully
2025-05-25 13:48:52,854 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 13:48:56,583 - INFO - Playing audio: output\output_1748170104.wav
2025-05-25 13:49:08,894 - INFO - Application closing
2025-05-25 14:33:22,262 - INFO - GUI initialization complete
2025-05-25 14:33:22,975 - INFO - Pygame mixer initialized successfully
2025-05-25 14:33:22,975 - INFO - Starting XTTS GUI application
2025-05-25 14:33:27,598 - ERROR - FFmpeg not found in PATH
2025-05-25 14:33:27,603 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 14:33:28,328 - INFO - FFmpeg check return code: 0
2025-05-25 14:33:28,328 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 14:33:28,329 - INFO - FFmpeg check stderr: 
2025-05-25 14:33:28,329 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 14:33:30,670 - INFO - Progress update: Checking model cache...
2025-05-25 14:33:30,677 - INFO - Starting model loading process
2025-05-25 14:33:30,678 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 14:33:30,705 - INFO - FFmpeg check return code: 0
2025-05-25 14:33:30,706 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 14:33:30,706 - INFO - FFmpeg check stderr: 
2025-05-25 14:33:30,706 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 14:33:41,171 - INFO - Attempting to load XTTS model
2025-05-25 14:33:41,171 - INFO - CUDA not available - using CPU
2025-05-25 14:33:41,172 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 14:34:08,780 - INFO - XTTS model loaded successfully
2025-05-25 14:34:16,061 - INFO - Loaded trained voice sample: egyptian_voice_samples\sample_01.wav
2025-05-25 14:34:16,197 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 14:34:23,032 - INFO - Loaded trained voice sample: egyptian_voice_samples\sample_01.wav
2025-05-25 14:34:24,356 - INFO - Starting speech generation
2025-05-25 14:34:24,389 - INFO - Generating speech to file: output\output_1748172864.wav
2025-05-25 14:34:24,397 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 14:34:54,368 - INFO - Speech generation completed successfully
2025-05-25 14:34:54,369 - INFO - Progress update: Speech generated successfully! (100.0%, ETA: unknown)
2025-05-25 14:34:58,042 - INFO - Playing audio: output\output_1748172864.wav
2025-05-25 14:35:09,584 - INFO - Application closing
