2025-05-25 11:47:31,968 - ERROR - Fatal error: bad option "-width": must be -after, -anchor, -before, -expand, -fill, -in, -ipadx, -ipady, -padx, -pady, or -side
2025-05-25 11:48:41,714 - INFO - GUI initialization complete
2025-05-25 11:48:42,933 - INFO - Pygame mixer initialized successfully
2025-05-25 11:48:42,934 - INFO - Starting XTTS GUI application
2025-05-25 11:50:08,226 - ERROR - FFmpeg not found in PATH
2025-05-25 11:50:09,958 - INFO - Starting FFmpeg installation...
2025-05-25 11:50:09,983 - INFO - Downloading FFmpeg...
2025-05-25 11:50:58,527 - INFO - Extracting FFmpeg...
2025-05-25 11:51:14,871 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 11:51:15,443 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:15,443 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:15,445 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:15,445 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:15,446 - INFO - FFmpeg installed successfully
2025-05-25 11:51:19,734 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:19,735 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:19,735 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:19,735 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:21,480 - INFO - Progress update: Checking model cache...
2025-05-25 11:51:21,639 - INFO - Starting model loading process
2025-05-25 11:51:21,664 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 11:51:21,764 - INFO - FFmpeg check return code: 0
2025-05-25 11:51:21,764 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 11:51:21,764 - INFO - FFmpeg check stderr: 
2025-05-25 11:51:21,764 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 11:51:38,674 - INFO - Attempting to load XTTS model
2025-05-25 11:51:38,674 - INFO - CUDA not available - using CPU
2025-05-25 11:51:38,677 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:01:04,526 - INFO - GUI initialization complete
2025-05-25 12:01:04,529 - ERROR - Failed to initialize Pygame mixer: No module named 'pygame'
2025-05-25 12:01:04,529 - INFO - Starting XTTS GUI application
2025-05-25 12:01:07,642 - ERROR - FFmpeg not found in PATH
2025-05-25 12:01:10,021 - INFO - Starting FFmpeg installation...
2025-05-25 12:01:10,022 - INFO - Downloading FFmpeg...
2025-05-25 12:01:54,155 - INFO - Extracting FFmpeg...
2025-05-25 12:02:08,410 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:02:09,343 - INFO - FFmpeg check return code: 0
2025-05-25 12:02:09,343 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:02:09,343 - INFO - FFmpeg check stderr: 
2025-05-25 12:02:09,344 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:02:09,344 - INFO - FFmpeg installed successfully
2025-05-25 12:02:11,454 - INFO - FFmpeg check return code: 0
2025-05-25 12:02:11,455 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:02:11,455 - INFO - FFmpeg check stderr: 
2025-05-25 12:02:11,455 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:02:12,864 - INFO - Progress update: Checking model cache...
2025-05-25 12:02:12,892 - INFO - Starting model loading process
2025-05-25 12:02:12,892 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:02:12,921 - INFO - FFmpeg check return code: 0
2025-05-25 12:02:12,922 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:02:12,922 - INFO - FFmpeg check stderr: 
2025-05-25 12:02:12,922 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:02:12,924 - ERROR - Model loading error: No module named 'TTS'
2025-05-25 12:02:24,161 - INFO - Application closing
2025-05-25 12:02:27,820 - INFO - GUI initialization complete
2025-05-25 12:02:27,822 - ERROR - Failed to initialize Pygame mixer: No module named 'pygame'
2025-05-25 12:02:27,822 - INFO - Starting XTTS GUI application
2025-05-25 12:02:30,358 - ERROR - FFmpeg not found in PATH
2025-05-25 12:02:31,953 - INFO - Starting FFmpeg installation...
2025-05-25 12:02:31,954 - INFO - Downloading FFmpeg...
2025-05-25 12:04:35,736 - INFO - GUI initialization complete
2025-05-25 12:04:35,737 - ERROR - Failed to initialize Pygame mixer: No module named 'pygame'
2025-05-25 12:04:35,738 - INFO - Starting XTTS GUI application
2025-05-25 12:04:37,898 - ERROR - FFmpeg not found in PATH
2025-05-25 12:04:39,906 - INFO - Starting FFmpeg installation...
2025-05-25 12:04:39,908 - INFO - Downloading FFmpeg...
2025-05-25 12:05:24,361 - INFO - Extracting FFmpeg...
2025-05-25 12:05:26,983 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:05:27,465 - INFO - FFmpeg check return code: 0
2025-05-25 12:05:27,465 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:05:27,466 - INFO - FFmpeg check stderr: 
2025-05-25 12:05:27,466 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:05:27,466 - INFO - FFmpeg installed successfully
2025-05-25 12:05:31,079 - INFO - FFmpeg check return code: 0
2025-05-25 12:05:31,080 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:05:31,080 - INFO - FFmpeg check stderr: 
2025-05-25 12:05:31,080 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:05:33,815 - INFO - Progress update: Checking model cache...
2025-05-25 12:05:33,824 - INFO - Starting model loading process
2025-05-25 12:05:33,825 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:05:33,852 - INFO - FFmpeg check return code: 0
2025-05-25 12:05:33,853 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:05:33,853 - INFO - FFmpeg check stderr: 
2025-05-25 12:05:33,853 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:05:33,854 - ERROR - Model loading error: No module named 'TTS'
2025-05-25 12:12:41,055 - INFO - Application closing
2025-05-25 12:15:25,345 - INFO - GUI initialization complete
2025-05-25 12:15:25,347 - ERROR - Failed to initialize Pygame mixer: No module named 'pygame'
2025-05-25 12:15:25,347 - INFO - Starting XTTS GUI application
2025-05-25 12:15:27,629 - ERROR - FFmpeg not found in PATH
2025-05-25 12:15:29,961 - INFO - Starting FFmpeg installation...
2025-05-25 12:15:29,961 - INFO - Downloading FFmpeg...
2025-05-25 12:16:14,111 - INFO - Extracting FFmpeg...
2025-05-25 12:16:17,099 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:16:17,415 - INFO - FFmpeg check return code: 0
2025-05-25 12:16:17,416 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:16:17,416 - INFO - FFmpeg check stderr: 
2025-05-25 12:16:17,416 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:16:17,416 - INFO - FFmpeg installed successfully
2025-05-25 12:16:19,108 - INFO - FFmpeg check return code: 0
2025-05-25 12:16:19,109 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:16:19,109 - INFO - FFmpeg check stderr: 
2025-05-25 12:16:19,109 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:16:20,745 - INFO - Progress update: Checking model cache...
2025-05-25 12:16:20,758 - INFO - Starting model loading process
2025-05-25 12:16:20,759 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:16:20,792 - INFO - FFmpeg check return code: 0
2025-05-25 12:16:20,792 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:16:20,792 - INFO - FFmpeg check stderr: 
2025-05-25 12:16:20,793 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:16:20,793 - ERROR - Model loading error: No module named 'TTS'
2025-05-25 12:16:23,857 - INFO - Application closing
2025-05-25 12:17:25,423 - INFO - GUI initialization complete
2025-05-25 12:17:26,105 - INFO - Pygame mixer initialized successfully
2025-05-25 12:17:26,105 - INFO - Starting XTTS GUI application
2025-05-25 12:17:30,098 - ERROR - FFmpeg not found in PATH
2025-05-25 12:17:30,098 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:17:30,124 - INFO - FFmpeg check return code: 0
2025-05-25 12:17:30,124 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:17:30,124 - INFO - FFmpeg check stderr: 
2025-05-25 12:17:30,124 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:17:31,608 - INFO - Progress update: Checking model cache...
2025-05-25 12:17:31,622 - INFO - Starting model loading process
2025-05-25 12:17:31,623 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:17:31,664 - INFO - FFmpeg check return code: 0
2025-05-25 12:17:31,664 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:17:31,664 - INFO - FFmpeg check stderr: 
2025-05-25 12:17:31,664 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:17:42,231 - INFO - Attempting to load XTTS model
2025-05-25 12:17:42,231 - INFO - CUDA not available - using CPU
2025-05-25 12:17:42,234 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:18:08,997 - INFO - XTTS model loaded successfully
2025-05-25 12:18:13,910 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 12:18:31,364 - INFO - Starting speech generation
2025-05-25 12:18:31,368 - INFO - Generating speech to file: output\output_1748164711.wav
2025-05-25 12:18:31,375 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:18:31,377 - WARNING - Advanced generation failed, falling back to standard: 'TTS' object has no attribute 'model'
2025-05-25 12:18:31,391 - ERROR - Generation error: generator didn't stop after throw()
2025-05-25 12:19:11,926 - INFO - Starting speech generation
2025-05-25 12:19:11,927 - INFO - Generating speech to file: output\output_1748164751.wav
2025-05-25 12:19:11,936 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:19:11,938 - WARNING - Advanced generation failed, falling back to standard: 'TTS' object has no attribute 'model'
2025-05-25 12:19:11,939 - ERROR - Generation error: generator didn't stop after throw()
2025-05-25 12:19:50,638 - INFO - GUI initialization complete
2025-05-25 12:19:52,023 - INFO - Pygame mixer initialized successfully
2025-05-25 12:19:52,023 - INFO - Starting XTTS GUI application
2025-05-25 12:19:55,578 - INFO - Application closing
2025-05-25 12:20:02,376 - ERROR - FFmpeg not found in PATH
2025-05-25 12:20:02,376 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:20:02,421 - INFO - FFmpeg check return code: 0
2025-05-25 12:20:02,421 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:20:02,421 - INFO - FFmpeg check stderr: 
2025-05-25 12:20:02,421 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:20:04,034 - INFO - Progress update: Checking model cache...
2025-05-25 12:20:04,043 - INFO - Starting model loading process
2025-05-25 12:20:04,044 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:20:04,090 - INFO - FFmpeg check return code: 0
2025-05-25 12:20:04,090 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:20:04,090 - INFO - FFmpeg check stderr: 
2025-05-25 12:20:04,090 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:20:15,206 - INFO - Attempting to load XTTS model
2025-05-25 12:20:15,207 - INFO - CUDA not available - using CPU
2025-05-25 12:20:15,207 - INFO - Progress update: Loading XTTS model... (25.0%, ETA: unknown)
2025-05-25 12:20:40,547 - INFO - XTTS model loaded successfully
2025-05-25 12:20:42,477 - INFO - Progress update: Model loaded successfully! (100.0%, ETA: unknown)
2025-05-25 12:20:45,366 - INFO - Starting speech generation
2025-05-25 12:20:45,406 - INFO - Generating speech to file: output\output_1748164845.wav
2025-05-25 12:20:45,413 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:20:45,433 - ERROR - Generation error: generator didn't stop after throw()
2025-05-25 12:21:18,330 - INFO - GUI initialization complete
2025-05-25 12:21:18,770 - INFO - Pygame mixer initialized successfully
2025-05-25 12:21:18,770 - INFO - Starting XTTS GUI application
2025-05-25 12:21:41,619 - INFO - Starting speech generation
2025-05-25 12:21:41,621 - INFO - Generating speech to file: output\output_1748164901.wav
2025-05-25 12:21:41,628 - INFO - Progress update: Processing text... (25.0%, ETA: unknown)
2025-05-25 12:21:41,631 - ERROR - Generation error: generator didn't stop after throw()
2025-05-25 12:21:55,431 - INFO - Application closing
2025-05-25 12:22:00,125 - INFO - GUI initialization complete
2025-05-25 12:22:00,134 - WARNING - Pygame not available - audio playback will use system default
2025-05-25 12:22:00,135 - INFO - Starting XTTS GUI application
2025-05-25 12:22:02,992 - ERROR - FFmpeg not found in PATH
2025-05-25 12:22:02,993 - INFO - Adding FFmpeg path to environment: C:\Users\<USER>\AppData\Local\ffmpeg\bin
2025-05-25 12:22:03,034 - INFO - FFmpeg check return code: 0
2025-05-25 12:22:03,035 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:22:03,035 - INFO - FFmpeg check stderr: 
2025-05-25 12:22:03,035 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:22:04,582 - INFO - Progress update: Checking model cache...
2025-05-25 12:22:04,590 - INFO - Starting model loading process
2025-05-25 12:22:04,591 - INFO - Requested model name: tts_models/multilingual/multi-dataset/xtts_v2
2025-05-25 12:22:04,617 - INFO - FFmpeg check return code: 0
2025-05-25 12:22:04,617 - INFO - FFmpeg check stdout: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
built with gcc 15.1.0 (crosstool-NG 1.27.0.42_35c1e72)
configuration: --prefix=/ffbuild/prefix --pkg-config-flags=--static --pkg-config=pkg-config --cross-prefix=x86_64-w64-mingw32- --arch=x86_64 --target-os=mingw32 --enable-gpl --enable-version3 --disable-debug --disable-w32threads --enable-pthreads --enable-iconv --enable-zlib --enable-libfribidi --enable-gmp --enable-libxml2 --enable-lzma --enable-fontc
2025-05-25 12:22:04,617 - INFO - FFmpeg check stderr: 
2025-05-25 12:22:04,617 - INFO - FFmpeg found: ffmpeg version N-119683-ga18b2c2696-20250524 Copyright (c) 2000-2025 the FFmpeg developers
2025-05-25 12:22:04,618 - ERROR - Model loading error: TTS library not found. Please install with: pip install TTS torch. Error: No module named 'TTS'
2025-05-25 12:22:09,422 - INFO - Application closing
