import os
import time
import sys
import torch

print("=== XTTS Test Script ===")
print("Python version:", sys.version)

try:
    # Import from coqui-tts package
    from TTS.api import TTS
    print("\ncoqui-tts package imported successfully!")
except ImportError as e:
    print(f"Error importing TTS: {e}")
    sys.exit(1)

print("\nTorch version:", torch.__version__)
print("CUDA available:", torch.cuda.is_available())
if torch.cuda.is_available():
    print("CUDA device:", torch.cuda.get_device_name(0))

print("\nChecking available models...")
try:
    # List available models
    tts = TTS()
    print("\nAvailable models:")
    for model in tts.list_models():
        if "xtts" in model.lower():
            print(f" - {model} (XTTS model)")
        else:
            print(f" - {model}")
    print("\nModels listed successfully!")
except Exception as e:
    print(f"Error listing models: {e}")
    sys.exit(1)

print("\nInitializing XTTS model...")
try:
    # Initialize TTS with XTTS model
    start_time = time.time()
    model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
    tts = TTS(model_name, gpu=torch.cuda.is_available())
    load_time = time.time() - start_time
    print(f"Model loaded in {load_time:.2f} seconds")

    # List available languages
    print("\nAvailable languages:")
    print(tts.languages)

    # Ask if user wants to generate speech
    generate = input("\nGenerate test speech? (y/n): ").lower() == 'y'
    if generate:
        # Generate speech
        print("\nGenerating English speech...")
        start_time = time.time()
        
        os.makedirs("output", exist_ok=True)
        tts.tts_to_file(
            text="Hello, this is a test of the XTTS text to speech system running on Windows.",
            speaker_wav="samples/sample_voice.wav",
            language="en",
            file_path="output/test_output_en.wav"
        )
        
        generation_time = time.time() - start_time
        print(f"Speech generated in {generation_time:.2f} seconds")
        print("Output saved to output/test_output_en.wav")
        
        # Generate Arabic speech
        print("\nGenerating Arabic speech...")
        start_time = time.time()
        
        tts.tts_to_file(
            text="مرحبا، هذا اختبار لنظام تحويل النص إلى كلام باستخدام إكس تي تي إس.",
            speaker_wav="samples/sample_voice.wav",
            language="ar",
            file_path="output/test_output_ar.wav"
        )
        
        generation_time = time.time() - start_time
        print(f"Arabic speech generated in {generation_time:.2f} seconds")
        print("Output saved to output/test_output_ar.wav")

    print("\nTest completed successfully!")
except Exception as e:
    print(f"Error during test: {e}")
