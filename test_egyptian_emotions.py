#!/usr/bin/env python3
"""
Test script for Egyptian Emotions GUI
Quick test to verify all features are working
"""

import os
import sys
import subprocess
import time

def test_gui_launch():
    """Test if the GUI launches without errors"""
    print("🧪 Testing Egyptian Emotions GUI Launch...")
    
    try:
        # Launch the GUI in a subprocess
        process = subprocess.Popen([
            sys.executable, 
            "xtts_gui_egyptian_emotions.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait a few seconds for initialization
        time.sleep(5)
        
        # Check if process is still running (good sign)
        if process.poll() is None:
            print("✅ GUI launched successfully!")
            print("✅ Process is running without immediate crashes")
            
            # Terminate the test process
            process.terminate()
            process.wait(timeout=5)
            return True
        else:
            # Process exited, check for errors
            stdout, stderr = process.communicate()
            print("❌ GUI exited unexpectedly")
            print(f"Exit code: {process.returncode}")
            if stderr:
                print(f"Errors: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to launch GUI: {str(e)}")
        return False

def test_dependencies():
    """Test if all required dependencies are available"""
    print("\n🔍 Testing Dependencies...")
    
    dependencies = {
        "tkinter": "GUI framework",
        "TTS": "Text-to-Speech library", 
        "torch": "PyTorch for ML models",
        "pygame": "Audio playback",
        "numpy": "Numerical computing",
        "scipy": "Scientific computing"
    }
    
    all_good = True
    
    for dep, description in dependencies.items():
        try:
            if dep == "tkinter":
                import tkinter
            elif dep == "TTS":
                from TTS.api import TTS
            elif dep == "torch":
                import torch
            elif dep == "pygame":
                import pygame
            elif dep == "numpy":
                import numpy
            elif dep == "scipy":
                import scipy
                
            print(f"✅ {dep} - {description}")
        except ImportError:
            print(f"❌ {dep} - {description} (MISSING)")
            all_good = False
        except Exception as e:
            print(f"⚠️ {dep} - {description} (ERROR: {str(e)})")
            all_good = False
    
    return all_good

def test_ffmpeg():
    """Test FFmpeg availability"""
    print("\n🎵 Testing FFmpeg...")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg found: {version_line}")
            return True
        else:
            print(f"❌ FFmpeg check failed with return code: {result.returncode}")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg not found in PATH")
        return False
    except Exception as e:
        print(f"❌ FFmpeg test error: {str(e)}")
        return False

def test_file_structure():
    """Test if required files exist"""
    print("\n📁 Testing File Structure...")
    
    required_files = [
        "xtts_gui_egyptian_emotions.py",
        "xtts_gui_fixed.py"
    ]
    
    all_good = True
    
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size:,} bytes)")
        else:
            print(f"❌ {file} (MISSING)")
            all_good = False
    
    # Check output directory
    if os.path.exists("output"):
        print("✅ output/ directory exists")
    else:
        print("⚠️ output/ directory missing (will be created automatically)")
    
    return all_good

def main():
    """Run all tests"""
    print("🎭 XTTS Egyptian Emotions GUI - Test Suite")
    print("=" * 50)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("FFmpeg", test_ffmpeg), 
        ("File Structure", test_file_structure),
        ("GUI Launch", test_gui_launch)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {str(e)}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The Egyptian Emotions GUI should work perfectly!")
        print("\n🚀 You can now run: python xtts_gui_egyptian_emotions.py")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please check the issues above.")
        
        if not results.get("Dependencies", False):
            print("\n💡 To fix dependency issues:")
            print("   pip install TTS torch pygame numpy scipy")
        
        if not results.get("FFmpeg", False):
            print("\n💡 To fix FFmpeg issues:")
            print("   The GUI will try to install FFmpeg automatically")
    
    print("\n🎭 Happy Egyptian speech generation! 🇪🇬")

if __name__ == "__main__":
    main()
