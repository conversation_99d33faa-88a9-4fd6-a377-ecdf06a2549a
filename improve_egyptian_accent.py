#!/usr/bin/env python3
"""
Egyptian Accent Improvement Guide and Tools
Tips and techniques for better Egyptian Arabic speech synthesis
"""

import os
import sys

def analyze_voice_sample(file_path):
    """Analyze voice sample quality for Egyptian accent"""
    print("🎤 Voice Sample Analysis for Egyptian Accent")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    # Get file info
    file_size = os.path.getsize(file_path)
    print(f"📁 File: {os.path.basename(file_path)}")
    print(f"📊 Size: {file_size:,} bytes")
    
    # Check file extension
    ext = os.path.splitext(file_path)[1].lower()
    if ext == '.wav':
        print("✅ Format: WAV (Good)")
    elif ext == '.mp3':
        print("⚠️ Format: MP3 (Convert to WAV for better quality)")
    else:
        print(f"❌ Format: {ext} (Use WAV format)")
    
    # Recommendations
    print("\n🎯 Recommendations for Better Egyptian Accent:")
    print("1. Use a native Egyptian speaker (Cairo dialect preferred)")
    print("2. Clear pronunciation of Egyptian-specific sounds:")
    print("   - ج as 'g' (not 'j'): جميل = 'gameel'")
    print("   - ق as glottal stop: قال = 'aal'") 
    print("   - Emphasis on pharyngeal sounds")
    print("3. Sample should be 5-15 seconds long")
    print("4. No background noise")
    print("5. Natural speaking pace")
    print("6. Include Egyptian-specific words if possible")
    
    return True

def get_egyptian_sample_texts():
    """Get high-quality Egyptian sample texts"""
    return {
        "Basic Egyptian": {
            "arabic": "أهلاً وسهلاً! إزيك النهاردة؟ أنا مصري من القاهرة.",
            "pronunciation": "Ahlan wa sahlan! Izzayyak ennaharda? Ana masri min el-qahira.",
            "english": "Hello and welcome! How are you today? I'm Egyptian from Cairo."
        },
        "Egyptian Expressions": {
            "arabic": "يلا بينا نروح نتمشى شوية في وسط البلد.",
            "pronunciation": "Yalla beena nerooh netmassha shwayya fi west el-balad.",
            "english": "Come on, let's go walk a bit in downtown."
        },
        "Egyptian Hospitality": {
            "arabic": "اتفضل اشرب شاي واقعد معانا شوية.",
            "pronunciation": "Itfaddal ishrab shai wa-o'od ma'ana shwayya.",
            "english": "Please have some tea and sit with us for a while."
        },
        "Egyptian Emotions": {
            "arabic": "والله العظيم ده أحلى كلام سمعته النهاردة!",
            "pronunciation": "Wallahi el-azeem da ahla kalam seme'to ennaharda!",
            "english": "I swear this is the best thing I heard today!"
        },
        "Egyptian Storytelling": {
            "arabic": "كان يا ما كان في قديم الزمان واحد مصري عايش في الإسكندرية.",
            "pronunciation": "Kan ya ma kan fi qadeem ez-zaman wahed masri 'ayesh fil-iskandariyya.",
            "english": "Once upon a time there was an Egyptian living in Alexandria."
        }
    }

def create_egyptian_voice_guide():
    """Create a guide for recording Egyptian voice samples"""
    print("\n🎙️ Egyptian Voice Recording Guide")
    print("=" * 40)
    
    texts = get_egyptian_sample_texts()
    
    print("📝 Recommended texts to record for voice samples:")
    print("(Record each one separately, then choose the best)")
    
    for i, (title, content) in enumerate(texts.items(), 1):
        print(f"\n{i}. {title}:")
        print(f"   Arabic: {content['arabic']}")
        print(f"   Pronunciation: {content['pronunciation']}")
        print(f"   Meaning: {content['english']}")
    
    print("\n🎯 Recording Tips:")
    print("• Use a quiet room")
    print("• Speak naturally, not too slow or fast")
    print("• Emphasize Egyptian pronunciation:")
    print("  - ج = 'g' sound (not 'j')")
    print("  - ق = glottal stop or 'a'")
    print("  - ث = 's' or 't' sound")
    print("  - ذ = 'z' or 'd' sound")
    print("• Include natural Egyptian intonation")
    print("• Record in WAV format, 44.1kHz or higher")

def improve_generation_settings():
    """Provide settings for better Egyptian accent generation"""
    print("\n⚙️ Optimal Settings for Egyptian Accent")
    print("=" * 45)
    
    settings = {
        "Temperature": {
            "value": "0.8-1.0",
            "reason": "Higher creativity for natural Egyptian expressions"
        },
        "Length Penalty": {
            "value": "0.9-1.1", 
            "reason": "Balanced length for natural Egyptian speech rhythm"
        },
        "Repetition Penalty": {
            "value": "1.8-2.2",
            "reason": "Prevent repetition while allowing Egyptian speech patterns"
        },
        "Speed": {
            "value": "0.9-1.1",
            "reason": "Natural Egyptian speaking pace"
        }
    }
    
    for param, info in settings.items():
        print(f"🎚️ {param}: {info['value']}")
        print(f"   Reason: {info['reason']}")
    
    print("\n🎭 Best Emotion Presets for Egyptian:")
    emotions = {
        "Happy": "For greetings and celebrations",
        "Dramatic": "For storytelling and emphasis", 
        "Neutral": "For general conversation",
        "Excited": "For enthusiastic expressions"
    }
    
    for emotion, use_case in emotions.items():
        print(f"• {emotion}: {use_case}")

def suggest_voice_samples():
    """Suggest where to find good Egyptian voice samples"""
    print("\n🔍 Finding Good Egyptian Voice Samples")
    print("=" * 40)
    
    print("📺 Sources for Egyptian voice samples:")
    print("1. Egyptian movies/TV shows (extract clean dialogue)")
    print("2. Egyptian news broadcasts")
    print("3. Egyptian YouTube channels")
    print("4. Record yourself if you're Egyptian")
    print("5. Ask Egyptian friends to record samples")
    
    print("\n🎯 What to look for:")
    print("• Clear Cairo dialect (most standard)")
    print("• No background music or noise")
    print("• Natural conversational tone")
    print("• Male or female voice (match your preference)")
    print("• Age appropriate for your use case")
    
    print("\n⚠️ Legal note:")
    print("Only use voice samples you have permission to use!")

def main():
    """Main function"""
    print("🇪🇬 Egyptian Accent Improvement Tool")
    print("=" * 50)
    
    print("Choose an option:")
    print("1. Analyze current voice sample")
    print("2. Get Egyptian sample texts")
    print("3. Voice recording guide")
    print("4. Optimal generation settings")
    print("5. Find voice samples")
    print("6. All of the above")
    
    choice = input("\nEnter choice (1-6): ").strip()
    
    if choice == "1":
        file_path = input("Enter path to your voice sample: ").strip()
        analyze_voice_sample(file_path)
    elif choice == "2":
        texts = get_egyptian_sample_texts()
        print("\n📝 Egyptian Sample Texts:")
        for title, content in texts.items():
            print(f"\n{title}:")
            print(f"Arabic: {content['arabic']}")
            print(f"English: {content['english']}")
    elif choice == "3":
        create_egyptian_voice_guide()
    elif choice == "4":
        improve_generation_settings()
    elif choice == "5":
        suggest_voice_samples()
    elif choice == "6":
        create_egyptian_voice_guide()
        improve_generation_settings()
        suggest_voice_samples()
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    main()
