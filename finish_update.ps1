# Script to finish XTTS update
# May 2025

# Configuration
$venv_name = "xtts_venv_310"
$python_exe = ".\$venv_name\Scripts\python.exe"

# Create log file
$log_file = "xtts_finish_update_log.txt"
Start-Transcript -Path $log_file -Append

Write-Host "=== Finishing XTTS Update ===" -ForegroundColor Cyan

# Install coqui-tts (the updated version of TTS)
Write-Host "Installing latest coqui-tts package..." -ForegroundColor Cyan
& $python_exe -m pip install coqui-tts>=0.26.1

Write-Host "\nUpdate complete!" -ForegroundColor Green
Write-Host "\nTo test the updated installation:\n"
Write-Host "1. Run the test script:\n   .\$venv_name\Scripts\activate.ps1\n   python test_xtts_updated.py\n"
Write-Host "2. To use the GUI application:\n   Double-click run_xtts_updated.bat\n"

Stop-Transcript
