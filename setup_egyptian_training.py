#!/usr/bin/env python3
"""
Setup Egyptian Training with Your Existing Audio
Quick setup using your existing Egyptian audio file
"""

import os
import shutil
import pandas as pd

def setup_with_existing_audio():
    """Setup training with your existing Egyptian audio"""
    print("🇪🇬 Setting Up Egyptian Training")
    print("=" * 35)
    
    # Your existing audio file
    source_audio = r"C:\Users\<USER>\Downloads\كيف تجاوب على قطعة النحو مع سوريا أمين.wav"
    
    if not os.path.exists(source_audio):
        print(f"❌ Audio file not found: {source_audio}")
        return
    
    # Copy to training dataset
    target_audio = "egyptian_dataset/wavs/speaker1_001.wav"
    
    print(f"📁 Copying audio file...")
    print(f"   From: {os.path.basename(source_audio)}")
    print(f"   To: {target_audio}")
    
    shutil.copy2(source_audio, target_audio)
    print("✅ Audio file copied successfully")
    
    # Update metadata with actual transcription
    print("\n📝 Updating metadata...")
    
    # You'll need to provide the actual transcription of your audio
    # For now, I'll use a placeholder - you should replace this with the actual text
    metadata_data = {
        'audio_file': ['speaker1_001.wav'],
        'text': ['كيف تجاوب على قطعة النحو مع سوريا أمين'],  # Replace with actual transcription
        'speaker_name': ['speaker1']
    }
    
    df = pd.DataFrame(metadata_data)
    df.to_csv("egyptian_dataset/metadata.csv", sep='|', index=False)
    
    print("✅ Metadata updated")
    print("\n⚠️ IMPORTANT:")
    print("You need to update the transcription in metadata.csv")
    print("Replace the text with the actual Arabic transcription of your audio")
    
    # Create additional samples by splitting the audio
    create_audio_segments()

def create_audio_segments():
    """Create multiple training samples from one audio file"""
    print("\n🔪 Creating Audio Segments")
    print("=" * 30)
    
    try:
        import librosa
        import soundfile as sf
        
        # Load the audio
        audio_file = "egyptian_dataset/wavs/speaker1_001.wav"
        audio, sr = librosa.load(audio_file, sr=22050)
        
        # Split into segments (e.g., 5-second chunks)
        segment_length = 5 * sr  # 5 seconds
        
        segments = []
        for i in range(0, len(audio), segment_length):
            segment = audio[i:i + segment_length]
            if len(segment) >= sr:  # At least 1 second
                segments.append(segment)
        
        print(f"📊 Created {len(segments)} segments from original audio")
        
        # Save segments
        metadata_entries = []
        
        for i, segment in enumerate(segments, 1):
            filename = f"speaker1_{i:03d}.wav"
            filepath = f"egyptian_dataset/wavs/{filename}"
            
            # Save segment
            sf.write(filepath, segment, sr)
            
            # Add to metadata (you'll need to transcribe each segment)
            metadata_entries.append({
                'audio_file': filename,
                'text': f'جزء {i} من الصوت المصري',  # Placeholder - replace with actual transcription
                'speaker_name': 'speaker1'
            })
        
        # Update metadata with all segments
        df = pd.DataFrame(metadata_entries)
        df.to_csv("egyptian_dataset/metadata.csv", sep='|', index=False)
        
        print(f"✅ Created {len(segments)} audio segments")
        print("⚠️ You need to transcribe each segment in metadata.csv")
        
    except ImportError:
        print("❌ librosa not installed")
        print("Install with: pip install librosa soundfile")
    except Exception as e:
        print(f"❌ Error creating segments: {str(e)}")

def create_minimal_training_config():
    """Create a minimal training config for quick testing"""
    print("\n⚙️ Creating Minimal Training Config")
    print("=" * 40)
    
    # Minimal config for testing with small dataset
    minimal_config = {
        "model_name": "xtts",
        "run_name": "egyptian_xtts_minimal",
        "run_description": "Minimal XTTS training on Egyptian Arabic",
        
        "datasets": [
            {
                "name": "egyptian_dataset",
                "path": "./egyptian_dataset/",
                "meta_file_train": "metadata.csv",
                "language": "ar"
            }
        ],
        
        "audio": {
            "sample_rate": 22050,
            "output_sample_rate": 24000
        },
        
        "batch_size": 1,  # Minimal for testing
        "eval_batch_size": 1,
        "num_loader_workers": 0,
        "num_eval_loader_workers": 0,
        "run_eval": True,
        "test_delay_epochs": 2,
        
        "epochs": 10,  # Minimal for testing
        "save_step": 100,
        "eval_step": 100,
        "log_step": 10,
        "save_n_checkpoints": 3,
        
        "lr": 1e-05,  # Higher learning rate for faster convergence
        "weight_decay": 1e-06,
        "optimizer": "AdamW",
        
        "output_path": "./egyptian_xtts_minimal/",
        "mixed_precision": False,
        "use_phonemes": False,
        "compute_input_seq_cache": True,
        "precompute_num_workers": 0
    }
    
    import json
    with open("egyptian_dataset/config_minimal.json", "w", encoding="utf-8") as f:
        json.dump(minimal_config, f, indent=2, ensure_ascii=False)
    
    print("✅ Created: config_minimal.json")
    print("🎯 Minimal training config for testing")

def create_quick_start_script():
    """Create a quick start training script"""
    print("\n🚀 Creating Quick Start Script")
    print("=" * 35)
    
    script_content = '''#!/usr/bin/env python3
"""
Quick Start Egyptian Training
Minimal training setup for testing
"""

import os
import sys

def quick_train():
    """Quick training with minimal setup"""
    print("🇪🇬 Quick Egyptian Training")
    print("=" * 30)
    
    # Check if data is ready
    if not os.path.exists("egyptian_dataset/wavs/speaker1_001.wav"):
        print("❌ No audio data found")
        print("Run: python setup_egyptian_training.py first")
        return
    
    try:
        from TTS.tts.configs.xtts_config import XttsConfig
        from TTS.tts.models.xtts import Xtts
        from trainer import Trainer, TrainerArgs
        
        print("🔧 Loading minimal config...")
        config = XttsConfig()
        config.load_json("egyptian_dataset/config_minimal.json")
        
        print("🤖 Initializing model...")
        model = Xtts.init_from_config(config)
        
        print("📥 Loading pretrained weights...")
        model.load_checkpoint(config, checkpoint_dir=None, eval=False)
        
        print("🏃 Starting quick training...")
        print("⏱️ This will run for 10 epochs (about 30 minutes)")
        
        trainer_args = TrainerArgs()
        trainer = Trainer(
            trainer_args,
            config,
            output_path=config.output_path,
            model=model
        )
        
        trainer.fit()
        
        print("🎉 Quick training complete!")
        print("Check results in: egyptian_xtts_minimal/")
        
    except ImportError as e:
        print(f"❌ Missing dependency: {str(e)}")
        print("Install with: pip install trainer")
    except Exception as e:
        print(f"❌ Training error: {str(e)}")

if __name__ == "__main__":
    quick_train()
'''
    
    with open("quick_train_egyptian.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ Created: quick_train_egyptian.py")
    print("🎯 Quick training script for testing")

def main():
    """Main setup function"""
    print("🇪🇬 Egyptian Training Setup")
    print("=" * 30)
    
    print("This will set up training using your existing audio file.")
    print("Steps:")
    print("1. Copy your audio file to training dataset")
    print("2. Create audio segments for more training data")
    print("3. Set up minimal training configuration")
    print("4. Create quick start script")
    
    proceed = input("\nProceed? (y/n): ").strip().lower()
    
    if proceed == 'y':
        setup_with_existing_audio()
        create_minimal_training_config()
        create_quick_start_script()
        
        print("\n🎉 Setup Complete!")
        print("\n📋 Next Steps:")
        print("1. Edit egyptian_dataset/metadata.csv with accurate transcriptions")
        print("2. Install training dependencies: pip install trainer librosa soundfile")
        print("3. Run quick training: python quick_train_egyptian.py")
        print("4. Monitor progress and check results")
        
        print("\n⚠️ Important:")
        print("• Update metadata.csv with actual Arabic transcriptions")
        print("• Training on CPU will be slow (consider using GPU)")
        print("• This is a minimal setup for testing - add more data for better results")
    else:
        print("Setup cancelled.")

if __name__ == "__main__":
    main()
