<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.gfxdraw &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.image" href="image.html" />
    <link rel="prev" title="pygame.freetype" href="freetype.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame.gfxdraw">
<span id="pygame-gfxdraw"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame.gfxdraw</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">pygame module for drawing shapes</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.pixel">pygame.gfxdraw.pixel</a></div>
</td>
<td>—</td>
<td>draw a pixel</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.hline">pygame.gfxdraw.hline</a></div>
</td>
<td>—</td>
<td>draw a horizontal line</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.vline">pygame.gfxdraw.vline</a></div>
</td>
<td>—</td>
<td>draw a vertical line</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.line">pygame.gfxdraw.line</a></div>
</td>
<td>—</td>
<td>draw a line</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.rectangle">pygame.gfxdraw.rectangle</a></div>
</td>
<td>—</td>
<td>draw a rectangle</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.box">pygame.gfxdraw.box</a></div>
</td>
<td>—</td>
<td>draw a filled rectangle</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.circle">pygame.gfxdraw.circle</a></div>
</td>
<td>—</td>
<td>draw a circle</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.aacircle">pygame.gfxdraw.aacircle</a></div>
</td>
<td>—</td>
<td>draw an antialiased circle</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.filled_circle">pygame.gfxdraw.filled_circle</a></div>
</td>
<td>—</td>
<td>draw a filled circle</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.ellipse">pygame.gfxdraw.ellipse</a></div>
</td>
<td>—</td>
<td>draw an ellipse</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.aaellipse">pygame.gfxdraw.aaellipse</a></div>
</td>
<td>—</td>
<td>draw an antialiased ellipse</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.filled_ellipse">pygame.gfxdraw.filled_ellipse</a></div>
</td>
<td>—</td>
<td>draw a filled ellipse</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.arc">pygame.gfxdraw.arc</a></div>
</td>
<td>—</td>
<td>draw an arc</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.pie">pygame.gfxdraw.pie</a></div>
</td>
<td>—</td>
<td>draw a pie</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.trigon">pygame.gfxdraw.trigon</a></div>
</td>
<td>—</td>
<td>draw a trigon/triangle</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.aatrigon">pygame.gfxdraw.aatrigon</a></div>
</td>
<td>—</td>
<td>draw an antialiased trigon/triangle</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.filled_trigon">pygame.gfxdraw.filled_trigon</a></div>
</td>
<td>—</td>
<td>draw a filled trigon/triangle</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.polygon">pygame.gfxdraw.polygon</a></div>
</td>
<td>—</td>
<td>draw a polygon</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.aapolygon">pygame.gfxdraw.aapolygon</a></div>
</td>
<td>—</td>
<td>draw an antialiased polygon</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.filled_polygon">pygame.gfxdraw.filled_polygon</a></div>
</td>
<td>—</td>
<td>draw a filled polygon</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.textured_polygon">pygame.gfxdraw.textured_polygon</a></div>
</td>
<td>—</td>
<td>draw a textured polygon</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="gfxdraw.html#pygame.gfxdraw.bezier">pygame.gfxdraw.bezier</a></div>
</td>
<td>—</td>
<td>draw a Bezier curve</td>
</tr>
</tbody>
</table>
<p><strong>EXPERIMENTAL!</strong>: This API may change or disappear in later pygame releases. If
you use this, your code may break with the next pygame release.</p>
<p>The pygame package does not import gfxdraw automatically when loaded, so it
must imported explicitly to be used.</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">pygame</span>
<span class="kn">import</span> <span class="nn">pygame.gfxdraw</span>
</pre></div>
</div>
<p>For all functions the arguments are strictly positional and integers are
accepted for coordinates and radii. The <code class="docutils literal notranslate"><span class="pre">color</span></code> argument can be one of the
following formats:</p>
<blockquote>
<div><ul class="simple">
<li><p>a <a class="tooltip reference internal" href="color.html#pygame.Color" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.Color</span></code><span class="tooltip-content">pygame object for color representations</span></a> object</p></li>
<li><p>an <code class="docutils literal notranslate"><span class="pre">(RGB)</span></code> triplet (tuple/list)</p></li>
<li><p>an <code class="docutils literal notranslate"><span class="pre">(RGBA)</span></code> quadruplet (tuple/list)</p></li>
</ul>
</div></blockquote>
<p>The functions <a class="reference internal" href="#pygame.gfxdraw.rectangle" title="pygame.gfxdraw.rectangle"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rectangle()</span></code></a> and <a class="reference internal" href="#pygame.gfxdraw.box" title="pygame.gfxdraw.box"><code class="xref py py-meth docutils literal notranslate"><span class="pre">box()</span></code></a> will accept any <code class="docutils literal notranslate"><span class="pre">(x,</span> <span class="pre">y,</span> <span class="pre">w,</span> <span class="pre">h)</span></code>
sequence for their <code class="docutils literal notranslate"><span class="pre">rect</span></code> argument, though <a class="tooltip reference internal" href="rect.html#pygame.Rect" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.Rect</span></code><span class="tooltip-content">pygame object for storing rectangular coordinates</span></a> instances are
preferred.</p>
<p>To draw a filled antialiased shape, first use the antialiased (aa*) version
of the function, and then use the filled (filled_*) version.
For example:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">col</span> <span class="o">=</span> <span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="n">surf</span><span class="o">.</span><span class="n">fill</span><span class="p">((</span><span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">))</span>
<span class="n">pygame</span><span class="o">.</span><span class="n">gfxdraw</span><span class="o">.</span><span class="n">aacircle</span><span class="p">(</span><span class="n">surf</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="n">col</span><span class="p">)</span>
<span class="n">pygame</span><span class="o">.</span><span class="n">gfxdraw</span><span class="o">.</span><span class="n">filled_circle</span><span class="p">(</span><span class="n">surf</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="n">col</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For threading, each of the functions releases the GIL during the C part of
the call.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>See the <a class="tooltip reference internal" href="draw.html#module-pygame.draw" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.draw</span></code><span class="tooltip-content">pygame module for drawing shapes</span></a> module for alternative draw methods.
The <code class="docutils literal notranslate"><span class="pre">pygame.gfxdraw</span></code> module differs from the <a class="tooltip reference internal" href="draw.html#module-pygame.draw" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.draw</span></code><span class="tooltip-content">pygame module for drawing shapes</span></a> module in
the API it uses and the different draw functions available.
<code class="docutils literal notranslate"><span class="pre">pygame.gfxdraw</span></code> wraps the primitives from the library called SDL_gfx,
rather than using modified versions.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 1.9.0.</span></p>
</div>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.pixel">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">pixel</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.pixel" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a pixel</span></div>
<div class="line"><span class="signature">pixel(surface, x, y, color) -&gt; None</span></div>
</div>
<p>Draws a single pixel, at position (x ,y), on the given surface.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the pixel</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the pixel</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.hline">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">hline</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.hline" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a horizontal line</span></div>
<div class="line"><span class="signature">hline(surface, x1, x2, y, color) -&gt; None</span></div>
</div>
<p>Draws a straight horizontal line (<code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y)</span></code>) on the given
surface. There are no endcaps.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x1</strong> (<em>int</em>) -- x coordinate of one end of the line</p></li>
<li><p><strong>x2</strong> (<em>int</em>) -- x coordinate of the other end of the line</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the line</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.vline">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">vline</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.vline" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a vertical line</span></div>
<div class="line"><span class="signature">vline(surface, x, y1, y2, color) -&gt; None</span></div>
</div>
<p>Draws a straight vertical line (<code class="docutils literal notranslate"><span class="pre">(x,</span> <span class="pre">y1)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x,</span> <span class="pre">y2)</span></code>) on the given
surface. There are no endcaps.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the line</p></li>
<li><p><strong>y1</strong> (<em>int</em>) -- y coordinate of one end of the line</p></li>
<li><p><strong>y2</strong> (<em>int</em>) -- y coordinate of the other end of the line</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.line">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">line</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.line" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a line</span></div>
<div class="line"><span class="signature">line(surface, x1, y1, x2, y2, color) -&gt; None</span></div>
</div>
<p>Draws a straight line (<code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code>) on the given surface.
There are no endcaps.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x1</strong> (<em>int</em>) -- x coordinate of one end of the line</p></li>
<li><p><strong>y1</strong> (<em>int</em>) -- y coordinate of one end of the line</p></li>
<li><p><strong>x2</strong> (<em>int</em>) -- x coordinate of the other end of the line</p></li>
<li><p><strong>y2</strong> (<em>int</em>) -- y coordinate of the other end of the line</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.rectangle">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">rectangle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.rectangle" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a rectangle</span></div>
<div class="line"><span class="signature">rectangle(surface, rect, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled rectangle on the given surface. For a filled rectangle use
<a class="reference internal" href="#pygame.gfxdraw.box" title="pygame.gfxdraw.box"><code class="xref py py-meth docutils literal notranslate"><span class="pre">box()</span></code></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>rect</strong> (<a class="reference internal" href="rect.html#pygame.Rect" title="pygame.Rect"><em>Rect</em></a>) -- rectangle to draw, position and dimensions</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">rect.bottom</span></code> and <code class="docutils literal notranslate"><span class="pre">rect.right</span></code> attributes of a <a class="tooltip reference internal" href="rect.html#pygame.Rect" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.Rect</span></code><span class="tooltip-content">pygame object for storing rectangular coordinates</span></a>
always lie one pixel outside of its actual border. Therefore, these
values will not be included as part of the drawing.</p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.box">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">box</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.box" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a filled rectangle</span></div>
<div class="line"><span class="signature">box(surface, rect, color) -&gt; None</span></div>
</div>
<p>Draws a filled rectangle on the given surface. For an unfilled rectangle use
<a class="reference internal" href="#pygame.gfxdraw.rectangle" title="pygame.gfxdraw.rectangle"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rectangle()</span></code></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>rect</strong> (<a class="reference internal" href="rect.html#pygame.Rect" title="pygame.Rect"><em>Rect</em></a>) -- rectangle to draw, position and dimensions</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">rect.bottom</span></code> and <code class="docutils literal notranslate"><span class="pre">rect.right</span></code> attributes of a <a class="tooltip reference internal" href="rect.html#pygame.Rect" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.Rect</span></code><span class="tooltip-content">pygame object for storing rectangular coordinates</span></a>
always lie one pixel outside of its actual border. Therefore, these
values will not be included as part of the drawing.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="tooltip reference internal" href="surface.html#pygame.Surface.fill" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.Surface.fill()</span></code><span class="tooltip-content">fill Surface with a solid color</span></a> method works just as well for drawing
filled rectangles. In fact <a class="tooltip reference internal" href="surface.html#pygame.Surface.fill" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.Surface.fill()</span></code><span class="tooltip-content">fill Surface with a solid color</span></a> can be hardware
accelerated on some platforms with both software and hardware display
modes.</p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.circle">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">circle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.circle" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a circle</span></div>
<div class="line"><span class="signature">circle(surface, x, y, r, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled circle on the given surface. For a filled circle use
<a class="reference internal" href="#pygame.gfxdraw.filled_circle" title="pygame.gfxdraw.filled_circle"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filled_circle()</span></code></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the center of the circle</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the center of the circle</p></li>
<li><p><strong>r</strong> (<em>int</em>) -- radius of the circle</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.aacircle">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">aacircle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.aacircle" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw an antialiased circle</span></div>
<div class="line"><span class="signature">aacircle(surface, x, y, r, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled antialiased circle on the given surface.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the center of the circle</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the center of the circle</p></li>
<li><p><strong>r</strong> (<em>int</em>) -- radius of the circle</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.filled_circle">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">filled_circle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.filled_circle" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a filled circle</span></div>
<div class="line"><span class="signature">filled_circle(surface, x, y, r, color) -&gt; None</span></div>
</div>
<p>Draws a filled circle on the given surface. For an unfilled circle use
<a class="reference internal" href="#pygame.gfxdraw.circle" title="pygame.gfxdraw.circle"><code class="xref py py-meth docutils literal notranslate"><span class="pre">circle()</span></code></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the center of the circle</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the center of the circle</p></li>
<li><p><strong>r</strong> (<em>int</em>) -- radius of the circle</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.ellipse">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">ellipse</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.ellipse" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw an ellipse</span></div>
<div class="line"><span class="signature">ellipse(surface, x, y, rx, ry, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled ellipse on the given surface. For a filled ellipse use
<a class="reference internal" href="#pygame.gfxdraw.filled_ellipse" title="pygame.gfxdraw.filled_ellipse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filled_ellipse()</span></code></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the center of the ellipse</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the center of the ellipse</p></li>
<li><p><strong>rx</strong> (<em>int</em>) -- horizontal radius of the ellipse</p></li>
<li><p><strong>ry</strong> (<em>int</em>) -- vertical radius of the ellipse</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.aaellipse">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">aaellipse</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.aaellipse" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw an antialiased ellipse</span></div>
<div class="line"><span class="signature">aaellipse(surface, x, y, rx, ry, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled antialiased ellipse on the given surface.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the center of the ellipse</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the center of the ellipse</p></li>
<li><p><strong>rx</strong> (<em>int</em>) -- horizontal radius of the ellipse</p></li>
<li><p><strong>ry</strong> (<em>int</em>) -- vertical radius of the ellipse</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.filled_ellipse">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">filled_ellipse</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.filled_ellipse" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a filled ellipse</span></div>
<div class="line"><span class="signature">filled_ellipse(surface, x, y, rx, ry, color) -&gt; None</span></div>
</div>
<p>Draws a filled ellipse on the given surface. For an unfilled ellipse use
<a class="reference internal" href="#pygame.gfxdraw.ellipse" title="pygame.gfxdraw.ellipse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ellipse()</span></code></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the center of the ellipse</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the center of the ellipse</p></li>
<li><p><strong>rx</strong> (<em>int</em>) -- horizontal radius of the ellipse</p></li>
<li><p><strong>ry</strong> (<em>int</em>) -- vertical radius of the ellipse</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.arc">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">arc</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.arc" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw an arc</span></div>
<div class="line"><span class="signature">arc(surface, x, y, r, start_angle, stop_angle, color) -&gt; None</span></div>
</div>
<p>Draws an arc on the given surface. For an arc with its endpoints connected
to its center use <a class="reference internal" href="#pygame.gfxdraw.pie" title="pygame.gfxdraw.pie"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pie()</span></code></a>.</p>
<p>The two angle arguments are given in degrees and indicate the start and stop
positions of the arc. The arc is drawn in a clockwise direction from the
<code class="docutils literal notranslate"><span class="pre">start_angle</span></code> to the <code class="docutils literal notranslate"><span class="pre">stop_angle</span></code>. If <code class="docutils literal notranslate"><span class="pre">start_angle</span> <span class="pre">==</span> <span class="pre">stop_angle</span></code>,
nothing will be drawn</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the center of the arc</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the center of the arc</p></li>
<li><p><strong>r</strong> (<em>int</em>) -- radius of the arc</p></li>
<li><p><strong>start_angle</strong> (<em>int</em>) -- start angle in degrees</p></li>
<li><p><strong>stop_angle</strong> (<em>int</em>) -- stop angle in degrees</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function uses <em>degrees</em> while the <a class="tooltip reference internal" href="draw.html#pygame.draw.arc" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.draw.arc()</span></code><span class="tooltip-content">draw an elliptical arc</span></a> function
uses <em>radians</em>.</p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.pie">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">pie</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.pie" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a pie</span></div>
<div class="line"><span class="signature">pie(surface, x, y, r, start_angle, stop_angle, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled pie on the given surface. A pie is an <a class="reference internal" href="#pygame.gfxdraw.arc" title="pygame.gfxdraw.arc"><code class="xref py py-meth docutils literal notranslate"><span class="pre">arc()</span></code></a> with its
endpoints connected to its center.</p>
<p>The two angle arguments are given in degrees and indicate the start and stop
positions of the pie. The pie is drawn in a clockwise direction from the
<code class="docutils literal notranslate"><span class="pre">start_angle</span></code> to the <code class="docutils literal notranslate"><span class="pre">stop_angle</span></code>. If <code class="docutils literal notranslate"><span class="pre">start_angle</span> <span class="pre">==</span> <span class="pre">stop_angle</span></code>,
a straight line will be drawn from the center position at the given angle,
to a length of the radius.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x</strong> (<em>int</em>) -- x coordinate of the center of the pie</p></li>
<li><p><strong>y</strong> (<em>int</em>) -- y coordinate of the center of the pie</p></li>
<li><p><strong>r</strong> (<em>int</em>) -- radius of the pie</p></li>
<li><p><strong>start_angle</strong> (<em>int</em>) -- start angle in degrees</p></li>
<li><p><strong>stop_angle</strong> (<em>int</em>) -- stop angle in degrees</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.trigon">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">trigon</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.trigon" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a trigon/triangle</span></div>
<div class="line"><span class="signature">trigon(surface, x1, y1, x2, y2, x3, y3, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled trigon (triangle) on the given surface. For a filled
trigon use <a class="reference internal" href="#pygame.gfxdraw.filled_trigon" title="pygame.gfxdraw.filled_trigon"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filled_trigon()</span></code></a>.</p>
<p>A trigon can also be drawn using <a class="reference internal" href="#pygame.gfxdraw.polygon" title="pygame.gfxdraw.polygon"><code class="xref py py-meth docutils literal notranslate"><span class="pre">polygon()</span></code></a> e.g.
<code class="docutils literal notranslate"><span class="pre">polygon(surface,</span> <span class="pre">((x1,</span> <span class="pre">y1),</span> <span class="pre">(x2,</span> <span class="pre">y2),</span> <span class="pre">(x3,</span> <span class="pre">y3)),</span> <span class="pre">color)</span></code></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x1</strong> (<em>int</em>) -- x coordinate of the first corner of the trigon</p></li>
<li><p><strong>y1</strong> (<em>int</em>) -- y coordinate of the first corner of the trigon</p></li>
<li><p><strong>x2</strong> (<em>int</em>) -- x coordinate of the second corner of the trigon</p></li>
<li><p><strong>y2</strong> (<em>int</em>) -- y coordinate of the second corner of the trigon</p></li>
<li><p><strong>x3</strong> (<em>int</em>) -- x coordinate of the third corner of the trigon</p></li>
<li><p><strong>y3</strong> (<em>int</em>) -- y coordinate of the third corner of the trigon</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.aatrigon">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">aatrigon</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.aatrigon" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw an antialiased trigon/triangle</span></div>
<div class="line"><span class="signature">aatrigon(surface, x1, y1, x2, y2, x3, y3, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled antialiased trigon (triangle) on the given surface.</p>
<p>An aatrigon can also be drawn using <a class="reference internal" href="#pygame.gfxdraw.aapolygon" title="pygame.gfxdraw.aapolygon"><code class="xref py py-meth docutils literal notranslate"><span class="pre">aapolygon()</span></code></a> e.g.
<code class="docutils literal notranslate"><span class="pre">aapolygon(surface,</span> <span class="pre">((x1,</span> <span class="pre">y1),</span> <span class="pre">(x2,</span> <span class="pre">y2),</span> <span class="pre">(x3,</span> <span class="pre">y3)),</span> <span class="pre">color)</span></code></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x1</strong> (<em>int</em>) -- x coordinate of the first corner of the trigon</p></li>
<li><p><strong>y1</strong> (<em>int</em>) -- y coordinate of the first corner of the trigon</p></li>
<li><p><strong>x2</strong> (<em>int</em>) -- x coordinate of the second corner of the trigon</p></li>
<li><p><strong>y2</strong> (<em>int</em>) -- y coordinate of the second corner of the trigon</p></li>
<li><p><strong>x3</strong> (<em>int</em>) -- x coordinate of the third corner of the trigon</p></li>
<li><p><strong>y3</strong> (<em>int</em>) -- y coordinate of the third corner of the trigon</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.filled_trigon">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">filled_trigon</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.filled_trigon" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a filled trigon/triangle</span></div>
<div class="line"><span class="signature">filled_trigon(surface, x1, y1, x2, y2, x3, y3, color) -&gt; None</span></div>
</div>
<p>Draws a filled trigon (triangle) on the given surface. For an unfilled
trigon use <a class="reference internal" href="#pygame.gfxdraw.trigon" title="pygame.gfxdraw.trigon"><code class="xref py py-meth docutils literal notranslate"><span class="pre">trigon()</span></code></a>.</p>
<p>A filled_trigon can also be drawn using <a class="reference internal" href="#pygame.gfxdraw.filled_polygon" title="pygame.gfxdraw.filled_polygon"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filled_polygon()</span></code></a> e.g.
<code class="docutils literal notranslate"><span class="pre">filled_polygon(surface,</span> <span class="pre">((x1,</span> <span class="pre">y1),</span> <span class="pre">(x2,</span> <span class="pre">y2),</span> <span class="pre">(x3,</span> <span class="pre">y3)),</span> <span class="pre">color)</span></code></p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>x1</strong> (<em>int</em>) -- x coordinate of the first corner of the trigon</p></li>
<li><p><strong>y1</strong> (<em>int</em>) -- y coordinate of the first corner of the trigon</p></li>
<li><p><strong>x2</strong> (<em>int</em>) -- x coordinate of the second corner of the trigon</p></li>
<li><p><strong>y2</strong> (<em>int</em>) -- y coordinate of the second corner of the trigon</p></li>
<li><p><strong>x3</strong> (<em>int</em>) -- x coordinate of the third corner of the trigon</p></li>
<li><p><strong>y3</strong> (<em>int</em>) -- y coordinate of the third corner of the trigon</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.polygon">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">polygon</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.polygon" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a polygon</span></div>
<div class="line"><span class="signature">polygon(surface, points, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled polygon on the given surface. For a filled polygon use
<a class="reference internal" href="#pygame.gfxdraw.filled_polygon" title="pygame.gfxdraw.filled_polygon"><code class="xref py py-meth docutils literal notranslate"><span class="pre">filled_polygon()</span></code></a>.</p>
<p>The adjacent coordinates in the <code class="docutils literal notranslate"><span class="pre">points</span></code> argument, as well as the first
and last points, will be connected by line segments.
e.g. For the points <code class="docutils literal notranslate"><span class="pre">[(x1,</span> <span class="pre">y1),</span> <span class="pre">(x2,</span> <span class="pre">y2),</span> <span class="pre">(x3,</span> <span class="pre">y3)]</span></code> a line segment will
be drawn from <code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code>, from <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code> to
<code class="docutils literal notranslate"><span class="pre">(x3,</span> <span class="pre">y3)</span></code>, and from <code class="docutils literal notranslate"><span class="pre">(x3,</span> <span class="pre">y3)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>points</strong> (<em>tuple</em><em>(</em><em>coordinate</em><em>) or </em><em>list</em><em>(</em><em>coordinate</em><em>)</em>) -- a sequence of 3 or more (x, y) coordinates, where each
<em>coordinate</em> in the sequence must be a
tuple/list/<a class="tooltip reference internal" href="math.html#pygame.math.Vector2" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.math.Vector2</span></code><span class="tooltip-content">a 2-Dimensional Vector</span></a> of 2 ints/floats (float values
will be truncated)</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>ValueError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(points)</span> <span class="pre">&lt;</span> <span class="pre">3</span></code> (must have at least 3 points)</p></li>
<li><p><strong>IndexError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(coordinate)</span> <span class="pre">&lt;</span> <span class="pre">2</span></code> (each coordinate must have
at least 2 items)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.aapolygon">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">aapolygon</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.aapolygon" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw an antialiased polygon</span></div>
<div class="line"><span class="signature">aapolygon(surface, points, color) -&gt; None</span></div>
</div>
<p>Draws an unfilled antialiased polygon on the given surface.</p>
<p>The adjacent coordinates in the <code class="docutils literal notranslate"><span class="pre">points</span></code> argument, as well as the first
and last points, will be connected by line segments.
e.g. For the points <code class="docutils literal notranslate"><span class="pre">[(x1,</span> <span class="pre">y1),</span> <span class="pre">(x2,</span> <span class="pre">y2),</span> <span class="pre">(x3,</span> <span class="pre">y3)]</span></code> a line segment will
be drawn from <code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code>, from <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code> to
<code class="docutils literal notranslate"><span class="pre">(x3,</span> <span class="pre">y3)</span></code>, and from <code class="docutils literal notranslate"><span class="pre">(x3,</span> <span class="pre">y3)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>points</strong> (<em>tuple</em><em>(</em><em>coordinate</em><em>) or </em><em>list</em><em>(</em><em>coordinate</em><em>)</em>) -- a sequence of 3 or more (x, y) coordinates, where each
<em>coordinate</em> in the sequence must be a
tuple/list/<a class="tooltip reference internal" href="math.html#pygame.math.Vector2" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.math.Vector2</span></code><span class="tooltip-content">a 2-Dimensional Vector</span></a> of 2 ints/floats (float values
will be truncated)</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>ValueError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(points)</span> <span class="pre">&lt;</span> <span class="pre">3</span></code> (must have at least 3 points)</p></li>
<li><p><strong>IndexError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(coordinate)</span> <span class="pre">&lt;</span> <span class="pre">2</span></code> (each coordinate must have
at least 2 items)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.filled_polygon">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">filled_polygon</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.filled_polygon" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a filled polygon</span></div>
<div class="line"><span class="signature">filled_polygon(surface, points, color) -&gt; None</span></div>
</div>
<p>Draws a filled polygon on the given surface. For an unfilled polygon use
<a class="reference internal" href="#pygame.gfxdraw.polygon" title="pygame.gfxdraw.polygon"><code class="xref py py-meth docutils literal notranslate"><span class="pre">polygon()</span></code></a>.</p>
<p>The adjacent coordinates in the <code class="docutils literal notranslate"><span class="pre">points</span></code> argument, as well as the first
and last points, will be connected by line segments.
e.g. For the points <code class="docutils literal notranslate"><span class="pre">[(x1,</span> <span class="pre">y1),</span> <span class="pre">(x2,</span> <span class="pre">y2),</span> <span class="pre">(x3,</span> <span class="pre">y3)]</span></code> a line segment will
be drawn from <code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code>, from <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code> to
<code class="docutils literal notranslate"><span class="pre">(x3,</span> <span class="pre">y3)</span></code>, and from <code class="docutils literal notranslate"><span class="pre">(x3,</span> <span class="pre">y3)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>points</strong> (<em>tuple</em><em>(</em><em>coordinate</em><em>) or </em><em>list</em><em>(</em><em>coordinate</em><em>)</em>) -- a sequence of 3 or more (x, y) coordinates, where each
<em>coordinate</em> in the sequence must be a
tuple/list/<a class="tooltip reference internal" href="math.html#pygame.math.Vector2" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.math.Vector2</span></code><span class="tooltip-content">a 2-Dimensional Vector</span></a> of 2 ints/floats (float values
will be truncated)`</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>ValueError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(points)</span> <span class="pre">&lt;</span> <span class="pre">3</span></code> (must have at least 3 points)</p></li>
<li><p><strong>IndexError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(coordinate)</span> <span class="pre">&lt;</span> <span class="pre">2</span></code> (each coordinate must have
at least 2 items)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.textured_polygon">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">textured_polygon</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.textured_polygon" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a textured polygon</span></div>
<div class="line"><span class="signature">textured_polygon(surface, points, texture, tx, ty) -&gt; None</span></div>
</div>
<p>Draws a textured polygon on the given surface. For better performance, the
surface and the texture should have the same format.</p>
<p>A per-pixel alpha texture blit to a per-pixel alpha surface will differ from
a <a class="tooltip reference internal" href="surface.html#pygame.Surface.blit" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.Surface.blit()</span></code><span class="tooltip-content">draw one image onto another</span></a> blit. Also, a per-pixel alpha texture cannot be
used with an 8-bit per pixel destination.</p>
<p>The adjacent coordinates in the <code class="docutils literal notranslate"><span class="pre">points</span></code> argument, as well as the first
and last points, will be connected by line segments.
e.g. For the points <code class="docutils literal notranslate"><span class="pre">[(x1,</span> <span class="pre">y1),</span> <span class="pre">(x2,</span> <span class="pre">y2),</span> <span class="pre">(x3,</span> <span class="pre">y3)]</span></code> a line segment will
be drawn from <code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code>, from <code class="docutils literal notranslate"><span class="pre">(x2,</span> <span class="pre">y2)</span></code> to
<code class="docutils literal notranslate"><span class="pre">(x3,</span> <span class="pre">y3)</span></code>, and from <code class="docutils literal notranslate"><span class="pre">(x3,</span> <span class="pre">y3)</span></code> to <code class="docutils literal notranslate"><span class="pre">(x1,</span> <span class="pre">y1)</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>points</strong> (<em>tuple</em><em>(</em><em>coordinate</em><em>) or </em><em>list</em><em>(</em><em>coordinate</em><em>)</em>) -- a sequence of 3 or more (x, y) coordinates, where each
<em>coordinate</em> in the sequence must be a
tuple/list/<a class="tooltip reference internal" href="math.html#pygame.math.Vector2" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.math.Vector2</span></code><span class="tooltip-content">a 2-Dimensional Vector</span></a> of 2 ints/floats (float values
will be truncated)</p></li>
<li><p><strong>texture</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- texture to draw on the polygon</p></li>
<li><p><strong>tx</strong> (<em>int</em>) -- x offset of the texture</p></li>
<li><p><strong>ty</strong> (<em>int</em>) -- y offset of the texture</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>ValueError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(points)</span> <span class="pre">&lt;</span> <span class="pre">3</span></code> (must have at least 3 points)</p></li>
<li><p><strong>IndexError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(coordinate)</span> <span class="pre">&lt;</span> <span class="pre">2</span></code> (each coordinate must have
at least 2 items)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.gfxdraw.bezier">
<span class="sig-prename descclassname"><span class="pre">pygame.gfxdraw.</span></span><span class="sig-name descname"><span class="pre">bezier</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.gfxdraw.bezier" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">draw a Bezier curve</span></div>
<div class="line"><span class="signature">bezier(surface, points, steps, color) -&gt; None</span></div>
</div>
<p>Draws a Bézier curve on the given surface.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>surface</strong> (<a class="reference internal" href="surface.html#pygame.Surface" title="pygame.Surface"><em>Surface</em></a>) -- surface to draw on</p></li>
<li><p><strong>points</strong> (<em>tuple</em><em>(</em><em>coordinate</em><em>) or </em><em>list</em><em>(</em><em>coordinate</em><em>)</em>) -- a sequence of 3 or more (x, y) coordinates used to form a
curve, where each <em>coordinate</em> in the sequence must be a
tuple/list/<a class="tooltip reference internal" href="math.html#pygame.math.Vector2" title=""><code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.math.Vector2</span></code><span class="tooltip-content">a 2-Dimensional Vector</span></a> of 2 ints/floats (float values
will be truncated)</p></li>
<li><p><strong>steps</strong> (<em>int</em>) -- number of steps for the interpolation, the minimum is 2</p></li>
<li><p><strong>color</strong> (<a class="reference internal" href="color.html#pygame.Color" title="pygame.Color"><em>Color</em></a><em> or </em><em>tuple</em><em>(</em><em>int</em><em>, </em><em>int</em><em>, </em><em>int</em><em>, </em><em>[</em><em>int</em><em>]</em><em>)</em>) -- color to draw with, the alpha value is optional if using a
tuple <code class="docutils literal notranslate"><span class="pre">(RGB[A])</span></code></p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>NoneType</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><strong>ValueError</strong> -- if <code class="docutils literal notranslate"><span class="pre">steps</span> <span class="pre">&lt;</span> <span class="pre">2</span></code></p></li>
<li><p><strong>ValueError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(points)</span> <span class="pre">&lt;</span> <span class="pre">3</span></code> (must have at least 3 points)</p></li>
<li><p><strong>IndexError</strong> -- if <code class="docutils literal notranslate"><span class="pre">len(coordinate)</span> <span class="pre">&lt;</span> <span class="pre">2</span></code> (each coordinate must have
at least 2 items)</p></li>
</ul>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref\gfxdraw.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="image.html" title="pygame.image"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="freetype.html" title="pygame.freetype"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.gfxdraw</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>