import os
import sys
import time
import threading
import tkinter as tk
from tkinter import filedialog, ttk, scrolledtext
from tkinter import messagebox
import pygame

# Try to import TTS
try:
    from TTS.api import TTS
except ImportError:
    pass  # We'll handle this in the GUI

class XTTS_GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("XTTS Voice Generator")
        self.root.geometry("800x700")
        self.root.minsize(800, 700)
        
        # Initialize pygame mixer for audio playback
        pygame.mixer.init()
        
        # Model loading status
        self.model_loaded = False
        self.tts = None
        self.available_languages = []
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.tab_control = ttk.Notebook(main_frame)
        
        # Main tab
        self.main_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.main_tab, text="Text to Speech")
        
        # Settings tab
        self.settings_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.settings_tab, text="Settings")
        
        self.tab_control.pack(expand=True, fill=tk.BOTH)
        
        # Setup main tab
        self.setup_main_tab()
        
        # Setup settings tab
        self.setup_settings_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready. Click 'Load Model' to start.")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Check if TTS is installed
        self.check_tts_installed()

    def check_tts_installed(self):
        try:
            import TTS
            self.status_var.set("TTS is installed. Ready to load model.")
        except ImportError:
            self.status_var.set("TTS is not installed. Please install it first.")
            messagebox.showerror("Error", "TTS package is not installed. Please run: pip install TTS torch torchaudio")

    def setup_main_tab(self):
        frame = ttk.Frame(self.main_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Model section
        model_frame = ttk.LabelFrame(frame, text="Model", padding="10")
        model_frame.pack(fill=tk.X, pady=5)
        
        load_btn = ttk.Button(model_frame, text="Load Model", command=self.load_model)
        load_btn.pack(side=tk.LEFT, padx=5)
        
        self.model_status = ttk.Label(model_frame, text="Not loaded")
        self.model_status.pack(side=tk.LEFT, padx=5)
        
        # Voice sample section
        voice_frame = ttk.LabelFrame(frame, text="Voice Sample", padding="10")
        voice_frame.pack(fill=tk.X, pady=5)
        
        self.voice_path_var = tk.StringVar()
        voice_entry = ttk.Entry(voice_frame, textvariable=self.voice_path_var, width=50)
        voice_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        browse_btn = ttk.Button(voice_frame, text="Browse", command=self.browse_voice)
        browse_btn.pack(side=tk.LEFT, padx=5)
        
        # Language selection
        lang_frame = ttk.LabelFrame(frame, text="Language", padding="10")
        lang_frame.pack(fill=tk.X, pady=5)
        
        self.language_var = tk.StringVar(value="en")
        self.language_combo = ttk.Combobox(lang_frame, textvariable=self.language_var, state="readonly")
        self.language_combo['values'] = ["en", "ar"]  # Default values, will be updated when model loads
        self.language_combo.pack(fill=tk.X, padx=5)
        
        # Text input
        text_frame = ttk.LabelFrame(frame, text="Text Input", padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.text_input = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, height=10)
        self.text_input.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Sample texts
        samples_frame = ttk.LabelFrame(frame, text="Sample Texts", padding="10")
        samples_frame.pack(fill=tk.X, pady=5)
        
        en_sample_btn = ttk.Button(samples_frame, text="English Sample", 
                                   command=lambda: self.load_sample_text("en"))
        en_sample_btn.pack(side=tk.LEFT, padx=5)
        
        ar_sample_btn = ttk.Button(samples_frame, text="Arabic Sample", 
                                   command=lambda: self.load_sample_text("ar"))
        ar_sample_btn.pack(side=tk.LEFT, padx=5)
        
        # Output section
        output_frame = ttk.LabelFrame(frame, text="Output", padding="10")
        output_frame.pack(fill=tk.X, pady=5)
        
        self.output_path_var = tk.StringVar(value="output/output.wav")
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, width=50)
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        output_browse_btn = ttk.Button(output_frame, text="Browse", command=self.browse_output)
        output_browse_btn.pack(side=tk.LEFT, padx=5)
        
        # Action buttons
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X, pady=10)
        
        generate_btn = ttk.Button(btn_frame, text="Generate Speech", command=self.generate_speech)
        generate_btn.pack(side=tk.LEFT, padx=5)
        
        play_btn = ttk.Button(btn_frame, text="Play", command=self.play_audio)
        play_btn.pack(side=tk.LEFT, padx=5)
        
        stop_btn = ttk.Button(btn_frame, text="Stop", command=self.stop_audio)
        stop_btn.pack(side=tk.LEFT, padx=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(frame, orient=tk.HORIZONTAL, length=100, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=5)

    def setup_settings_tab(self):
        frame = ttk.Frame(self.settings_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # GPU settings
        gpu_frame = ttk.LabelFrame(frame, text="GPU Settings", padding="10")
        gpu_frame.pack(fill=tk.X, pady=5)
        
        self.use_gpu_var = tk.BooleanVar(value=True)
        gpu_check = ttk.Checkbutton(gpu_frame, text="Use GPU (if available)", variable=self.use_gpu_var)
        gpu_check.pack(anchor=tk.W, padx=5)
        
        # Output directory
        dir_frame = ttk.LabelFrame(frame, text="Default Output Directory", padding="10")
        dir_frame.pack(fill=tk.X, pady=5)
        
        self.output_dir_var = tk.StringVar(value="output")
        dir_entry = ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=50)
        dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        dir_browse_btn = ttk.Button(dir_frame, text="Browse", 
                                   command=lambda: self.output_dir_var.set(filedialog.askdirectory()))
        dir_browse_btn.pack(side=tk.LEFT, padx=5)
        
        # Create directory if it doesn't exist
        create_dir_btn = ttk.Button(dir_frame, text="Create Directory", 
                                   command=lambda: os.makedirs(self.output_dir_var.get(), exist_ok=True))
        create_dir_btn.pack(side=tk.LEFT, padx=5)
        
        # Model selection
        model_frame = ttk.LabelFrame(frame, text="Model Selection", padding="10")
        model_frame.pack(fill=tk.X, pady=5)
        
        self.model_var = tk.StringVar(value="tts_models/multilingual/multi-dataset/xtts_v2")
        model_entry = ttk.Entry(model_frame, textvariable=self.model_var, width=50)
        model_entry.pack(fill=tk.X, padx=5, pady=5)
        
        model_note = ttk.Label(model_frame, 
                              text="Note: Standard model is 'tts_models/multilingual/multi-dataset/xtts_v2'")
        model_note.pack(anchor=tk.W, padx=5)
        
        # Save settings button
        save_btn = ttk.Button(frame, text="Save Settings", command=self.save_settings)
        save_btn.pack(anchor=tk.E, pady=10)
        
        # About section
        about_frame = ttk.LabelFrame(frame, text="About", padding="10")
        about_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        about_text = scrolledtext.ScrolledText(about_frame, wrap=tk.WORD, height=10)
        about_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        about_text.insert(tk.END, "XTTS Voice Generator\n\n"
                         "This application uses Coqui TTS's XTTS model to generate realistic speech "
                         "in multiple languages including English and Arabic.\n\n"
                         "XTTS can clone voices from short audio samples and generate speech in "
                         "different languages while preserving the voice characteristics.\n\n"
                         "First time use: Click 'Load Model' to download the XTTS model (about 5GB).\n"
                         "This will only happen once.\n\n"
                         "For best results, use a clear voice sample in WAV format (16kHz, mono).\n\n"
                         "Version 1.0 - May 2025")
        about_text.config(state=tk.DISABLED)

    def load_model(self):
        if self.model_loaded:
            messagebox.showinfo("Info", "Model is already loaded.")
            return
            
        self.status_var.set("Loading model... This may take a few minutes.")
        self.progress.start()
        
        # Run in a separate thread to avoid freezing the UI
        threading.Thread(target=self._load_model_thread, daemon=True).start()
    
    def _load_model_thread(self):
        try:
            # Make sure output directory exists
            os.makedirs("output", exist_ok=True)
            
            # Show terms and conditions dialog before loading the model
            self.root.after(0, self._show_terms_dialog)
        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to load model: {error_msg}"))
            self.root.after(0, lambda: self.status_var.set("Error loading model."))
            self.root.after(0, lambda: self.progress.stop())
    
    def _show_terms_dialog(self):
        # Create terms and conditions dialog
        terms_dialog = tk.Toplevel(self.root)
        terms_dialog.title("Terms of Service")
        terms_dialog.geometry("600x500")
        terms_dialog.transient(self.root)
        terms_dialog.grab_set()
        
        # Terms and conditions text
        terms_frame = ttk.Frame(terms_dialog, padding="10")
        terms_frame.pack(fill=tk.BOTH, expand=True)
        
        terms_label = ttk.Label(terms_frame, text="XTTS Terms of Service", font=("Arial", 14, "bold"))
        agree_check = ttk.Checkbutton(terms_frame, text="I agree to the terms and conditions", variable=self.agree_var)
        agree_check.pack(pady=10)
        
        # Buttons
        button_frame = ttk.Frame(terms_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        agree_btn = ttk.Button(button_frame, text="Continue", command=lambda: self._handle_terms_agreement(terms_dialog))
        agree_btn.pack(side=tk.RIGHT, padx=10)
        
        cancel_btn = ttk.Button(button_frame, text="Cancel", command=lambda: self._cancel_model_loading(terms_dialog))
        cancel_btn.pack(side=tk.RIGHT, padx=10)
    
    def _handle_terms_agreement(self, dialog):
        if not self.agree_var.get():
            messagebox.showerror("Error", "You must agree to the terms of service to use this model.")
            return
        
        dialog.destroy()
        
        # Continue with model loading
        try:
            # Load the model
            model_path = self.model_var.get()
            use_gpu = self.use_gpu_var.get()
            
            self.tts = TTS(model_path, gpu=use_gpu)
            
            # Update available languages
            self.available_languages = self.tts.languages
            self.root.after(0, lambda: self.language_combo.config(values=self.available_languages))
            
            self.model_loaded = True
            
            # Update UI from the main thread
            self.root.after(0, lambda: self.model_status.config(text="Loaded"))
            self.root.after(0, lambda: self.status_var.set("Model loaded successfully."))
            self.root.after(0, lambda: self.progress.stop())
        except Exception as e:
            error_msg = str(e)
            messagebox.showerror("Error", f"Failed to load model: {error_msg}")
            self.status_var.set("Error loading model.")
            self.progress.stop()
    
    def _cancel_model_loading(self, dialog):
        dialog.destroy()
        self.status_var.set("Model loading canceled.")
        self.progress.stop()
        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to load model: {error_msg}"))
            self.root.after(0, lambda: self.status_var.set("Error loading model."))
            self.root.after(0, lambda: self.progress.stop())

    def browse_voice(self):
        file_path = filedialog.askopenfilename(filetypes=[("WAV files", "*.wav")])
        if file_path:
            self.voice_path_var.set(file_path)

    def browse_output(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".wav",
            filetypes=[("WAV files", "*.wav")],
            initialdir=self.output_dir_var.get(),
            initialfile="output.wav"
        )
        if file_path:
            self.output_path_var.set(file_path)

    def load_sample_text(self, language):
        if language == "en":
            sample = "Hello, this is a sample text for testing the XTTS voice generation system. It can generate realistic speech in multiple languages while preserving the voice characteristics."
        elif language == "ar":
            sample = "مرحبا، هذا نص تجريبي لاختبار نظام توليد الصوت XTTS. يمكنه إنشاء كلام واقعي بلغات متعددة مع الحفاظ على خصائص الصوت."
        else:
            return
            
        self.text_input.delete(1.0, tk.END)
        self.text_input.insert(tk.END, sample)
        self.language_var.set(language)

    def generate_speech(self):
        if not self.model_loaded:
            messagebox.showerror("Error", "Please load the model first.")
            return
            
        voice_path = self.voice_path_var.get()
        if not voice_path:
            messagebox.showerror("Error", "Please select a voice sample.")
            return
            
        text = self.text_input.get(1.0, tk.END).strip()
        if not text:
            messagebox.showerror("Error", "Please enter some text.")
            return
            
        language = self.language_var.get()
        output_path = self.output_path_var.get()
        
        # Make sure output directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        self.status_var.set("Generating speech... Please wait.")
        self.progress.start()
        
        # Run in a separate thread
        threading.Thread(target=self._generate_speech_thread, 
                        args=(text, voice_path, language, output_path), 
                        daemon=True).start()

    def _generate_speech_thread(self, text, voice_path, language, output_path):
        try:
            start_time = time.time()
            
            self.tts.tts_to_file(
                text=text,
                speaker_wav=voice_path,
                language=language,
                file_path=output_path
            )
            
            generation_time = time.time() - start_time
            
            # Update UI from the main thread
            self.root.after(0, lambda: self.status_var.set(
                f"Speech generated in {generation_time:.2f} seconds. Saved to {output_path}"))
            self.root.after(0, lambda: self.progress.stop())
            self.root.after(0, lambda: messagebox.showinfo("Success", 
                                                         f"Speech generated successfully in {generation_time:.2f} seconds."))
            
        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to generate speech: {error_msg}"))
            self.root.after(0, lambda: self.status_var.set("Error generating speech."))
            self.root.after(0, lambda: self.progress.stop())

    def play_audio(self):
        output_path = self.output_path_var.get()
        if not os.path.exists(output_path):
            messagebox.showerror("Error", f"File not found: {output_path}")
            return
            
        try:
            pygame.mixer.music.load(output_path)
            pygame.mixer.music.play()
            self.status_var.set(f"Playing: {output_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to play audio: {str(e)}")

    def stop_audio(self):
        if pygame.mixer.music.get_busy():
            pygame.mixer.music.stop()
            self.status_var.set("Audio playback stopped.")

    def save_settings(self):
        # In a real app, we would save these to a config file
        messagebox.showinfo("Settings", "Settings saved.")
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir_var.get(), exist_ok=True)

if __name__ == "__main__":
    # Create output directories
    os.makedirs("output", exist_ok=True)
    os.makedirs("samples", exist_ok=True)
    
    # Start the GUI
    root = tk.Tk()
    app = XTTS_GUI(root)
    root.mainloop()
