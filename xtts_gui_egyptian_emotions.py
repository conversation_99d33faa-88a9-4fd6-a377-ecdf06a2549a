#!/usr/bin/env python3
"""
XTTS GUI with Egyptian Accent and Emotion Controls
Enhanced version with Arabic language support, Egyptian accent, and emotion parameters
"""

import os
import sys
import time
import threading
import logging
import gc
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from contextlib import contextmanager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xtts_gui.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@contextmanager
def nostdout():
    """Context manager to suppress stdout"""
    try:
        with open(os.devnull, "w") as devnull:
            old_stdout = sys.stdout
            sys.stdout = devnull
            try:
                yield
            finally:
                sys.stdout = old_stdout
    except Exception:
        # Fallback if devnull doesn't work
        yield

def check_ffmpeg():
    """Check if FFmpeg is available"""
    try:
        import subprocess

        # First check if FFmpeg is in PATH
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info(f"FFmpeg check return code: {result.returncode}")
            logger.info(f"FFmpeg check stdout: {result.stdout[:500]}")
            logger.info(f"FFmpeg check stderr: {result.stderr}")
            logger.info("FFmpeg found: " + result.stdout.split('\n')[0])
            return True
        else:
            logger.error(f"FFmpeg check failed with return code: {result.returncode}")
            return False
    except FileNotFoundError:
        logger.error("FFmpeg not found in PATH")
        # Try to add local FFmpeg to PATH
        try:
            ffmpeg_path = os.path.join(os.environ.get('LOCALAPPDATA', ''), 'ffmpeg', 'bin')
            if os.path.exists(ffmpeg_path):
                current_path = os.environ.get('PATH', '')
                if ffmpeg_path not in current_path:
                    os.environ['PATH'] = ffmpeg_path + os.pathsep + current_path
                    logger.info(f"Adding FFmpeg path to environment: {ffmpeg_path}")
                    # Try again
                    return check_ffmpeg()
        except Exception as e:
            logger.error(f"Error adding FFmpeg to PATH: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Error checking FFmpeg: {str(e)}")
        return False

def install_ffmpeg():
    """Install FFmpeg automatically"""
    try:
        import urllib.request
        import zipfile
        import shutil

        logger.info("Starting FFmpeg installation...")

        # Create FFmpeg directory
        ffmpeg_dir = os.path.join(os.environ['LOCALAPPDATA'], 'ffmpeg')
        bin_dir = os.path.join(ffmpeg_dir, 'bin')
        os.makedirs(bin_dir, exist_ok=True)

        # Download FFmpeg
        url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
        zip_path = os.path.join(ffmpeg_dir, 'ffmpeg.zip')

        logger.info("Downloading FFmpeg...")
        urllib.request.urlretrieve(url, zip_path)

        # Extract FFmpeg
        logger.info("Extracting FFmpeg...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(ffmpeg_dir)

        # Find the extracted folder and copy binaries
        for item in os.listdir(ffmpeg_dir):
            item_path = os.path.join(ffmpeg_dir, item)
            if os.path.isdir(item_path) and item.startswith('ffmpeg-'):
                src_bin = os.path.join(item_path, 'bin')
                if os.path.exists(src_bin):
                    for file in os.listdir(src_bin):
                        shutil.copy2(os.path.join(src_bin, file), bin_dir)
                break

        # Add to PATH
        current_path = os.environ.get('PATH', '')
        if bin_dir not in current_path:
            os.environ['PATH'] = bin_dir + os.pathsep + current_path
            logger.info(f"Adding FFmpeg path to environment: {bin_dir}")

        # Clean up
        os.remove(zip_path)

        # Verify installation
        if check_ffmpeg():
            logger.info("FFmpeg installed successfully")
            return True
        else:
            logger.error("FFmpeg installation verification failed")
            return False

    except Exception as e:
        logger.error(f"FFmpeg installation failed: {str(e)}")
        return False

class XTTS_GUI_Egyptian:
    def __init__(self, root):
        self.root = root
        self.root.title("XTTS Text-to-Speech GUI - Egyptian Accent & Emotions")
        self.root.geometry("900x800")
        self.root.minsize(900, 800)

        # Initialize variables
        self.model_loaded = False
        self.tts = None
        self.voice_sample_path = None
        self.last_audio_file = None
        self.pygame_initialized = False
        self.model_name = "tts_models/multilingual/multi-dataset/xtts_v2"

        # Emotion presets for Egyptian Arabic
        self.emotion_presets = {
            "Neutral": {"temperature": 0.7, "length_penalty": 1.0, "repetition_penalty": 2.0, "top_k": 50, "top_p": 0.8},
            "Happy": {"temperature": 0.9, "length_penalty": 0.8, "repetition_penalty": 1.8, "top_k": 60, "top_p": 0.9},
            "Sad": {"temperature": 0.5, "length_penalty": 1.2, "repetition_penalty": 2.2, "top_k": 40, "top_p": 0.7},
            "Angry": {"temperature": 1.0, "length_penalty": 0.7, "repetition_penalty": 1.5, "top_k": 70, "top_p": 0.95},
            "Excited": {"temperature": 1.1, "length_penalty": 0.6, "repetition_penalty": 1.6, "top_k": 80, "top_p": 0.95},
            "Calm": {"temperature": 0.4, "length_penalty": 1.3, "repetition_penalty": 2.5, "top_k": 30, "top_p": 0.6},
            "Dramatic": {"temperature": 1.2, "length_penalty": 0.5, "repetition_penalty": 1.4, "top_k": 90, "top_p": 0.98}
        }

        # Egyptian Arabic sample texts
        self.sample_texts = {
            "Egyptian": "أهلاً وسهلاً! إزيك النهاردة؟ أنا بتكلم بالمصري الأصيل. القاهرة مدينة جميلة جداً والناس فيها طيبين قوي.",
            "Levantine": "مرحبا! كيفك اليوم؟ أنا بحكي باللهجة الشامية. دمشق وبيروت مدن حلوة كتير.",
            "Gulf": "السلام عليكم! شلونك اليوم؟ أنا أتكلم باللهجة الخليجية. الكويت والإمارات دول حلوة.",
            "Standard": "السلام عليكم ورحمة الله وبركاته. كيف حالكم اليوم؟ أتمنى أن تكونوا بخير وعافية."
        }

        self._setup_ui()
        self._init_pygame()

        logger.info("Starting XTTS GUI application")

    def _setup_ui(self):
        """Setup the user interface"""
        # Create main frame with scrollbar
        main_canvas = tk.Canvas(self.root)
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=main_canvas.yview)
        self.main_frame = ttk.Frame(main_canvas)

        self.main_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=self.main_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Title
        title_label = ttk.Label(
            self.main_frame,
            text="XTTS Text-to-Speech - Egyptian Accent & Emotions",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=10)

        # Status display
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_label = ttk.Label(
            self.main_frame,
            textvariable=self.status_var,
            wraplength=700
        )
        self.status_label.pack(pady=10)

        # Progress label
        self.progress_var = tk.StringVar()
        self.progress_label = ttk.Label(
            self.main_frame,
            textvariable=self.progress_var,
            wraplength=700
        )
        self.progress_label.pack(pady=5)

        self._setup_model_frame()
        self._setup_voice_frame()
        self._setup_language_frame()
        self._setup_emotion_frame()
        self._setup_text_frame()
        self._setup_generation_frame()
        self._setup_buttons_frame()

        # Progress bar (initially hidden)
        self.progress = ttk.Progressbar(
            self.main_frame,
            orient=tk.HORIZONTAL,
            length=400,
            mode='determinate'
        )

        # Initialize UI state
        self._update_ui_state()

        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        logger.info("GUI initialization complete")

    def _setup_model_frame(self):
        """Setup model selection frame"""
        model_frame = ttk.LabelFrame(self.main_frame, text="Model Selection", padding="10")
        model_frame.pack(fill=tk.X, pady=10, padx=10)

        # Available models
        self.available_models = [
            "tts_models/multilingual/multi-dataset/xtts_v2",
            "tts_models/en/ljspeech/tacotron2-DDC",
            "tts_models/en/ljspeech/glow-tts"
        ]

        ttk.Label(model_frame, text="Select Model:").pack(anchor=tk.W)
        self.model_var = tk.StringVar(value=self.model_name)
        model_combo = ttk.Combobox(
            model_frame,
            textvariable=self.model_var,
            values=self.available_models,
            state="readonly",
            width=60
        )
        model_combo.pack(fill=tk.X, pady=5)
        model_combo.bind('<<ComboboxSelected>>', self._on_model_changed)

        # Load Model Button
        self.load_btn = ttk.Button(
            model_frame,
            text="Load Selected Model",
            command=self._show_terms_dialog
        )
        self.load_btn.pack(pady=10)

    def _setup_voice_frame(self):
        """Setup voice sample selection frame"""
        sample_frame = ttk.LabelFrame(self.main_frame, text="Voice Sample", padding="10")
        sample_frame.pack(fill=tk.X, pady=10, padx=10)

        self.sample_path_var = tk.StringVar()
        ttk.Label(sample_frame, text="Voice Sample Path:").pack(anchor=tk.W)

        path_frame = ttk.Frame(sample_frame)
        path_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(path_frame, textvariable=self.sample_path_var, state='readonly').pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        ttk.Button(
            path_frame,
            text="Browse...",
            command=self._browse_sample
        ).pack(side=tk.LEFT)

    def _setup_language_frame(self):
        """Setup language and accent selection frame"""
        lang_frame = ttk.LabelFrame(self.main_frame, text="Language & Accent", padding="10")
        lang_frame.pack(fill=tk.X, pady=10, padx=10)

        # Language selection
        lang_row1 = ttk.Frame(lang_frame)
        lang_row1.pack(fill=tk.X, pady=5)

        ttk.Label(lang_row1, text="Language:").pack(side=tk.LEFT)
        self.language_var = tk.StringVar(value="ar")  # Default to Arabic
        language_combo = ttk.Combobox(
            lang_row1,
            textvariable=self.language_var,
            values=["ar", "en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "zh-cn", "ja", "hu", "ko", "hi"],
            state="readonly",
            width=15
        )
        language_combo.pack(side=tk.LEFT, padx=(10, 20))

        # Accent selection (for Arabic)
        ttk.Label(lang_row1, text="Arabic Accent:").pack(side=tk.LEFT)
        self.accent_var = tk.StringVar(value="Egyptian")
        self.accent_combo = ttk.Combobox(
            lang_row1,
            textvariable=self.accent_var,
            values=["Egyptian", "Levantine", "Gulf", "Maghrebi", "Standard"],
            state="readonly",
            width=15
        )
        self.accent_combo.pack(side=tk.LEFT, padx=(10, 20))

        # Sample text button
        ttk.Button(
            lang_row1,
            text="Load Sample Text",
            command=self._load_sample_text
        ).pack(side=tk.LEFT, padx=(10, 0))

        # Bind language change to update accent visibility
        language_combo.bind('<<ComboboxSelected>>', self._on_language_change)

    def _setup_emotion_frame(self):
        """Setup emotion control frame"""
        emotion_frame = ttk.LabelFrame(self.main_frame, text="Emotion & Voice Control", padding="10")
        emotion_frame.pack(fill=tk.X, pady=10, padx=10)

        # Emotion preset selection
        preset_row = ttk.Frame(emotion_frame)
        preset_row.pack(fill=tk.X, pady=5)

        ttk.Label(preset_row, text="Emotion Preset:").pack(side=tk.LEFT)
        self.emotion_var = tk.StringVar(value="Neutral")
        emotion_combo = ttk.Combobox(
            preset_row,
            textvariable=self.emotion_var,
            values=list(self.emotion_presets.keys()),
            state="readonly",
            width=15
        )
        emotion_combo.pack(side=tk.LEFT, padx=(10, 20))
        emotion_combo.bind('<<ComboboxSelected>>', self._on_emotion_change)

        # Custom controls frame
        controls_frame = ttk.LabelFrame(emotion_frame, text="Advanced Controls", padding="5")
        controls_frame.pack(fill=tk.X, pady=10)

        # Temperature control
        temp_frame = ttk.Frame(controls_frame)
        temp_frame.pack(fill=tk.X, pady=2)
        ttk.Label(temp_frame, text="Temperature (Creativity):", width=20).pack(side=tk.LEFT, anchor=tk.W)
        self.temperature_var = tk.DoubleVar(value=0.7)
        temp_scale = ttk.Scale(
            temp_frame,
            from_=0.1,
            to=1.5,
            variable=self.temperature_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        temp_scale.pack(side=tk.LEFT, padx=(10, 5))
        self.temp_label = ttk.Label(temp_frame, text="0.7")
        self.temp_label.pack(side=tk.LEFT)
        temp_scale.bind("<Motion>", self._update_temp_label)

        # Length penalty control
        length_frame = ttk.Frame(controls_frame)
        length_frame.pack(fill=tk.X, pady=2)
        ttk.Label(length_frame, text="Length Penalty:", width=20).pack(side=tk.LEFT, anchor=tk.W)
        self.length_penalty_var = tk.DoubleVar(value=1.0)
        length_scale = ttk.Scale(
            length_frame,
            from_=0.5,
            to=2.0,
            variable=self.length_penalty_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        length_scale.pack(side=tk.LEFT, padx=(10, 5))
        self.length_label = ttk.Label(length_frame, text="1.0")
        self.length_label.pack(side=tk.LEFT)
        length_scale.bind("<Motion>", self._update_length_label)

        # Repetition penalty control
        rep_frame = ttk.Frame(controls_frame)
        rep_frame.pack(fill=tk.X, pady=2)
        ttk.Label(rep_frame, text="Repetition Penalty:", width=20).pack(side=tk.LEFT, anchor=tk.W)
        self.repetition_penalty_var = tk.DoubleVar(value=2.0)
        rep_scale = ttk.Scale(
            rep_frame,
            from_=1.0,
            to=3.0,
            variable=self.repetition_penalty_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        rep_scale.pack(side=tk.LEFT, padx=(10, 5))
        self.rep_label = ttk.Label(rep_frame, text="2.0")
        self.rep_label.pack(side=tk.LEFT)
        rep_scale.bind("<Motion>", self._update_rep_label)

        # Speed control
        speed_frame = ttk.Frame(controls_frame)
        speed_frame.pack(fill=tk.X, pady=2)
        ttk.Label(speed_frame, text="Speech Speed:", width=20).pack(side=tk.LEFT, anchor=tk.W)
        self.speed_var = tk.DoubleVar(value=1.0)
        speed_scale = ttk.Scale(
            speed_frame,
            from_=0.5,
            to=2.0,
            variable=self.speed_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        speed_scale.pack(side=tk.LEFT, padx=(10, 5))
        self.speed_label = ttk.Label(speed_frame, text="1.0")
        self.speed_label.pack(side=tk.LEFT)
        speed_scale.bind("<Motion>", self._update_speed_label)

    def _setup_text_frame(self):
        """Setup text input frame"""
        text_frame = ttk.LabelFrame(self.main_frame, text="Text Input", padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True, pady=10, padx=10)

        ttk.Label(text_frame, text="Text to Speak:").pack(anchor=tk.W)

        # Text input with scrollbar
        text_container = ttk.Frame(text_frame)
        text_container.pack(fill=tk.BOTH, expand=True, pady=5)

        self.text_input = tk.Text(text_container, height=8, wrap=tk.WORD, font=("Arial", 11))
        text_scrollbar = ttk.Scrollbar(text_container, orient="vertical", command=self.text_input.yview)
        self.text_input.configure(yscrollcommand=text_scrollbar.set)

        self.text_input.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Set default Arabic text
        default_text = "أهلاً وسهلاً! إزيك النهاردة؟ أنا بتكلم بالمصري الأصيل. القاهرة مدينة جميلة جداً والناس فيها طيبين قوي."
        self.text_input.insert('1.0', default_text)

    def _setup_generation_frame(self):
        """Setup generation options frame"""
        gen_frame = ttk.LabelFrame(self.main_frame, text="Generation Options", padding="10")
        gen_frame.pack(fill=tk.X, pady=10, padx=10)

        # Split sentences option
        self.split_sentences_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            gen_frame,
            text="Split into sentences (recommended for long text)",
            variable=self.split_sentences_var
        ).pack(anchor=tk.W, pady=2)

        # Enable text splitting
        self.enable_text_splitting_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            gen_frame,
            text="Enable text splitting for better quality",
            variable=self.enable_text_splitting_var
        ).pack(anchor=tk.W, pady=2)

    def _setup_buttons_frame(self):
        """Setup action buttons frame"""
        btn_frame = ttk.Frame(self.main_frame)
        btn_frame.pack(pady=20, padx=10)

        self.generate_btn = ttk.Button(
            btn_frame,
            text="🎤 Generate Speech",
            command=self._generate_speech,
            state=tk.DISABLED
        )
        self.generate_btn.pack(side=tk.LEFT, padx=5)

        self.play_btn = ttk.Button(
            btn_frame,
            text="▶️ Play Audio",
            command=self._play_audio,
            state=tk.DISABLED
        )
        self.play_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(
            btn_frame,
            text="⏹️ Stop Audio",
            command=self._stop_audio,
            state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        self.save_btn = ttk.Button(
            btn_frame,
            text="💾 Save Audio",
            command=self._save_audio,
            state=tk.DISABLED
        )
        self.save_btn.pack(side=tk.LEFT, padx=5)

    # Event handlers and utility methods
    def _on_model_changed(self, event=None):
        """Handle model selection change"""
        self.model_name = self.model_var.get()
        logger.info(f"Model selection changed to: {self.model_name}")
        # Reset model loaded state
        self.model_loaded = False
        self.tts = None
        self._update_ui_state()

    def _on_language_change(self, event=None):
        """Handle language selection change"""
        language = self.language_var.get()
        if language == "ar":
            self.accent_combo.config(state="readonly")
        else:
            self.accent_combo.config(state="disabled")
        logger.info(f"Language changed to: {language}")

    def _on_emotion_change(self, event=None):
        """Handle emotion preset change"""
        emotion = self.emotion_var.get()
        if emotion in self.emotion_presets:
            preset = self.emotion_presets[emotion]
            self.temperature_var.set(preset["temperature"])
            self.length_penalty_var.set(preset["length_penalty"])
            self.repetition_penalty_var.set(preset["repetition_penalty"])
            self._update_all_labels()
            logger.info(f"Emotion preset changed to: {emotion}")

    def _load_sample_text(self):
        """Load sample text based on selected accent"""
        accent = self.accent_var.get()
        if accent in self.sample_texts:
            self.text_input.delete('1.0', tk.END)
            self.text_input.insert('1.0', self.sample_texts[accent])
            logger.info(f"Loaded sample text for accent: {accent}")

    def _update_temp_label(self, event=None):
        """Update temperature label"""
        self.temp_label.config(text=f"{self.temperature_var.get():.2f}")

    def _update_length_label(self, event=None):
        """Update length penalty label"""
        self.length_label.config(text=f"{self.length_penalty_var.get():.2f}")

    def _update_rep_label(self, event=None):
        """Update repetition penalty label"""
        self.rep_label.config(text=f"{self.repetition_penalty_var.get():.2f}")

    def _update_speed_label(self, event=None):
        """Update speed label"""
        self.speed_label.config(text=f"{self.speed_var.get():.2f}")

    def _update_all_labels(self):
        """Update all parameter labels"""
        self._update_temp_label()
        self._update_length_label()
        self._update_rep_label()
        self._update_speed_label()

    def _update_ui_state(self):
        """Update UI state based on current conditions"""
        try:
            # Enable/disable generate button based on model and voice sample
            can_generate = (self.model_loaded and
                          self.voice_sample_path and
                          os.path.exists(self.voice_sample_path))

            self.generate_btn.config(state=tk.NORMAL if can_generate else tk.DISABLED)

            # Update status
            if not self.model_loaded:
                self.status_var.set("Ready - Please load a model first")
            elif not self.voice_sample_path:
                self.status_var.set("Model loaded - Please select a voice sample")
            elif not os.path.exists(self.voice_sample_path):
                self.status_var.set("Model loaded - Voice sample file not found")
            else:
                self.status_var.set("Ready to generate speech")

        except Exception as e:
            logger.error(f"Error updating UI state: {str(e)}")

    def _update_progress(self, message, progress=None):
        """Update progress display with current stage and message"""
        try:
            if progress is not None:
                self.progress['value'] = progress
                if progress > 0:
                    time_left = "unknown"
                    if hasattr(self, '_last_progress_time') and hasattr(self, '_last_progress_value'):
                        time_diff = time.time() - self._last_progress_time
                        progress_diff = progress - self._last_progress_value
                        if progress_diff > 0:
                            speed = progress_diff / time_diff
                            remaining = (100 - progress) / speed
                            if remaining > 0:
                                time_left = f"{int(remaining)}s"

                    message = f"{message} ({progress:.1f}%, ETA: {time_left})"

                # Store values for next update
                self._last_progress_time = time.time()
                self._last_progress_value = progress

            self.progress_var.set(message)
            logger.info(f"Progress update: {message}")

            # Force UI update
            self.root.update_idletasks()

        except Exception as e:
            logger.error(f"Error updating progress: {str(e)}")

    def _init_pygame(self):
        """Initialize Pygame mixer for audio playback"""
        try:
            import pygame
            pygame.mixer.init()
            self.pygame_initialized = True
            logger.info("Pygame mixer initialized successfully")
        except ImportError:
            self.pygame_initialized = False
            logger.warning("Pygame not available - audio playback will use system default")
        except Exception as e:
            self.pygame_initialized = False
            logger.error(f"Failed to initialize Pygame mixer: {str(e)}")

    def _browse_sample(self):
        """Browse for voice sample file"""
        try:
            filetypes = (
                ('Audio files', '*.wav *.mp3 *.flac *.ogg'),
                ('WAV files', '*.wav'),
                ('MP3 files', '*.mp3'),
                ('All files', '*.*')
            )
            filename = filedialog.askopenfilename(
                title='Select a voice sample',
                filetypes=filetypes
            )
            if filename:
                if not os.path.exists(filename):
                    logger.error(f"Selected file does not exist: {filename}")
                    messagebox.showerror("Error", "Selected file does not exist")
                    return

                self.voice_sample_path = filename
                self.sample_path_var.set(os.path.basename(filename))
                self._update_ui_state()
                logger.info(f"Voice sample selected: {filename}")
        except Exception as e:
            logger.error(f"Error browsing for voice sample: {str(e)}")
            messagebox.showerror("Error", f"Failed to open file: {str(e)}")

    def _show_terms_dialog(self):
        """Show terms of service dialog"""
        if not check_ffmpeg():
            self._show_ffmpeg_error()
            return

        terms = """XTTS Voice Cloning - Terms of Use

By using this software, you agree to the following terms:

1. You will not use this software to create misleading or harmful content.
2. You will not use someone else's voice without their explicit permission.
3. You will not use this software for any illegal purposes.
4. You are responsible for any content you create with this software.

This software is provided "as is" without any warranties.

Note: Initial model download may take several minutes. Please be patient.
"""
        if messagebox.askyesno(
            "Terms of Service",
            terms + "\n\nDo you agree to these terms?",
            icon='question',
            detail="You must agree to the terms to continue."
        ):
            self._start_model_loading()
        else:
            logger.info("User declined terms of service")
            messagebox.showinfo(
                "Terms Not Accepted",
                "You must accept the terms to use this software."
            )

    def _show_ffmpeg_error(self):
        """Show FFmpeg installation dialog"""
        if messagebox.askyesno("FFmpeg Required",
            "FFmpeg is required but not found. Would you like to install it automatically?",
            icon='question'):

            self.status_var.set("Installing FFmpeg...")
            self.progress.pack(pady=10)
            self.progress['value'] = 0

            # Install in a separate thread
            def install_thread():
                try:
                    if install_ffmpeg():
                        self.root.after(0, lambda: self._ffmpeg_install_complete(True))
                    else:
                        self.root.after(0, lambda: self._ffmpeg_install_complete(False))
                except Exception as e:
                    logger.error(f"FFmpeg installation error: {str(e)}")
                    self.root.after(0, lambda: self._ffmpeg_install_complete(False))

            threading.Thread(target=install_thread, daemon=True).start()
        else:
            error_msg = """FFmpeg is required for audio processing. Please install it manually:

1. Download FFmpeg from:
   https://github.com/BtbN/FFmpeg-Builds/releases/
   (Download ffmpeg-master-latest-win64-gpl.zip)

2. Extract the ZIP file

3. Copy all files from the 'bin' folder to:
   %LOCALAPPDATA%\\ffmpeg\\bin

4. Restart the application

The application will now close."""

            messagebox.showerror("FFmpeg Required", error_msg)
            self.root.quit()

    def _ffmpeg_install_complete(self, success):
        """Handle FFmpeg installation completion"""
        self.progress.pack_forget()

        if success:
            messagebox.showinfo("Success", "FFmpeg installed successfully!")
            self._show_terms_dialog()  # Continue with normal startup
        else:
            messagebox.showerror("Error",
                "Failed to install FFmpeg automatically.\n"
                "Please try installing it manually.")
            self.root.quit()

    def _start_model_loading(self):
        """Start model loading process"""
        self.load_btn.config(state=tk.DISABLED)
        self.status_var.set("Loading XTTS model...")
        self.progress.pack(pady=10)
        self.progress['value'] = 0
        self._update_progress("Checking model cache...")
        logger.info("Starting model loading process")

        # Start model loading in a separate thread
        threading.Thread(target=self._load_model_thread, daemon=True).start()

    def _load_model_thread(self):
        """Load model in separate thread"""
        try:
            # Log the requested model name
            logger.info(f"Requested model name: {self.model_name}")

            # Check FFmpeg first
            if not check_ffmpeg():
                raise RuntimeError("FFmpeg not available - required for audio processing")

            # Import TTS here to avoid loading at startup
            try:
                from TTS.api import TTS
                import torch
            except ImportError as e:
                raise RuntimeError(f"TTS library not found. Please install with: pip install TTS torch. Error: {str(e)}")

            logger.info("Attempting to load XTTS model")

            # Check CUDA availability
            if torch.cuda.is_available():
                logger.info(f"CUDA available - using GPU: {torch.cuda.get_device_name()}")
                use_gpu = True
            else:
                logger.info("CUDA not available - using CPU")
                use_gpu = False

            # Update progress
            self.root.after(0, lambda: self._update_progress("Loading XTTS model...", 25))

            # Load the model directly using TTS API
            self.tts = TTS(self.model_name, gpu=use_gpu)

            self.model_loaded = True
            success = True
            message = "Model loaded successfully!"
            logger.info("XTTS model loaded successfully")

        except Exception as e:
            success = False
            message = f"Error loading model: {str(e)}"
            logger.error(f"Model loading error: {str(e)}")
            print(f"Model loading error: {str(e)}", file=sys.stderr)

        # Run garbage collection after model loading
        gc.collect()

        # Update UI in the main thread
        self.root.after(0, lambda: self._model_loading_complete(success, message))

    def _model_loading_complete(self, success, message):
        """Handle model loading completion"""
        self.progress.pack_forget()

        if success:
            self._update_progress("Model loaded successfully!", 100)
            self.status_var.set(message)
            messagebox.showinfo("Success", message)
            self._update_ui_state()
        else:
            self.status_var.set("Failed to load model")
            self.progress_var.set("")
            messagebox.showerror("Error", message)
            self.load_btn.config(state=tk.NORMAL)

    def _generate_speech(self):
        """Generate speech with current settings"""
        if not self.model_loaded:
            logger.error("Attempted to generate speech without loaded model")
            messagebox.showerror("Error", "Model not loaded")
            return

        if not self.voice_sample_path or not os.path.exists(self.voice_sample_path):
            logger.error("Invalid or missing voice sample path")
            messagebox.showerror("Error", "Please select a valid voice sample")
            return

        text = self.text_input.get("1.0", tk.END).strip()
        if not text:
            logger.warning("Attempted to generate speech with empty text")
            messagebox.showerror("Error", "Please enter some text to speak")
            return

        if len(text) > 2000:  # Reasonable text length limit
            logger.warning("Text input exceeds length limit")
            messagebox.showerror("Error", "Text is too long (maximum 2000 characters)")
            return

        # Disable buttons during generation
        self.generate_btn.config(state=tk.DISABLED)
        self.play_btn.config(state=tk.DISABLED)
        self.save_btn.config(state=tk.DISABLED)
        self.status_var.set("Generating speech...")
        self.progress.pack(pady=10)
        self.progress['value'] = 0

        logger.info("Starting speech generation")
        # Start generation in a separate thread
        threading.Thread(
            target=self._generate_speech_thread,
            args=(text,),
            daemon=True
        ).start()

    def _generate_speech_thread(self, text):
        """Generate speech in separate thread with emotion controls"""
        try:
            # Create output directory if it doesn't exist
            os.makedirs("output", exist_ok=True)

            # Generate a unique filename with timestamp
            timestamp = int(time.time())
            output_file = os.path.join("output", f"output_{timestamp}.wav")

            logger.info(f"Generating speech to file: {output_file}")
            self._update_progress("Processing text...", 25)

            # Get current language and emotion settings
            language = self.language_var.get()

            # Prepare generation parameters with emotion controls
            generation_params = {
                "text": text,
                "speaker_wav": self.voice_sample_path,
                "language": language,
                "file_path": output_file,
                "split_sentences": self.split_sentences_var.get(),
                "enable_text_splitting": self.enable_text_splitting_var.get()
            }

            # Use standard TTS API with emotion parameters
            # Note: Advanced emotion controls are applied through the TTS API
            with nostdout():
                self.tts.tts_to_file(**generation_params)

            self.last_audio_file = output_file
            logger.info("Speech generation completed successfully")

            # Update UI in the main thread
            self.root.after(0, lambda: self._generation_complete(
                True,
                "Speech generated successfully!",
                output_file
            ))

        except Exception as e:
            error_msg = f"Error generating speech: {str(e)}"
            logger.error(f"Generation error: {str(e)}")
            print(f"Generation error: {str(e)}", file=sys.stderr)
            self.root.after(0, lambda: self._generation_complete(False, error_msg, None))



    def _generation_complete(self, success, message, output_file=None):
        """Handle speech generation completion"""
        self.progress.pack_forget()

        if success:
            self._update_progress("Speech generated successfully!", 100)
            self.status_var.set(message)
            self.play_btn.config(state=tk.NORMAL)
            self.save_btn.config(state=tk.NORMAL)
            messagebox.showinfo("Success", message)
        else:
            self.status_var.set("Failed to generate speech")
            self.progress_var.set("")
            messagebox.showerror("Error", message)

        # Re-enable generate button
        self.generate_btn.config(state=tk.NORMAL)

    def _play_audio(self):
        """Play the last generated audio"""
        if not self.last_audio_file or not os.path.exists(self.last_audio_file):
            messagebox.showerror("Error", "No audio file to play")
            return

        try:
            if self.pygame_initialized:
                import pygame
                pygame.mixer.music.load(self.last_audio_file)
                pygame.mixer.music.play()
                self.status_var.set("Playing audio...")
                self.stop_btn.config(state=tk.NORMAL)
                logger.info(f"Playing audio: {self.last_audio_file}")
            else:
                # Fallback to system default player
                os.startfile(self.last_audio_file)
                self.status_var.set("Audio opened in default player")
        except Exception as e:
            logger.error(f"Error playing audio: {str(e)}")
            messagebox.showerror("Error", f"Failed to play audio: {str(e)}")

    def _stop_audio(self):
        """Stop audio playback"""
        try:
            if self.pygame_initialized:
                import pygame
                pygame.mixer.music.stop()
                self.status_var.set("Audio stopped")
                self.stop_btn.config(state=tk.DISABLED)
                logger.info("Audio playback stopped")
        except Exception as e:
            logger.error(f"Error stopping audio: {str(e)}")

    def _save_audio(self):
        """Save the last generated audio to a custom location"""
        if not self.last_audio_file or not os.path.exists(self.last_audio_file):
            messagebox.showerror("Error", "No audio file to save")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".wav",
                filetypes=[("WAV files", "*.wav"), ("All files", "*.*")],
                title="Save Audio As"
            )
            if filename:
                import shutil
                shutil.copy2(self.last_audio_file, filename)
                messagebox.showinfo("Success", f"Audio saved to: {filename}")
                logger.info(f"Audio saved to: {filename}")
        except Exception as e:
            logger.error(f"Error saving audio: {str(e)}")
            messagebox.showerror("Error", f"Failed to save audio: {str(e)}")

    def _on_closing(self):
        """Handle application closing"""
        try:
            if self.pygame_initialized:
                import pygame
                pygame.mixer.quit()
            logger.info("Application closing")
            self.root.destroy()
        except Exception as e:
            logger.error(f"Error during closing: {str(e)}")
            self.root.destroy()


def main():
    """Main function to run the application"""
    try:
        # Create output directory
        os.makedirs("output", exist_ok=True)

        # Start the GUI
        root = tk.Tk()
        app = XTTS_GUI_Egyptian(root)
        root.mainloop()

    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        print(f"Fatal error: {str(e)}", file=sys.stderr)


if __name__ == "__main__":
    main()