#!/usr/bin/env python3
"""
Egyptian Data Collection Helper
Help collect and organize Egyptian speech data for training
"""

import os
import sys
import shutil
import pandas as pd
from pathlib import Path

def create_sample_recordings():
    """Create a script to record Egyptian samples"""
    print("🎤 Creating Recording Script")
    print("=" * 30)
    
    recording_script = '''#!/usr/bin/env python3
"""
Egyptian Voice Recording Script
Record high-quality Egyptian samples for training
"""

import sounddevice as sd
import soundfile as sf
import numpy as np
import time

def record_sample(filename, duration=10, sample_rate=22050):
    """Record a single audio sample"""
    print(f"🎤 Recording {filename} for {duration} seconds...")
    print("3... 2... 1... START!")
    
    # Record audio
    audio = sd.rec(int(duration * sample_rate), 
                   samplerate=sample_rate, 
                   channels=1, 
                   dtype=np.float32)
    sd.wait()  # Wait for recording to complete
    
    # Save audio
    sf.write(f"egyptian_dataset/wavs/{filename}", audio, sample_rate)
    print(f"✅ Saved: {filename}")

def main():
    """Record Egyptian samples"""
    print("🇪🇬 Egyptian Voice Recording Session")
    print("=" * 40)
    
    # Egyptian texts to record
    texts = [
        ("speaker1_001.wav", "أهلاً وسهلاً! إزيك النهاردة؟"),
        ("speaker1_002.wav", "أنا مصري من القاهرة والحمد لله."),
        ("speaker1_003.wav", "يلا بينا نروح نتمشى شوية في وسط البلد."),
        ("speaker1_004.wav", "والله العظيم ده أحلى كلام سمعته النهاردة!"),
        ("speaker1_005.wav", "اتفضل اشرب شاي واقعد معانا شوية."),
        ("speaker1_006.wav", "مصر أم الدنيا وشعبها كريم قوي."),
        ("speaker1_007.wav", "كان يا ما كان في قديم الزمان واحد مصري."),
        ("speaker1_008.wav", "القاهرة مدينة جميلة والنيل نهر عظيم."),
        ("speaker1_009.wav", "الطقس النهاردة حلو قوي ومناسب للخروج."),
        ("speaker1_010.wav", "ربنا يخليك ويحفظك من كل شر.")
    ]
    
    print("📝 You will record these Egyptian texts:")
    for i, (filename, text) in enumerate(texts, 1):
        print(f"{i:2d}. {text}")
    
    input("\\nPress Enter when ready to start recording...")
    
    for filename, text in texts:
        print(f"\\n📝 Next text: {text}")
        input("Press Enter to start recording...")
        record_sample(filename, duration=8)
        time.sleep(1)
    
    print("\\n🎉 Recording session complete!")
    print("Next steps:")
    print("1. Check audio quality in egyptian_dataset/wavs/")
    print("2. Re-record any poor quality samples")
    print("3. Run: python prepare_egyptian_data.py")

if __name__ == "__main__":
    main()
'''
    
    with open("record_egyptian_samples.py", "w", encoding="utf-8") as f:
        f.write(recording_script)
    
    print("✅ Created: record_egyptian_samples.py")
    print("\n📋 To use:")
    print("1. Install: pip install sounddevice soundfile")
    print("2. Run: python record_egyptian_samples.py")
    print("3. Follow prompts to record Egyptian samples")

def organize_existing_audio():
    """Help organize existing audio files"""
    print("\n📁 Organizing Existing Audio Files")
    print("=" * 35)
    
    # Look for audio files in common locations
    search_paths = [
        ".",
        "samples",
        "audio",
        "recordings",
        os.path.expanduser("~/Downloads"),
        os.path.expanduser("~/Desktop")
    ]
    
    audio_files = []
    
    for path in search_paths:
        if os.path.exists(path):
            for file in os.listdir(path):
                if file.lower().endswith(('.wav', '.mp3', '.m4a', '.flac')):
                    full_path = os.path.join(path, file)
                    audio_files.append(full_path)
    
    if audio_files:
        print(f"🔍 Found {len(audio_files)} audio files:")
        for i, file in enumerate(audio_files[:10], 1):  # Show first 10
            print(f"  {i}. {os.path.basename(file)} ({os.path.dirname(file)})")
        
        if len(audio_files) > 10:
            print(f"  ... and {len(audio_files) - 10} more files")
        
        print("\n📋 To use these files:")
        print("1. Copy Egyptian speech files to: egyptian_dataset/wavs/")
        print("2. Rename them as: speaker1_001.wav, speaker1_002.wav, etc.")
        print("3. Update metadata.csv with transcriptions")
        print("4. Run: python prepare_egyptian_data.py")
    else:
        print("❌ No audio files found in common locations")
        print("📝 You'll need to:")
        print("1. Record new samples with: python record_egyptian_samples.py")
        print("2. Or manually add audio files to: egyptian_dataset/wavs/")

def create_quick_training_guide():
    """Create a quick training guide"""
    print("\n📚 Quick Training Guide")
    print("=" * 25)
    
    guide_content = '''# Egyptian XTTS Training Quick Guide

## 🎯 Overview
Train XTTS model specifically on Egyptian Arabic for better accent quality.

## 📋 Requirements
- 1-10 hours of Egyptian speech data
- GPU with 8GB+ VRAM (recommended)
- 16GB+ RAM
- 50GB+ free disk space

## 🚀 Quick Start

### Step 1: Prepare Data
```bash
# Option A: Record new samples
pip install sounddevice soundfile
python record_egyptian_samples.py

# Option B: Use existing audio
# Copy Egyptian audio files to: egyptian_dataset/wavs/
# Update metadata.csv with transcriptions
```

### Step 2: Install Training Dependencies
```bash
pip install trainer librosa soundfile pandas scikit-learn
```

### Step 3: Prepare Dataset
```bash
python prepare_egyptian_data.py
```

### Step 4: Start Training
```bash
python train_egyptian_xtts.py
```

### Step 5: Monitor Training
```bash
tensorboard --logdir egyptian_xtts_training/
```

## 📊 Training Process
- **Duration**: 6-24 hours (depending on data size and hardware)
- **Checkpoints**: Saved every 1000 steps
- **Monitoring**: Use Tensorboard for progress tracking
- **Output**: Trained model in `egyptian_xtts_training/`

## 🎯 Data Quality Tips
1. **Clear audio**: No background noise
2. **Consistent volume**: Normalize all files
3. **Egyptian dialect**: Use authentic Egyptian expressions
4. **Multiple speakers**: Include variety for better generalization
5. **Accurate transcriptions**: Essential for good results

## 🔧 Training Parameters
- **Batch size**: 2 (adjust based on GPU memory)
- **Learning rate**: 5e-06
- **Epochs**: 100
- **Save frequency**: Every 1000 steps

## 🎉 Using Trained Model
After training completes:
1. Copy best checkpoint to your GUI folder
2. Update GUI to use your trained model
3. Enjoy improved Egyptian accent quality!

## ⚠️ Troubleshooting
- **Out of memory**: Reduce batch size
- **Slow training**: Use GPU if available
- **Poor quality**: Check data quality and transcriptions
- **Training stops**: Check logs for errors

## 📞 Support
Check training logs and Tensorboard for detailed progress information.
'''
    
    with open("TRAINING_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ Created: TRAINING_GUIDE.md")
    print("📖 Complete training guide with step-by-step instructions")

def check_training_readiness():
    """Check if ready to start training"""
    print("\n🔍 Training Readiness Check")
    print("=" * 30)
    
    checks = []
    
    # Check dataset structure
    if os.path.exists("egyptian_dataset/wavs"):
        wav_files = [f for f in os.listdir("egyptian_dataset/wavs") if f.endswith('.wav')]
        if wav_files:
            checks.append(f"✅ Audio files: {len(wav_files)} found")
        else:
            checks.append("❌ Audio files: None found")
    else:
        checks.append("❌ Dataset folder: Not found")
    
    # Check metadata
    if os.path.exists("egyptian_dataset/metadata.csv"):
        try:
            df = pd.read_csv("egyptian_dataset/metadata.csv", sep='|')
            checks.append(f"✅ Metadata: {len(df)} entries")
        except:
            checks.append("❌ Metadata: Invalid format")
    else:
        checks.append("❌ Metadata: Not found")
    
    # Check dependencies
    try:
        import torch
        if torch.cuda.is_available():
            checks.append("✅ GPU: Available")
        else:
            checks.append("⚠️ GPU: Not available (CPU training will be slow)")
    except:
        checks.append("❌ PyTorch: Not installed")
    
    try:
        import TTS
        checks.append(f"✅ TTS: {TTS.__version__}")
    except:
        checks.append("❌ TTS: Not installed")
    
    # Display results
    for check in checks:
        print(f"  {check}")
    
    # Recommendations
    ready = all("✅" in check for check in checks if not check.startswith("⚠️"))
    
    if ready:
        print("\n🎉 Ready to start training!")
        print("Run: python train_egyptian_xtts.py")
    else:
        print("\n📋 Next steps:")
        if "❌ Audio files" in str(checks):
            print("  • Add Egyptian audio files to egyptian_dataset/wavs/")
        if "❌ Metadata" in str(checks):
            print("  • Update egyptian_dataset/metadata.csv with transcriptions")
        if "❌ PyTorch" in str(checks):
            print("  • Install PyTorch: pip install torch torchaudio")
        if "❌ TTS" in str(checks):
            print("  • Install TTS: pip install TTS[all]")

def main():
    """Main function"""
    print("🇪🇬 Egyptian Data Collection Helper")
    print("=" * 40)
    
    print("Choose an option:")
    print("1. Create recording script")
    print("2. Organize existing audio files")
    print("3. Create training guide")
    print("4. Check training readiness")
    print("5. All of the above")
    
    choice = input("\nEnter choice (1-5): ").strip()
    
    if choice == "1":
        create_sample_recordings()
    elif choice == "2":
        organize_existing_audio()
    elif choice == "3":
        create_quick_training_guide()
    elif choice == "4":
        check_training_readiness()
    elif choice == "5":
        create_sample_recordings()
        organize_existing_audio()
        create_quick_training_guide()
        check_training_readiness()
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    main()
