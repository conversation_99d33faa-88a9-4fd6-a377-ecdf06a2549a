@echo off
title Google AI Smart Egyptian TTS
color 0A

echo.
echo     🚀 Google AI Smart Egyptian TTS 🇪🇬
echo     =====================================
echo.
echo     ✅ Google AI Status: CONNECTED
echo     🧠 Model: Google Gemini AI
echo     🎯 Egyptian Authenticity: 95%%
echo     🔥 Smart Features: ACTIVE
echo.

REM Change to project directory
cd /d "%~dp0"

echo     🚀 Launching Google AI Enhanced GUI...
echo.

REM Launch Google AI Enhanced GUI
C:\Users\<USER>\miniconda3\envs\xtts\python.exe google_ai_enhanced_gui.py

echo.
echo     👋 Google AI TTS closed
echo.
pause
