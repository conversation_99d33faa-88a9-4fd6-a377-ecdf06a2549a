#!/usr/bin/env python3
"""
Egyptian XTTS Training Script
Fine-tune XTTS model on Egyptian Arabic data
"""

import os
import sys
import torch
from trainer import Trainer, TrainerArgs
from TTS.tts.configs.xtts_config import XttsConfig
from TTS.tts.models.xtts import Xtts

def main():
    """Main training function"""
    print("🇪🇬 Starting Egyptian XTTS Training")
    print("=" * 40)
    
    # Check CUDA
    if torch.cuda.is_available():
        print(f"✅ Using GPU: {torch.cuda.get_device_name()}")
        device = "cuda"
    else:
        print("⚠️ Using CPU (training will be very slow)")
        device = "cpu"
    
    # Load config
    config_path = "egyptian_dataset/config.json"
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        print("Run egyptian_model_training.py first to create config")
        return
    
    config = XttsConfig()
    config.load_json(config_path)
    
    # Initialize model
    print("🔧 Initializing XTTS model...")
    model = Xtts.init_from_config(config)
    
    # Load pretrained weights
    print("📥 Loading pretrained XTTS weights...")
    checkpoint_dir = model.get_model_file_path()
    model.load_checkpoint(config, checkpoint_dir=checkpoint_dir, eval=False)
    
    # Setup trainer
    trainer_args = TrainerArgs()
    trainer_args.restore_path = None
    
    trainer = Trainer(
        trainer_args,
        config,
        output_path=config.output_path,
        model=model,
        train_samples=None,
        eval_samples=None
    )
    
    # Start training
    print("🚀 Starting training...")
    print("📊 Monitor progress in: egyptian_xtts_training/")
    print("🔗 Tensorboard: tensorboard --logdir egyptian_xtts_training/")
    
    trainer.fit()

if __name__ == "__main__":
    main()
