from TTS.api import TTS
import time

print("Loading XTTS model...")
start_time = time.time()

# Initialize TTS with XTTS model
tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=True)

load_time = time.time() - start_time
print(f"Model loaded in {load_time:.2f} seconds")

# List available models
print("\nAvailable models:")
print(tts.list_models())

# List available speakers if using a multi-speaker model
if hasattr(tts, 'speakers') and tts.speakers:
    print("\nAvailable speakers:")
    print(tts.speakers)

# List available languages
print("\nAvailable languages:")
print(tts.languages)

# Generate speech
print("\nGenerating speech...")
start_time = time.time()

tts.tts_to_file(
    text="Hello, this is a test of the XTTS text to speech system running on Windows.",
    speaker_wav="samples/sample_voice.wav",
    language="en",
    file_path="output/test_output.wav"
)

generation_time = time.time() - start_time
print(f"Speech generated in {generation_time:.2f} seconds")
print("Output saved to output/test_output.wav")

# Generate Arabic speech if needed
generate_arabic = input("\nGenerate Arabic speech sample? (y/n): ").lower() == 'y'
if generate_arabic:
    print("Generating Arabic speech...")
    start_time = time.time()
    
    tts.tts_to_file(
        text="مرحبا، هذا اختبار لنظام تحويل النص إلى كلام باستخدام إكس تي تي إس.",
        speaker_wav="samples/sample_voice.wav",
        language="ar",
        file_path="output/arabic_output.wav"
    )
    
    generation_time = time.time() - start_time
    print(f"Arabic speech generated in {generation_time:.2f} seconds")
    print("Output saved to output/arabic_output.wav")
