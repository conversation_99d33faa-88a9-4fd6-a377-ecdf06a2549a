<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.tests &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.time" href="time.html" />
    <link rel="prev" title="pygame.surfarray" href="surfarray.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame.tests">
<span id="pygame-tests"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame.tests</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Pygame unit test suite package</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="tests.html#pygame.tests.run">pygame.tests.run</a></div>
</td>
<td>—</td>
<td>Run the pygame unit test suite</td>
</tr>
</tbody>
</table>
<p>A quick way to run the test suite package from the command line is to import
the go submodule with the Python -m option:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">pygame</span><span class="o">.</span><span class="n">tests</span> <span class="p">[</span><span class="o">&lt;</span><span class="n">test</span> <span class="n">options</span><span class="o">&gt;</span><span class="p">]</span>
</pre></div>
</div>
<p>Command line option --help displays a usage message. Available options
correspond to the <a class="tooltip reference internal" href="#pygame.tests.run" title=""><code class="xref py py-func docutils literal notranslate"><span class="pre">pygame.tests.run()</span></code><span class="tooltip-content">Run the pygame unit test suite</span></a> arguments.</p>
<p>The xxxx_test submodules of the tests package are unit test suites for
individual parts of pygame. Each can also be run as a main program. This is
useful if the test, such as cdrom_test, is interactive.</p>
<p>For pygame development the test suite can be run from a pygame distribution
root directory. Program <code class="docutils literal notranslate"><span class="pre">run_tests.py</span></code> is provided for convenience, though
test/go.py can be run directly.</p>
<p>Module level tags control which modules are included in a unit test run. Tags
are assigned to a unit test module with a corresponding &lt;name&gt;_tags.py module.
The tags module has the global __tags__, a list of tag names. For example,
<code class="docutils literal notranslate"><span class="pre">cdrom_test.py</span></code> has a tag file <code class="docutils literal notranslate"><span class="pre">cdrom_tags.py</span></code> containing a tags list that
has the 'interactive' string. The 'interactive' tag indicates <code class="docutils literal notranslate"><span class="pre">cdrom_test.py</span></code>
expects user input. It is excluded from a <code class="docutils literal notranslate"><span class="pre">run_tests.py</span></code> or
<code class="docutils literal notranslate"><span class="pre">pygame.tests.go</span></code> run.</p>
<p>Two other tags that are excluded are 'ignore' and 'subprocess_ignore'. These
two tags indicate unit tests that will not run on a particular platform, or
for which no corresponding pygame module is available.</p>
<p>The test runner will list each excluded module along with the tag responsible.</p>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.tests.run">
<span class="sig-prename descclassname"><span class="pre">pygame.tests.</span></span><span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.tests.run" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Run the pygame unit test suite</span></div>
<div class="line"><span class="signature">run(*args, **kwds) -&gt; tuple</span></div>
</div>
<p>Positional arguments (optional):</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">The</span> <span class="n">names</span> <span class="n">of</span> <span class="n">tests</span> <span class="n">to</span> <span class="n">include</span><span class="o">.</span> <span class="n">If</span> <span class="n">omitted</span> <span class="n">then</span> <span class="nb">all</span> <span class="n">tests</span> <span class="n">are</span> <span class="n">run</span><span class="o">.</span> <span class="n">Test</span> <span class="n">names</span>
<span class="n">need</span> <span class="ow">not</span> <span class="n">include</span> <span class="n">the</span> <span class="n">trailing</span> <span class="s1">&#39;_test&#39;</span><span class="o">.</span>
</pre></div>
</div>
<p>Keyword arguments:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">incomplete</span> <span class="o">-</span> <span class="n">fail</span> <span class="n">incomplete</span> <span class="n">tests</span> <span class="p">(</span><span class="n">default</span> <span class="kc">False</span><span class="p">)</span>
<span class="n">nosubprocess</span> <span class="o">-</span> <span class="n">run</span> <span class="nb">all</span> <span class="n">test</span> <span class="n">suites</span> <span class="ow">in</span> <span class="n">the</span> <span class="n">current</span> <span class="n">process</span>
               <span class="p">(</span><span class="n">default</span> <span class="kc">False</span><span class="p">,</span> <span class="n">use</span> <span class="n">separate</span> <span class="n">subprocesses</span><span class="p">)</span>
<span class="n">dump</span> <span class="o">-</span> <span class="n">dump</span> <span class="n">failures</span><span class="o">/</span><span class="n">errors</span> <span class="k">as</span> <span class="nb">dict</span> <span class="n">ready</span> <span class="n">to</span> <span class="nb">eval</span> <span class="p">(</span><span class="n">default</span> <span class="kc">False</span><span class="p">)</span>
<span class="n">file</span> <span class="o">-</span> <span class="k">if</span> <span class="n">provided</span><span class="p">,</span> <span class="n">the</span> <span class="n">name</span> <span class="n">of</span> <span class="n">a</span> <span class="n">file</span> <span class="n">into</span> <span class="n">which</span> <span class="n">to</span> <span class="n">dump</span> <span class="n">failures</span><span class="o">/</span><span class="n">errors</span>
<span class="n">timings</span> <span class="o">-</span> <span class="k">if</span> <span class="n">provided</span><span class="p">,</span> <span class="n">the</span> <span class="n">number</span> <span class="n">of</span> <span class="n">times</span> <span class="n">to</span> <span class="n">run</span> <span class="n">each</span> <span class="n">individual</span> <span class="n">test</span> <span class="n">to</span>
          <span class="n">get</span> <span class="n">an</span> <span class="n">average</span> <span class="n">run</span> <span class="n">time</span> <span class="p">(</span><span class="n">default</span> <span class="ow">is</span> <span class="n">run</span> <span class="n">each</span> <span class="n">test</span> <span class="n">once</span><span class="p">)</span>
<span class="n">exclude</span> <span class="o">-</span> <span class="n">A</span> <span class="nb">list</span> <span class="n">of</span> <span class="n">TAG</span> <span class="n">names</span> <span class="n">to</span> <span class="n">exclude</span> <span class="kn">from</span> <span class="nn">the</span> <span class="n">run</span>
<span class="n">show_output</span> <span class="o">-</span> <span class="n">show</span> <span class="n">silenced</span> <span class="n">stderr</span><span class="o">/</span><span class="n">stdout</span> <span class="n">on</span> <span class="n">errors</span> <span class="p">(</span><span class="n">default</span> <span class="kc">False</span><span class="p">)</span>
<span class="nb">all</span> <span class="o">-</span> <span class="n">dump</span> <span class="nb">all</span> <span class="n">results</span><span class="p">,</span> <span class="ow">not</span> <span class="n">just</span> <span class="n">errors</span> <span class="p">(</span><span class="n">default</span> <span class="kc">False</span><span class="p">)</span>
<span class="n">randomize</span> <span class="o">-</span> <span class="n">randomize</span> <span class="n">order</span> <span class="n">of</span> <span class="n">tests</span> <span class="p">(</span><span class="n">default</span> <span class="kc">False</span><span class="p">)</span>
<span class="n">seed</span> <span class="o">-</span> <span class="k">if</span> <span class="n">provided</span><span class="p">,</span> <span class="n">a</span> <span class="n">seed</span> <span class="n">randomizer</span> <span class="n">integer</span>
<span class="n">multi_thread</span> <span class="o">-</span> <span class="k">if</span> <span class="n">provided</span><span class="p">,</span> <span class="n">the</span> <span class="n">number</span> <span class="n">of</span> <span class="n">THREADS</span> <span class="ow">in</span> <span class="n">which</span> <span class="n">to</span> <span class="n">run</span>
               <span class="n">subprocessed</span> <span class="n">tests</span>
<span class="n">time_out</span> <span class="o">-</span> <span class="k">if</span> <span class="n">subprocess</span> <span class="ow">is</span> <span class="kc">True</span> <span class="n">then</span> <span class="n">the</span> <span class="n">time</span> <span class="n">limit</span> <span class="ow">in</span> <span class="n">seconds</span> <span class="n">before</span>
           <span class="n">killing</span> <span class="n">a</span> <span class="n">test</span> <span class="p">(</span><span class="n">default</span> <span class="mi">30</span><span class="p">)</span>
<span class="n">fake</span> <span class="o">-</span> <span class="k">if</span> <span class="n">provided</span><span class="p">,</span> <span class="n">the</span> <span class="n">name</span> <span class="n">of</span> <span class="n">the</span> <span class="n">fake</span> <span class="n">tests</span> <span class="n">package</span> <span class="ow">in</span> <span class="n">the</span>
       <span class="n">run_tests__tests</span> <span class="n">subpackage</span> <span class="n">to</span> <span class="n">run</span> <span class="n">instead</span> <span class="n">of</span> <span class="n">the</span> <span class="n">normal</span>
       <span class="n">pygame</span> <span class="n">tests</span>
<span class="n">python</span> <span class="o">-</span> <span class="n">the</span> <span class="n">path</span> <span class="n">to</span> <span class="n">a</span> <span class="n">python</span> <span class="n">executable</span> <span class="n">to</span> <span class="n">run</span> <span class="n">subprocessed</span> <span class="n">tests</span>
         <span class="p">(</span><span class="n">default</span> <span class="n">sys</span><span class="o">.</span><span class="n">executable</span><span class="p">)</span>
</pre></div>
</div>
<p>Return value:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">A</span> <span class="nb">tuple</span> <span class="n">of</span> <span class="n">total</span> <span class="n">number</span> <span class="n">of</span> <span class="n">tests</span> <span class="n">run</span><span class="p">,</span> <span class="n">dictionary</span> <span class="n">of</span> <span class="n">error</span> <span class="n">information</span><span class="o">.</span>
<span class="n">The</span> <span class="n">dictionary</span> <span class="ow">is</span> <span class="n">empty</span> <span class="k">if</span> <span class="n">no</span> <span class="n">errors</span> <span class="n">were</span> <span class="n">recorded</span><span class="o">.</span>
</pre></div>
</div>
<p>By default individual test modules are run in separate subprocesses. This
recreates normal pygame usage where <code class="docutils literal notranslate"><span class="pre">pygame.init()</span></code> and <code class="docutils literal notranslate"><span class="pre">pygame.quit()</span></code>
are called only once per program execution, and avoids unfortunate
interactions between test modules.</p>
<p>A time limit is placed on test execution ensuring that any frozen tests
processes are killed when their time allotment is expired. Use the single
process option if threading is not working properly or if tests are taking
too long. It is not guaranteed that all tests will pass in single process
mode.</p>
<p>Tests are run in a randomized order if the randomize argument is True or a
seed argument is provided. If no seed integer is provided then the system
time is used for the randomization seed value.</p>
<p>Individual test modules may have a __tags__ attribute, a list of tag strings
used to selectively omit modules from a run. By default only 'interactive'
modules such as cdrom_test are ignored. An interactive module must be run
from the console as a Python program.</p>
<p>This function can only be called once per Python session. It is not
reentrant.</p>
</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref\tests.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="time.html" title="pygame.time"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="surfarray.html" title="pygame.surfarray"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.tests</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>