@echo off
title Egyptian Pronunciation Training
color 0D

echo.
echo     🎓 Egyptian Pronunciation Training 🇪🇬
echo     =====================================
echo.
echo     Training the model for correct Egyptian pronunciation:
echo     • ج sound as 'g' (not 'j')
echo     • ق sound as glottal stop
echo     • Egyptian expressions and accent
echo.

REM Change to project directory
cd /d "%~dp0"

echo     Step 1: Setting up pronunciation data...
C:\Users\<USER>\miniconda3\envs\xtts\python.exe train_egyptian_pronunciation.py

echo.
echo     Step 2: Running pronunciation training...
C:\Users\<USER>\miniconda3\envs\xtts\python.exe run_pronunciation_training.py

echo.
echo     🎉 Pronunciation training complete!
echo     📁 Check these files for results:
echo        • pronunciation_test_*.wav - Character pronunciation tests
echo        • pronunciation_comparison_*.wav - Standard vs Egyptian
echo        • PRONUNCIATION_TRAINING_GUIDE.md - Complete guide
echo.
pause
