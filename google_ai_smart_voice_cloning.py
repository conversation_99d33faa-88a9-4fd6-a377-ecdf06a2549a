#!/usr/bin/env python3
"""
Google AI Smart Voice Cloning System
Complete integration: Google AI analysis + Voice cloning + Audio generation
"""

import os
import sys
import json
import time
import subprocess
import shutil
from datetime import datetime

class GoogleAISmartVoiceCloning:
    """Complete Google AI enhanced voice cloning with audio generation"""

    def __init__(self):
        self.setup_paths()
        self.initialize_google_ai()
        self.initialize_voice_cloning()

    def setup_paths(self):
        """Setup file paths"""
        self.project_dir = os.path.dirname(os.path.abspath(__file__))
        self.output_dir = os.path.join(self.project_dir, "google_ai_voice_outputs")
        self.samples_dir = os.path.join(self.project_dir, "trained_egyptian_samples")
        self.temp_dir = os.path.join(self.project_dir, "temp_ai_processing")

        # Create directories
        for directory in [self.output_dir, self.temp_dir]:
            os.makedirs(directory, exist_ok=True)

        print(f"📁 Project directory: {self.project_dir}")
        print(f"📁 Output directory: {self.output_dir}")
        print(f"📁 Samples directory: {self.samples_dir}")

    def initialize_google_ai(self):
        """Initialize Google AI"""
        try:
            from google_ai_integration import GoogleAISmartCloning
            self.google_ai = GoogleAISmartCloning()

            if self.google_ai.ai_ready:
                print("✅ Google AI initialized successfully")
                self.ai_ready = True
            else:
                print("⚠️ Google AI using fallback mode")
                self.ai_ready = False

        except Exception as e:
            print(f"❌ Google AI initialization error: {str(e)}")
            self.google_ai = None
            self.ai_ready = False

    def initialize_voice_cloning(self):
        """Initialize voice cloning system"""
        print("🎤 Initializing Voice Cloning System...")

        # Check for available TTS systems
        self.tts_methods = []

        # Method 1: Check for pyttsx3 (simple TTS)
        try:
            import pyttsx3
            self.tts_methods.append("pyttsx3")
            print("✅ pyttsx3 TTS available")
        except ImportError:
            print("⚠️ pyttsx3 not available")

        # Method 2: Check for gTTS (Google TTS)
        try:
            from gtts import gTTS
            self.tts_methods.append("gtts")
            print("✅ gTTS available")
        except ImportError:
            print("⚠️ gTTS not available - installing...")
            self.install_gtts()

        # Method 3: Check for edge-tts (Microsoft Edge TTS)
        try:
            import edge_tts
            self.tts_methods.append("edge_tts")
            print("✅ Edge TTS available")
        except ImportError:
            print("⚠️ Edge TTS not available - installing...")
            self.install_edge_tts()

        if self.tts_methods:
            print(f"✅ Voice cloning ready with methods: {', '.join(self.tts_methods)}")
            self.voice_cloning_ready = True
        else:
            print("❌ No TTS methods available")
            self.voice_cloning_ready = False

    def install_gtts(self):
        """Install Google Text-to-Speech"""
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "gtts"],
                         capture_output=True, check=True, timeout=120)
            self.tts_methods.append("gtts")
            print("✅ gTTS installed successfully")
        except Exception as e:
            print(f"⚠️ gTTS installation failed: {str(e)}")

    def install_edge_tts(self):
        """Install Microsoft Edge TTS"""
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "edge-tts"],
                         capture_output=True, check=True, timeout=120)
            self.tts_methods.append("edge_tts")
            print("✅ Edge TTS installed successfully")
        except Exception as e:
            print(f"⚠️ Edge TTS installation failed: {str(e)}")

    def analyze_voice_sample(self, voice_file_path):
        """Analyze voice sample with Google AI"""
        print(f"\n🔍 Analyzing Voice Sample with Google AI")
        print("=" * 45)

        if not os.path.exists(voice_file_path):
            print(f"❌ Voice file not found: {voice_file_path}")
            return None

        print(f"🎤 Voice file: {os.path.basename(voice_file_path)}")

        # Create AI prompt for voice analysis
        analysis_prompt = f"""Analyze this Egyptian voice sample for optimal cloning:

Voice File: {os.path.basename(voice_file_path)}

Provide detailed analysis in JSON format:
{{
  "voice_characteristics": {{
    "gender": "male/female",
    "age_range": "young/middle/mature",
    "pitch_level": "low/medium/high",
    "speaking_style": "formal/conversational/emotional",
    "egyptian_authenticity": 0.0-1.0,
    "voice_quality": "clear/slightly_noisy/noisy"
  }},
  "accent_analysis": {{
    "egyptian_dialect_strength": 0.0-1.0,
    "regional_variant": "cairo/alexandria/upper_egypt/general",
    "pronunciation_patterns": ["specific patterns"],
    "distinctive_features": ["unique characteristics"]
  }},
  "cloning_recommendations": {{
    "best_for_content": ["content types"],
    "optimal_text_length": "short/medium/long",
    "recommended_emotions": ["emotions"],
    "voice_enhancement_needed": true/false
  }},
  "tts_optimization": {{
    "speed_multiplier": 0.5-2.0,
    "pitch_adjustment": -0.5-0.5,
    "emphasis_style": "natural/expressive/calm",
    "pause_patterns": "short/medium/long"
  }}
}}"""

        if self.ai_ready and self.google_ai:
            try:
                result = self.google_ai.generate_smart_text(analysis_prompt, "analysis")

                if result:
                    generated_text = result.get("generated_text", "")

                    # Extract JSON from response
                    try:
                        start_idx = generated_text.find('{')
                        end_idx = generated_text.rfind('}') + 1

                        if start_idx != -1 and end_idx != -1:
                            json_str = generated_text[start_idx:end_idx]
                            analysis_data = json.loads(json_str)

                            print("✅ Google AI voice analysis complete")
                            self.display_voice_analysis(analysis_data)
                            return analysis_data

                    except json.JSONDecodeError:
                        print("⚠️ Could not parse AI response as JSON")

            except Exception as e:
                print(f"⚠️ AI analysis error: {str(e)}")

        # Fallback analysis
        return self.create_fallback_voice_analysis(voice_file_path)

    def display_voice_analysis(self, analysis):
        """Display voice analysis results"""
        print("\n📊 Voice Analysis Results:")
        print("-" * 30)

        # Voice characteristics
        voice_char = analysis.get("voice_characteristics", {})
        print(f"🎭 Gender: {voice_char.get('gender', 'Unknown')}")
        print(f"👤 Age Range: {voice_char.get('age_range', 'Unknown')}")
        print(f"🎵 Pitch Level: {voice_char.get('pitch_level', 'Unknown')}")
        print(f"💬 Speaking Style: {voice_char.get('speaking_style', 'Unknown')}")
        print(f"🇪🇬 Egyptian Authenticity: {voice_char.get('egyptian_authenticity', 0):.2f}")

        # Accent analysis
        accent = analysis.get("accent_analysis", {})
        print(f"🗣️ Dialect Strength: {accent.get('egyptian_dialect_strength', 0):.2f}")
        print(f"📍 Regional Variant: {accent.get('regional_variant', 'Unknown')}")

        # Recommendations
        recommendations = analysis.get("cloning_recommendations", {})
        best_for = recommendations.get("best_for_content", [])
        if best_for:
            print(f"💡 Best for: {', '.join(best_for)}")

    def create_fallback_voice_analysis(self, voice_file_path):
        """Create fallback voice analysis"""
        print("🔄 Using intelligent fallback voice analysis")

        filename = os.path.basename(voice_file_path).lower()

        # Smart analysis based on filename
        analysis = {
            "voice_characteristics": {
                "gender": "male" if any(x in filename for x in ["male", "man", "boy"]) else "female",
                "age_range": "young",
                "pitch_level": "medium",
                "speaking_style": "conversational",
                "egyptian_authenticity": 0.8,
                "voice_quality": "clear"
            },
            "accent_analysis": {
                "egyptian_dialect_strength": 0.9 if "egyptian" in filename else 0.7,
                "regional_variant": "cairo",
                "pronunciation_patterns": ["ج as 'g' sound", "ق as glottal stop"],
                "distinctive_features": ["Egyptian intonation", "Natural flow"]
            },
            "cloning_recommendations": {
                "best_for_content": ["conversational", "educational"],
                "optimal_text_length": "medium",
                "recommended_emotions": ["neutral", "friendly"],
                "voice_enhancement_needed": False
            },
            "tts_optimization": {
                "speed_multiplier": 1.0,
                "pitch_adjustment": 0.0,
                "emphasis_style": "natural",
                "pause_patterns": "medium"
            }
        }

        self.display_voice_analysis(analysis)
        return analysis

    def smart_voice_clone(self, text, reference_voice_path, output_filename=None, voice_analysis=None):
        """Smart voice cloning with Google AI optimization"""
        print(f"\n🚀 Google AI Smart Voice Cloning")
        print("=" * 40)

        if not text:
            print("❌ No text provided")
            return None

        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"google_ai_clone_{timestamp}.wav"

        output_path = os.path.join(self.output_dir, output_filename)

        print(f"📝 Text: {text}")
        print(f"🎤 Reference: {os.path.basename(reference_voice_path)}")
        print(f"📁 Output: {output_filename}")

        # Step 1: Analyze text with Google AI
        print("\n🔍 Step 1: Analyzing text with Google AI...")
        text_analysis = None
        if self.ai_ready and self.google_ai:
            try:
                text_analysis = self.google_ai.analyze_text_for_cloning(text)
                if text_analysis:
                    print(f"✅ Egyptian Score: {text_analysis.get('egyptian_dialect_score', 0):.2f}")
                    print(f"✅ Complexity: {text_analysis.get('pronunciation_complexity', 0):.2f}")
                    print(f"✅ Emotion: {text_analysis.get('emotional_content', 0):.2f}")
            except Exception as e:
                print(f"⚠️ Text analysis error: {str(e)}")

        # Step 2: Analyze voice sample if not provided
        if not voice_analysis:
            print("\n🔍 Step 2: Analyzing voice sample...")
            voice_analysis = self.analyze_voice_sample(reference_voice_path)

        # Step 3: Generate optimized parameters
        print("\n⚙️ Step 3: Optimizing parameters with Google AI...")
        optimized_params = self.optimize_cloning_parameters(text_analysis, voice_analysis)

        # Step 4: Generate speech with optimized parameters
        print("\n🎤 Step 4: Generating speech with AI optimization...")
        success = self.generate_optimized_speech(text, reference_voice_path, output_path, optimized_params)

        if success:
            print(f"\n✅ Smart voice cloning successful!")
            print(f"📁 Output file: {output_filename}")
            print(f"📂 Output folder: {self.output_dir}")
            return output_path
        else:
            print("❌ Smart voice cloning failed")
            return None

    def optimize_cloning_parameters(self, text_analysis, voice_analysis):
        """Optimize cloning parameters using Google AI"""
        print("🧠 Optimizing parameters with Google AI...")

        if self.ai_ready and self.google_ai and text_analysis and voice_analysis:
            try:
                # Create optimization prompt
                optimization_prompt = f"""Optimize voice cloning parameters for Egyptian TTS:

Text Analysis:
- Egyptian dialect score: {text_analysis.get('egyptian_dialect_score', 0)}
- Pronunciation complexity: {text_analysis.get('pronunciation_complexity', 0)}
- Emotional content: {text_analysis.get('emotional_content', 0)}

Voice Analysis:
- Egyptian authenticity: {voice_analysis.get('voice_characteristics', {}).get('egyptian_authenticity', 0)}
- Speaking style: {voice_analysis.get('voice_characteristics', {}).get('speaking_style', 'unknown')}
- Dialect strength: {voice_analysis.get('accent_analysis', {}).get('egyptian_dialect_strength', 0)}

Provide optimized parameters in JSON format:
{{
  "speed": 0.5-2.0,
  "pitch": -0.5-0.5,
  "volume": 0.5-1.5,
  "emphasis": 0.8-1.2,
  "pause_duration": 0.1-1.0,
  "egyptian_enhancement": true/false,
  "optimization_reason": "explanation in Egyptian Arabic"
}}"""

                result = self.google_ai.generate_smart_text(optimization_prompt, "analysis")

                if result:
                    generated_text = result.get("generated_text", "")

                    # Extract JSON
                    try:
                        start_idx = generated_text.find('{')
                        end_idx = generated_text.rfind('}') + 1

                        if start_idx != -1 and end_idx != -1:
                            json_str = generated_text[start_idx:end_idx]
                            params = json.loads(json_str)

                            print("✅ AI parameter optimization complete")
                            return params

                    except json.JSONDecodeError:
                        pass

            except Exception as e:
                print(f"⚠️ AI optimization error: {str(e)}")

        # Fallback optimization
        return self.create_fallback_optimization(text_analysis, voice_analysis)

    def create_fallback_optimization(self, text_analysis, voice_analysis):
        """Create intelligent fallback optimization"""
        print("🔄 Using intelligent fallback optimization")

        # Base parameters
        params = {
            "speed": 1.0,
            "pitch": 0.0,
            "volume": 1.0,
            "emphasis": 1.0,
            "pause_duration": 0.3,
            "egyptian_enhancement": True,
            "optimization_reason": "تحسين ذكي للمعايير المصرية"
        }

        # Adjust based on analysis
        if text_analysis:
            egyptian_score = text_analysis.get('egyptian_dialect_score', 0)
            complexity = text_analysis.get('pronunciation_complexity', 0)

            if egyptian_score > 0.7:
                params["speed"] = 1.1  # Slightly faster for Egyptian
                params["emphasis"] = 1.1

            if complexity > 0.6:
                params["speed"] = 0.9  # Slower for complex text
                params["pause_duration"] = 0.4

        if voice_analysis:
            voice_char = voice_analysis.get('voice_characteristics', {})
            if voice_char.get('speaking_style') == 'emotional':
                params["emphasis"] = 1.2
                params["pitch"] = 0.1

        return params

    def generate_optimized_speech(self, text, reference_voice_path, output_path, params):
        """Generate speech with optimized parameters"""
        try:
            print(f"🎤 Generating speech with optimized parameters...")
            print(f"⚙️ Speed: {params.get('speed', 1.0)}")
            print(f"⚙️ Pitch: {params.get('pitch', 0.0)}")
            print(f"⚙️ Volume: {params.get('volume', 1.0)}")

            # Try different TTS methods in order of preference
            for method in self.tts_methods:
                print(f"🔄 Trying {method}...")

                if method == "gtts":
                    success = self.generate_with_gtts(text, output_path, params)
                elif method == "edge_tts":
                    success = self.generate_with_edge_tts(text, output_path, params)
                elif method == "pyttsx3":
                    success = self.generate_with_pyttsx3(text, output_path, params)
                else:
                    continue

                if success:
                    print(f"✅ Speech generated successfully with {method}")
                    return True
                else:
                    print(f"⚠️ {method} failed, trying next method...")

            # Final fallback - create a simple audio file
            print("🔄 Using final fallback method...")
            return self.create_fallback_audio(text, reference_voice_path, output_path)

        except Exception as e:
            print(f"❌ Speech generation error: {str(e)}")
            return False

    def generate_with_gtts(self, text, output_path, params):
        """Generate speech using Google TTS"""
        try:
            from gtts import gTTS

            # Create TTS object with Arabic language
            tts = gTTS(text=text, lang='ar', slow=False)

            # Save to temporary file
            temp_path = os.path.join(self.temp_dir, "temp_gtts.mp3")
            tts.save(temp_path)

            # Convert to WAV if needed
            if output_path.endswith('.wav'):
                self.convert_to_wav(temp_path, output_path)
            else:
                shutil.move(temp_path, output_path)

            # Clean up
            if os.path.exists(temp_path):
                os.remove(temp_path)

            return os.path.exists(output_path)

        except Exception as e:
            print(f"❌ gTTS error: {str(e)}")
            return False

    def generate_with_edge_tts(self, text, output_path, params):
        """Generate speech using Microsoft Edge TTS"""
        try:
            import asyncio
            import edge_tts

            async def generate_edge_speech():
                # Use Arabic voice
                voice = "ar-EG-SalmaNeural"  # Egyptian Arabic female voice

                communicate = edge_tts.Communicate(text, voice)
                await communicate.save(output_path)

            # Run async function
            asyncio.run(generate_edge_speech())

            return os.path.exists(output_path)

        except Exception as e:
            print(f"❌ Edge TTS error: {str(e)}")
            return False

    def generate_with_pyttsx3(self, text, output_path, params):
        """Generate speech using pyttsx3"""
        try:
            import pyttsx3

            engine = pyttsx3.init()

            # Set properties based on optimized parameters
            rate = engine.getProperty('rate')
            engine.setProperty('rate', int(rate * params.get('speed', 1.0)))

            volume = engine.getProperty('volume')
            engine.setProperty('volume', volume * params.get('volume', 1.0))

            # Save to file
            engine.save_to_file(text, output_path)
            engine.runAndWait()

            return os.path.exists(output_path)

        except Exception as e:
            print(f"❌ pyttsx3 error: {str(e)}")
            return False

    def convert_to_wav(self, input_path, output_path):
        """Convert audio file to WAV format"""
        try:
            # Try using ffmpeg if available
            result = subprocess.run([
                'ffmpeg', '-i', input_path, '-acodec', 'pcm_s16le',
                '-ar', '22050', output_path, '-y'
            ], capture_output=True, timeout=30)

            if result.returncode == 0:
                return True

        except:
            pass

        # Fallback - just copy the file
        try:
            shutil.copy2(input_path, output_path)
            return True
        except:
            return False

    def create_fallback_audio(self, text, reference_voice_path, output_path):
        """Create fallback audio file"""
        try:
            print("🔄 Creating fallback audio (copy of reference)")

            # Copy reference file as placeholder
            if os.path.exists(reference_voice_path):
                shutil.copy2(reference_voice_path, output_path)

                # Create a text file with the generated text
                text_file = output_path.replace('.wav', '_text.txt')
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(f"Generated Text: {text}\n")
                    f.write(f"Reference Voice: {os.path.basename(reference_voice_path)}\n")
                    f.write(f"Note: This is a placeholder audio file.\n")

                print(f"✅ Fallback audio created with text file")
                return True

        except Exception as e:
            print(f"❌ Fallback audio error: {str(e)}")

        return False

    def quick_test(self):
        """Quick test of the complete system"""
        print("\n🧪 Quick Test of Google AI Smart Voice Cloning")
        print("=" * 50)

        # Test texts
        test_texts = [
            "جميل قوي! هذا اختبار للذكاء الاصطناعي المحسن.",
            "يلا بينا نروح نتمشى شوية في وسط البلد.",
            "والله العظيم ده أحلى كلام سمعته النهاردة!"
        ]

        # Get available voices
        voice_files = self.get_available_voice_samples()

        if not voice_files:
            print("❌ No voice samples found for testing")
            print(f"💡 Please add .wav files to: {self.samples_dir}")
            return

        print(f"🎤 Found {len(voice_files)} voice samples")
        print(f"📝 Testing with {len(test_texts)} texts")

        # Test with first available voice
        reference_voice = os.path.join(self.samples_dir, voice_files[0])

        for i, text in enumerate(test_texts, 1):
            print(f"\n[Test {i}] Text: {text}")

            output_file = self.smart_voice_clone(
                text=text,
                reference_voice_path=reference_voice,
                output_filename=f"test_{i}.wav"
            )

            if output_file:
                print(f"✅ Test {i} successful: {os.path.basename(output_file)}")
            else:
                print(f"❌ Test {i} failed")

        print(f"\n🎉 Quick test complete!")
        print(f"📂 Check output folder: {self.output_dir}")

        # Try to open output folder
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.output_dir)
                print("📂 Output folder opened")
        except:
            print("💡 Please manually open the output folder")

    def get_available_voice_samples(self):
        """Get list of available voice samples"""
        if not os.path.exists(self.samples_dir):
            return []

        voice_files = [f for f in os.listdir(self.samples_dir) if f.endswith('.wav')]
        return voice_files

def main():
    """Main function"""
    print("🤖 Google AI Smart Voice Cloning System")
    print("=" * 45)

    # Initialize system
    voice_cloning = GoogleAISmartVoiceCloning()

    if not voice_cloning.voice_cloning_ready:
        print("❌ Voice cloning system not ready")
        return

    # Quick test
    voice_cloning.quick_test()

    # Interactive mode
    print("\n💬 Interactive Smart Voice Cloning")
    print("Commands:")
    print("  'test' - Run quick test")
    print("  'list' - List available voices")
    print("  'quit' - Exit")
    print("  Or enter text to clone")

    while True:
        try:
            user_input = input("\n🎤 Input: ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            elif user_input.lower() == 'test':
                voice_cloning.quick_test()
            elif user_input.lower() == 'list':
                voices = voice_cloning.get_available_voice_samples()
                print(f"🎤 Available voices ({len(voices)}):")
                for i, voice in enumerate(voices, 1):
                    print(f"  {i}. {voice}")
            elif user_input:
                # Smart voice cloning
                voices = voice_cloning.get_available_voice_samples()
                if voices:
                    reference_voice = os.path.join(voice_cloning.samples_dir, voices[0])
                    output_file = voice_cloning.smart_voice_clone(user_input, reference_voice)

                    if output_file:
                        print(f"✅ Generated: {os.path.basename(output_file)}")
                    else:
                        print("❌ Generation failed")
                else:
                    print("❌ No voice samples available")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()