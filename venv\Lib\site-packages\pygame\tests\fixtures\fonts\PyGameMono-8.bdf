STARTFONT 2.1
FONT -FontForge-PyGameMono-Medium-R-Normal--8-80-75-75-C-80-ISO10646-1
SIZE 8 75 75
FONTBOUNDINGBOX 6 7 0 0
COMMENT "Generated by fontforge, http://fontforge.sourceforge.net"
COMMENT "Created by <PERSON><PERSON>,,, with FontForge 2.0 (http://fontforge.sf.net)"
STARTPROPERTIES 29
FOUNDRY "FontForge"
FAMILY_NAME "PyGameMono"
WEIGHT_NAME "Medium"
SLANT "R"
SETWIDTH_NAME "Normal"
ADD_STYLE_NAME ""
PIXEL_SIZE 8
POINT_SIZE 80
RESOLUTION_X 75
RESOLUTION_Y 75
SPACING "C"
AVERAGE_WIDTH 80
CHARSET_REGISTRY "ISO10646"
CHARSET_ENCODING "1"
FONTNAME_REGISTRY ""
CHARSET_COLLECTIONS "ISO10646-1"
FONT_NAME "PyGameMono"
FACE_NAME "PyGame Mon<PERSON>"
FONT_VERSION "001.000"
FONT_ASCENT 6
FONT_DESCENT 2
UNDERLINE_POSITION -1
UNDERLINE_THICKNESS 1
RAW_ASCENT 800
RAW_DESCENT 200
RELATIVE_WEIGHT 50
RELATIVE_SETWIDTH 50
FIGURE_WIDTH -1
AVG_UPPERCASE_WIDTH 80
ENDPROPERTIES
CHARS 5
STARTCHAR .notdef
ENCODING 0
SWIDTH 1000 0
DWIDTH 8 0
BBX 6 6 0 0
BITMAP
FC
84
84
84
84
FC
ENDCHAR
STARTCHAR A
ENCODING 65
SWIDTH 1000 0
DWIDTH 8 0
BBX 6 7 0 0
BITMAP
78
84
84
FC
84
84
84
ENDCHAR
STARTCHAR B
ENCODING 66
SWIDTH 1000 0
DWIDTH 8 0
BBX 6 6 0 0
BITMAP
FC
44
78
4C
44
FC
ENDCHAR
STARTCHAR C
ENCODING 67
SWIDTH 1000 0
DWIDTH 8 0
BBX 6 6 0 0
BITMAP
78
C4
C0
C0
C4
78
ENDCHAR
STARTCHAR u13079
ENCODING 77945
SWIDTH 1000 0
DWIDTH 8 0
BBX 6 4 0 1
BITMAP
78
B4
B4
78
ENDCHAR
ENDFONT
