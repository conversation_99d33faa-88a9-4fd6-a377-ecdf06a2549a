# XTTS Setup Script for Windows

# Check if Python 3.10 is installed
$python310Path = "$env:LOCALAPPDATA\Programs\Python\Python310\python.exe"
if (-not (Test-Path $python310Path)) {
    Write-Host "Python 3.10 not found. Please run install_python310.ps1 first." -ForegroundColor Red
    exit 1
}

# Create and activate virtual environment with Python 3.10
Write-Host "Creating virtual environment with Python 3.10..." -ForegroundColor Green
& $python310Path -m venv venv
.\venv\Scripts\Activate.ps1

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Green
pip install --upgrade pip
pip install -r requirements.txt

# Create directories for models and samples
Write-Host "Creating project directories..." -ForegroundColor Green
New-Item -ItemType Directory -Force -Path .\models
New-Item -ItemType Directory -Force -Path .\samples
New-Item -ItemType Directory -Force -Path .\output

# Download a sample voice file if none exists
if (-not (Test-Path .\samples\sample_voice.wav)) {
    Write-Host "Downloading a sample voice file..." -ForegroundColor Green
    Invoke-WebRequest -Uri "https://github.com/coqui-ai/TTS/raw/dev/tests/inputs/ljspeech/LJ001-0001.wav" -OutFile ".\samples\sample_voice.wav"
}

# Create a test script
Write-Host "Creating test script..." -ForegroundColor Green
$testScript = @"
from TTS.api import TTS
import time

print("Loading XTTS model...")
start_time = time.time()

# Initialize TTS with XTTS model
tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", gpu=True)

load_time = time.time() - start_time
print(f"Model loaded in {load_time:.2f} seconds")

# List available models
print("\nAvailable models:")
print(tts.list_models())

# List available speakers if using a multi-speaker model
if hasattr(tts, 'speakers') and tts.speakers:
    print("\nAvailable speakers:")
    print(tts.speakers)

# List available languages
print("\nAvailable languages:")
print(tts.languages)

# Generate speech
print("\nGenerating speech...")
start_time = time.time()

tts.tts_to_file(
    text="Hello, this is a test of the XTTS text to speech system running on Windows.",
    speaker_wav="samples/sample_voice.wav",
    language="en",
    file_path="output/test_output.wav"
)

generation_time = time.time() - start_time
print(f"Speech generated in {generation_time:.2f} seconds")
print("Output saved to output/test_output.wav")

# Generate Arabic speech if needed
generate_arabic = input("\nGenerate Arabic speech sample? (y/n): ").lower() == 'y'
if generate_arabic:
    print("Generating Arabic speech...")
    start_time = time.time()
    
    tts.tts_to_file(
        text="مرحبا، هذا اختبار لنظام تحويل النص إلى كلام باستخدام إكس تي تي إس.",
        speaker_wav="samples/sample_voice.wav",
        language="ar",
        file_path="output/arabic_output.wav"
    )
    
    generation_time = time.time() - start_time
    print(f"Arabic speech generated in {generation_time:.2f} seconds")
    print("Output saved to output/arabic_output.wav")
"@

Set-Content -Path .\test_xtts.py -Value $testScript

Write-Host "
Setup complete! To test XTTS:
1. Make sure your virtual environment is activated: .\venv\Scripts\Activate.ps1
2. Run the test script: python test_xtts.py

Note: The first run will download the XTTS model (about 5GB).
" -ForegroundColor Cyan
