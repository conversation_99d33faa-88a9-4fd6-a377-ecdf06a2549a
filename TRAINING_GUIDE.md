# Egyptian XTTS Training Quick Guide

## 🎯 Overview
Train XTTS model specifically on Egyptian Arabic for better accent quality.

## 📋 Requirements
- 1-10 hours of Egyptian speech data
- GPU with 8GB+ VRAM (recommended)
- 16GB+ RAM
- 50GB+ free disk space

## 🚀 Quick Start

### Step 1: Prepare Data
```bash
# Option A: Record new samples
pip install sounddevice soundfile
python record_egyptian_samples.py

# Option B: Use existing audio
# Copy Egyptian audio files to: egyptian_dataset/wavs/
# Update metadata.csv with transcriptions
```

### Step 2: Install Training Dependencies
```bash
pip install trainer librosa soundfile pandas scikit-learn
```

### Step 3: Prepare Dataset
```bash
python prepare_egyptian_data.py
```

### Step 4: Start Training
```bash
python train_egyptian_xtts.py
```

### Step 5: Monitor Training
```bash
tensorboard --logdir egyptian_xtts_training/
```

## 📊 Training Process
- **Duration**: 6-24 hours (depending on data size and hardware)
- **Checkpoints**: Saved every 1000 steps
- **Monitoring**: Use Tensorboard for progress tracking
- **Output**: Trained model in `egyptian_xtts_training/`

## 🎯 Data Quality Tips
1. **Clear audio**: No background noise
2. **Consistent volume**: Normalize all files
3. **Egyptian dialect**: Use authentic Egyptian expressions
4. **Multiple speakers**: Include variety for better generalization
5. **Accurate transcriptions**: Essential for good results

## 🔧 Training Parameters
- **Batch size**: 2 (adjust based on GPU memory)
- **Learning rate**: 5e-06
- **Epochs**: 100
- **Save frequency**: Every 1000 steps

## 🎉 Using Trained Model
After training completes:
1. Copy best checkpoint to your GUI folder
2. Update GUI to use your trained model
3. Enjoy improved Egyptian accent quality!

## ⚠️ Troubleshooting
- **Out of memory**: Reduce batch size
- **Slow training**: Use GPU if available
- **Poor quality**: Check data quality and transcriptions
- **Training stops**: Check logs for errors

## 📞 Support
Check training logs and Tensorboard for detailed progress information.
