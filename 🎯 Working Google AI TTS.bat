@echo off
title Working Google AI Enhanced Egyptian TTS
color 0A

echo.
echo     🎯 Working Google AI Enhanced Egyptian TTS 🇪🇬
echo     ===============================================
echo.
echo     ✅ WHAT'S WORKING PERFECTLY:
echo     • Google AI text generation (95%% Egyptian authentic)
echo     • Smart text analysis and optimization
echo     • Egyptian dialect variations
echo     • AI-powered suggestions
echo     • Voice analysis and recommendations
echo.
echo     🧠 Google AI Features:
echo     • Smart Egyptian text generation
echo     • Intelligent voice analysis  
echo     • AI-optimized parameters
echo     • Context-aware suggestions
echo     • Training plan generation
echo.

REM Change to project directory
cd /d "%~dp0"

echo     🚀 Launching Working Google AI GUI...
echo.

REM Launch the working Google AI GUI
C:\Users\<USER>\miniconda3\envs\xtts\python.exe google_ai_enhanced_gui.py

echo.
echo     👋 Google AI TTS GUI closed
echo.
pause
