#!/usr/bin/env python3
"""
Quick Start Egyptian Training
Minimal training setup for testing
"""

import os
import sys

def quick_train():
    """Quick training with minimal setup"""
    print("🇪🇬 Quick Egyptian Training")
    print("=" * 30)
    
    # Check if data is ready
    if not os.path.exists("egyptian_dataset/wavs/speaker1_001.wav"):
        print("❌ No audio data found")
        print("Run: python setup_egyptian_training.py first")
        return
    
    try:
        from TTS.tts.configs.xtts_config import XttsConfig
        from TTS.tts.models.xtts import Xtts
        from trainer import Trainer, TrainerArgs
        
        print("🔧 Loading minimal config...")
        config = XttsConfig()
        config.load_json("egyptian_dataset/config_minimal.json")
        
        print("🤖 Initializing model...")
        model = Xtts.init_from_config(config)
        
        print("📥 Loading pretrained weights...")
        model.load_checkpoint(config, checkpoint_dir=None, eval=False)
        
        print("🏃 Starting quick training...")
        print("⏱️ This will run for 10 epochs (about 30 minutes)")
        
        trainer_args = TrainerArgs()
        trainer = Trainer(
            trainer_args,
            config,
            output_path=config.output_path,
            model=model
        )
        
        trainer.fit()
        
        print("🎉 Quick training complete!")
        print("Check results in: egyptian_xtts_minimal/")
        
    except ImportError as e:
        print(f"❌ Missing dependency: {str(e)}")
        print("Install with: pip install trainer")
    except Exception as e:
        print(f"❌ Training error: {str(e)}")

if __name__ == "__main__":
    quick_train()
